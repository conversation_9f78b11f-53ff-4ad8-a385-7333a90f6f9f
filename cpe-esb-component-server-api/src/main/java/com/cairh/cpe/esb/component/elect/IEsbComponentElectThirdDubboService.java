package com.cairh.cpe.esb.component.elect;

import com.cairh.cpe.esb.component.elect.dto.req.ElecUploadToThirdByMobileReq;
import com.cairh.cpe.esb.component.elect.dto.req.ElecUploadToThirdReq;
import com.cairh.cpe.esb.component.elect.dto.req.ElectArchiveThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDeleteFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadImageRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectRotateImageRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileByUriRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadImageByUriRequest;
import com.cairh.cpe.esb.component.elect.dto.req.ElectUploadImageRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElecUploadToThirdResp;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadFileResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadImageResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadThirdFileResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectRotateImageResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileByUriResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadFileResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadImageByUriResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectUploadImageResponse;

public interface IEsbComponentElectThirdDubboService {

    /**
     * 文件上传到柜台、新意等第三方系统
     * 档案图片上传base64
     * @param request
     * @return
     */
    ElecUploadToThirdResp uploadToThird(ElecUploadToThirdReq request);

    /**
     * 档案上传到柜台、新意等第三方系统
     * @param request
     */
    void uploadToThirdByMobile(ElecUploadToThirdByMobileReq request);

    /**
     * 从新意等第三方系统下载文件
     * @param request
     * @return
     */
    ElectDownloadThirdFileResponse electDownloadThirdFile(ElectDownloadThirdFileRequest request);

    /**
     * 第三方系统归档文件
     * @param request
     * @return
     */
    void electArchiveThirdFile(ElectArchiveThirdFileRequest request);
}
