package com.cairh.cpe.esb.component.elect.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ElectArchiveThirdFileRequest implements Serializable {
    private static final long serialVersionUID = 225037625439487192L;

    /**
     * T2柜台归档时传入
     */
    private String scantask_id;

    /**
     * 新意归档时传入
     */
    private String app_id;

    /**
     * 新意归档时传入
     */
    private String sync;

    /**
     * 新意归档时传入
     */
    private String busi_serial_no;

    /**
     * 客户号
     */
    private String client_id;

    /**
     * 同步新意时必传，必须为1，2，3，4; 同步T2柜台
     */
    private String id_kind;

    /**
     * 同步新意时必传，证件号码; 同步T2柜台
     */
    private String id_no;

    /**
     * 同步新意时必传，分支机构, 同步T2柜台时传入
     */
    private String branch_no;

    /**
     * 同步新意时传入
     */
    private String organ_flag;

    /**
     * 同步新意时传入
     */
    private String user_name;

    /**
     * 同步新意时传入
     */
    private String c_busi_code;

    /**
     * 同步新意时传入
     */
    private String busi_code;

    /**
     * 同步新意时必传 开户方式  1临柜 2网上 3双人见证 4单人见证
     */
    private String open_type;

    /**
     * 同步新意时传入
     */
    private String source_nos;

    /**
     * 同步新意时传入
     */
    private String hide_flag;

    /**
     * 同步新意时传入
     */
    private String op_user_code;
}
