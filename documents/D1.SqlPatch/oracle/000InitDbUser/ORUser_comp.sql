
declare
  v_rowcount integer;
begin
  select count(*) into v_rowcount from dual where exists(select * from all_users a where a.username = upper('CRH_USER'));
  if v_rowcount = 0 then
 	 execute immediate 'CREATE USER CRH_USER IDENTIFIED BY cairenhui DEFAULT TABLESPACE CRH_USER_DATA TEMPORARY TABLESPACE TEMP';
	 execute immediate 'GRANT CONNECT TO CRH_USER';
     execute immediate 'GRANT RESOURCE TO CRH_USER';
     execute immediate 'GRANT DBA TO CRH_USER';
     execute immediate 'GRANT UNLIMITED TABLESPACE TO CRH_USER';
     execute immediate 'GRANT create any table TO CRH_USER';
     execute immediate 'GRANT select any table TO CRH_USER';
     execute immediate 'GRANT drop any table TO CRH_USER';
     execute immediate 'GRANT create any index TO CRH_USER';
     execute immediate 'GRANT delete any table TO CRH_USER';
     execute immediate 'GRANT insert any table TO CRH_USER';
     execute immediate 'GRANT update any table TO CRH_USER';
     execute immediate 'GRANT create any directory TO CRH_USER';
     execute immediate 'GRANT create any job TO CRH_USER';
     execute immediate 'GRANT select any sequence to CRH_USER';
     execute immediate 'grant select any dictionary to CRH_USER';
  end if;
end;
/
------------------------------
COMMIT;
/
