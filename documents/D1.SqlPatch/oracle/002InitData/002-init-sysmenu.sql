
insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '11', 'K', '组件管理', '组件管理', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 11);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1110', 'KA', '试卷管理', '试卷管理', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1110);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111001', 'KAA', '问卷模板', '问卷模板', ' ', 'backend20/com/#/questionnaire-manage/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111001);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1111', 'KB', '消息管理', '消息管理', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1111);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111101', 'KBA', '消息模板', '消息模板', ' ', 'backend20/com/#/message-template/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111101);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1112', 'KC', '协议管理', '协议管理', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1112);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111201', 'KBA', '协议模板', '协议模板', ' ', 'backend20/com/#/agreement-template/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111201);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111202', 'KBB', '协议规则', '协议规则', ' ', 'backend20/com/#/agreement-rules/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111202);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1113', 'KD', '视频', '视频', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1113);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111301', 'KDA', '视频见证', '视频见证', ' ', 'backend20/com/#/video-manage/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111301);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111302', 'KDB', '视频话术模板', '视频话术模板', ' ', 'backend20/com/#/speechcraft-template/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111302);


insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1114', 'KE', '适当性', '适当性', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1114);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111401', 'KE', '业务-产品适当性', '业务-产品适当性', ' ', 'backend20/com/#/busin-suit/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111401);

insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '1119', 'KZ', '业务配置', '业务配置', ' ', 'NO_URL', ' ', '11', '1', '1', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 1119);
insert into crh_user.sysmenu (crhresource_id , menu_site , menu_caption , menu_name , menu_class_style , action_url , menu_hint , subsys_no , show_flag , leaf_flag , role_rights , use_new_sysmenu , new_action_url )
 select '111901', 'KZA', '系统参数配置', '系统参数配置', ' ', 'backend20/cpe/#/com-config-manage/list', ' ', '11', '1', '3', ' ', '0', ' ' from dual 
 where not exists(select 1 from crh_user.sysmenu where crhresource_id = 111901);
commit;
/
