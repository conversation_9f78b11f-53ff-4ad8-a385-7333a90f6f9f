
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('videorecord');
	if v_count = 0 then
		execute immediate 'create table crh_comp.videorecord (
			serial_id                        varchar2(32)         default '' ''         not null,
			branch_no                        varchar2(20)         default '' ''         not null,
			full_name                        varchar2(120)        default '' ''         not null,
			id_kind                          char(1)              default '' ''         not null,
			id_no                            varchar2(40)         default '' ''         not null,
			client_id                        varchar2(18)         default '' ''         not null,
			fund_account                     varchar2(18)         default '' ''         not null,
			mobile_tel                       varchar2(24)         default '' ''         not null,
			operator_no                      varchar2(18)         default '' ''         not null,
			op_branch_no                     varchar2(20)         default '' ''         not null,
			operator_name                    varchar2(60)         default '' ''         not null,
			busin_type                       number(10,0)         default 0             not null,
			busin_name                       varchar2(64)         default '' ''         not null,
			subsys_no                        number(10,0)         default 0             not null,
			unique_id                        varchar2(255)        default '' ''         not null,
			organ_flag                       char(1)              default '' ''         not null,
			app_id                           varchar2(20)         default '' ''         not null,
			queue_length                     number(10,0)         default 0             not null,
			busin_flag                       varchar2(6)          default '' ''         not null,
			room_id                          varchar2(255)        default '' ''         not null,
			factory_name                     varchar2(255)        default '' ''         not null,
			filerecord_id                    varchar2(32)         default '' ''         not null,
			reject_code                      varchar2(32)         default '' ''         not null,
			reject_info                      varchar2(2000)       default '' ''         not null,
			remark                           varchar2(2000)       default '' ''         not null,
			create_datetime                  Date                 default sysdate       not null,
			tohis_flag                       char(1)              default '' ''         not null,
			tohis_datetime                   Date                 

			) tablespace crh_comp_data';
		execute immediate 'create index crh_comp.idx_videorecord_id_no on crh_comp.videorecord (id_no) tablespace crh_comp_idx';
		execute immediate 'create index crh_comp.idx_videorecord_serial_id on crh_comp.videorecord (serial_id) tablespace crh_comp_idx';
		execute immediate 'create index crh_comp.idx_videorecord_mobile_tel on crh_comp.videorecord (mobile_tel) tablespace crh_comp_idx';
		execute immediate 'create index crh_comp.idx_videorecord_fund_account on crh_comp.videorecord (fund_account) tablespace crh_comp_idx';
	end if;
end;
/
			