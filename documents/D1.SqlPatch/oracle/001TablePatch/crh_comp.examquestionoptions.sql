
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('examquestionoptions');
	if v_count = 0 then
		execute immediate 'create table crh_comp.examquestionoptions (
			serial_id                        varchar2(32)         default '' ''         not null,
			option_no                        number(10,0)         default 0             not null,
			option_content                   varchar2(500)        default '' ''         not null,
			option_need_reason               char(1)              default '' ''         not null,
			exampaper_id                     varchar2(255)        default '' ''         not null,
			score                            number(19,2)         default 0.0           not null,
			examquestion_id                  varchar2(255)        default '' ''         not null,
			relation_value                   char(1)              default '' ''         not null,
			min_risk_level_flag              char(1)              default '' ''         not null,
			order_no                         number(18,0)         default 0             not null,
			modify_by                        varchar2(18)         default '' ''         not null,
			modify_datetime                  Date                 default sysdate       not null,
			create_by                        varchar2(18)         default '' ''         not null,
			create_datetime                  Date                 default sysdate       not null,
			selected_tip                     varchar2(255)        default '' ''         not null,
			tohis_flag                       char(1)              default '' ''         not null,
			tohis_datetime                   Date                 								,
			status                           char(1)              default '' ''         not null

			) tablespace crh_comp_data';
		execute immediate 'create index crh_comp.idx_examquestionoptions on crh_comp.examquestionoptions (exampaper_id) tablespace crh_comp_idx';
	end if;
end;
/
			