
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('csdcdatablobfile');
	if v_count = 0 then
		execute immediate 'create table crh_comp.csdcdatablobfile (
			serial_id                        varchar2(32)         default '' ''         not null,
			file_guid                        varchar2(32)         default '' ''         not null,
			ordinal                          number(18,0)         default 0             not null,
			blob_no                          number(10,0)         default 0             not null,
			deal_flag                        char(1)              default '' ''         not null,
			file_obj                         varchar2(2000)       default '' ''         not null,
			remark                           varchar2(2000)       default '' ''         not null,
			create_datetime                  Date                 default sysdate       not null,
			image_file                       clob                 default '' ''         not null

			) tablespace crh_comp_data';
		execute immediate 'create unique index crh_comp.idx_csdcdatablobfile on crh_comp.csdcdatablobfile (file_guid) tablespace crh_comp_idx';
	end if;
end;
/
			