
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('elecagreemodel');
	if v_count = 0 then
		execute immediate 'create table crh_comp.elecagreemodel (
			serial_id                        varchar2(32)         default '' ''         not null,
			agreement_no                     varchar2(64)         default '' ''         not null,
			agreement_name                   varchar2(255)        default '' ''         not null,
			agreement_version                varchar2(32)         default '' ''         not null,
			ex_name                          varchar2(255)        default '' ''         not null,
			agreement_type                   varchar2(4)          default '' ''         not null,
			agreement_sign_type              varchar2(6)          default '' ''         not null,
			agreement_content                clob                 default '' ''         not null,
			agreement_file_type              char(1)              default '' ''         not null,
			agreement_file_id                varchar2(32)         default '' ''         not null,
			agreement_status                 char(1)              default '' ''         not null,
			encrypt_type                     varchar2(14)         default '' ''         not null,
			encrypt_content                  varchar2(2000)       default '' ''         not null,
			modify_by                        varchar2(18)         default '' ''         not null,
			modify_datetime                  Date                 default sysdate       not null,
			create_by                        varchar2(18)         default '' ''         not null,
			create_datetime                  Date                 default sysdate       not null,
			effective_datetime               Date                 default sysdate       not null,
			invalid_datetime                 Date                 default sysdate       not null,
			seal_pos                         varchar2(500)        default '' ''         not null,
			sign_pos                         varchar2(500)        default '' ''         not null,
			third_source_type                char(1)              default '' ''         not null,
			ex_param                         varchar2(4000)       default '' ''         not null,
			tohis_flag                       char(1)              default '' ''         not null,
			tohis_datetime                   Date                 

			) tablespace crh_comp_data';
		execute immediate 'create index crh_comp.idx_elecagreemodel on crh_comp.elecagreemodel (agreement_type) tablespace crh_comp_idx';
		execute immediate 'create unique index crh_comp.idx_elecagreemodel_id on crh_comp.elecagreemodel (serial_id) tablespace crh_comp_idx';
	end if;
end;
/
			