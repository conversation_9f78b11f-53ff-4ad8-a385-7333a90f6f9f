
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('noticemodel');
	if v_count = 0 then
		execute immediate 'create table crh_comp.noticemodel (
			serial_id                        varchar2(32)         default '' ''         not null,
			model_no                         number(10,0)         default 0             not null,
			model_name                       varchar2(128)        default '' ''         not null,
			model_title                      varchar2(100)        default '' ''         not null,
			model_content                    varchar2(2000)       default '' ''         not null,
			status                           char(1)              default '' ''         not null,
			modify_by                        varchar2(18)         default '' ''         not null,
			modify_datetime                  Date                 default sysdate       not null,
			create_by                        varchar2(18)         default '' ''         not null,
			create_datetime                  Date                 default sysdate       not null

			) tablespace crh_comp_data';
		execute immediate 'create unique index crh_comp.idx_noticemodel on crh_comp.noticemodel (model_no) tablespace crh_comp_idx';
	end if;
end;
/
			