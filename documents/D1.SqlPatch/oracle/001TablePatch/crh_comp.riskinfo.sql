
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('riskinfo');
	if v_count = 0 then
		execute immediate 'create table crh_comp.riskinfo (
			serial_id                        varchar2(32)         default '' ''         not null,
			examtestresult_id                varchar2(32)         default '' ''         not null,
			corp_risk_level                  number(10,0)         default 0             not null,
			en_invest_term                   varchar2(64)         default '' ''         not null,
			en_invest_kind                   varchar2(64)         default '' ''         not null,
			cooling_period                   number(10,0)         default 0             not null,
			tohis_flag                       char(1)              default '' ''         not null,
			tohis_datetime                   Date                 

			) tablespace crh_comp_data';
		execute immediate 'create unique index crh_comp.idx_riskinfo on crh_comp.riskinfo (examtestresult_id) tablespace crh_comp_idx';
	end if;
end;
/
			