
declare
	v_count number;
begin
	select count(*) into v_count from all_tables t where t.owner = upper('crh_comp') and t.table_name = upper('prodsuitinfojour');
	if v_count = 0 then
		execute immediate 'create table crh_comp.prodsuitinfojour (
			serial_id                        varchar2(32)         default '' ''         not null,
			prodsuitinfo_id                  varchar2(32)         default '' ''         not null,
			prod_code                        varchar2(32)         default '' ''         not null,
			prodta_no                        varchar2(24)         default '' ''         not null,
			prod_name                        varchar2(255)        default '' ''         not null,
			risk_level                       number(10,0)         default 0             not null,
			invest_kind                      char(1)              default '' ''         not null,
			invest_term                      char(1)              default '' ''         not null,
			max_deficit_rate                 number(9,8)          default 0.0           not null,
			income_type                      varchar2(2)          default '' ''         not null,
			prodpre_income                   number(19,2)         default 0.0           not null

			) tablespace crh_comp_data';
		
	end if;
end;
/
			