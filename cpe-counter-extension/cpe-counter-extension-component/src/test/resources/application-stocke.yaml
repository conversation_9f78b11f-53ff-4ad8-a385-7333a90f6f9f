cpe:
  service:
    component-scans:
      - base-packages: com.cairh.cpe.http.data.component.stocke.**
        service-type: http
        template-id: face-stocke
    http:
      - id: face-stocke
        template-class-name: com.cairh.cpe.counter.http.template.component.stocke.ComponentStockeHttpTemplate
        desc: face-stocke
        debug: true
        mock: true
        trace: true
        config:
          url: http://10.52.198.104:7766/faceid
          appid: xxxxxxxappid
          headers:
            "[*.appid]": xxxxxxxxappid
            "[*.appkey]": xxxxxxxxappkey
            "[*.appsecret]": xxxxxxxxappsecret