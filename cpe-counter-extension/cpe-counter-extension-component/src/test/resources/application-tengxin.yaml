cpe:
  protocol-gateway:
    service:
      ComponentTengxinHttp:
        url: http://api.sms1086.com/
        username: chuangyuan
        password: 123456
        signmark: 创元证券
        f: 1
#cpe:
#  service:
#    component-scans:
#      - base-packages: com.cairh.cpe.http.data.component.tengxin.**
#        service-type: http
#        template-id: component-tengxin
#    http:
#      - id: component-tengxin
#        template-class-name: com.cairh.cpe.counter.http.template.component.tengxin.ComponentTengxinHttpTemplate
#        desc: component-tengxin
#        debug: false
#        mock: true
#        trace: true
#        config:
#          url: http://api.sms1086.com/
#          username: chuangyuan
#          password: 123456
#          signmark: 创元证券
#          f: 1