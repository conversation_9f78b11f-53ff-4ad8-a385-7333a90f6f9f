package com.cairh.cpe.counter.service.yiqian;


import com.cairh.cpe.http.data.component.yiqian.ComponentYiQianHttpService;
import com.cairh.cpe.http.data.component.yiqian.req.YiQianFirstStepForGatPassRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = YiQianHttpServiceTest.ApplicationTest.class)
@ActiveProfiles(profiles = {"yiqian"})
public class YiQianHttpServiceTest {
    @Autowired
    private ComponentYiQianHttpService componentYiQianHttpService;

    @Test
    public void test1() throws Exception {
        YiQianFirstStepForGatPassRequest request = new YiQianFirstStepForGatPassRequest();
        request.setAppname("");
        request.setAuthMode("");
        request.setTransid("");
        String s = componentYiQianHttpService.yiQianFirstStepForGatPass(request);

    }
    @SpringBootApplication(scanBasePackages = "com.cairh.cpe")
    public static class ApplicationTest {}
}
