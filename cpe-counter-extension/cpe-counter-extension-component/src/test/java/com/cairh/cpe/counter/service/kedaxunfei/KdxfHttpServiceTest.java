package com.cairh.cpe.counter.service.kedaxunfei;


import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;

/**
 * kdxf test
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = KdxfHttpServiceTest.ApplicationTest.class)
@ActiveProfiles(profiles = {"kdxf"})
class KdxfHttpServiceTest {

//    @Autowired
//    private ComponentKdxfHttpService httpService;


    @Test
    public void testAsr() {
//        FileSystemResource resource = new FileSystemResource("D:/tmp/7b4ed98a59cf46c9aacc1bade962d847.wav");
//        String result = httpService.asr(new KdxfAsrRequest().setAudioFile(resource));
//        Assertions.assertNotNull(result);
    }

    @Test
    public void testTts() {
//        String content = "乌鹊南飞";
//        UstcWsCommonRequest apiRequest = new UstcWsCommonRequest();
//        UstcWsCommonRequest.Payloads payloads = new UstcWsCommonRequest.Payloads();
//        payloads.setData(content);
//        apiRequest.setPayloads(Arrays.asList(payloads));
//        UstcWsCommonRequest.Params params = new UstcWsCommonRequest.Params();
//        params.setEngine_vid("62420");
//        apiRequest.setParams(params);
//        String apiResponse = httpService.tts(apiRequest);
//        System.out.println(apiResponse);
    }

    @SpringBootApplication(scanBasePackages = "com.cairh.cpe")
    public static class ApplicationTest {

    }
}