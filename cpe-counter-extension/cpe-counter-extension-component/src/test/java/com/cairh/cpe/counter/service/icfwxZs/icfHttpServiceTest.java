package com.cairh.cpe.counter.service.icfwxZs;


import com.cairh.cpe.http.data.component.icfwxZs.ComponentIcfwxZsService;
import com.cairh.cpe.http.data.component.icfwxZs.req.IcfwxZsSendMsgRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

@SpringBootTest(classes = icfHttpServiceTest.ApplicationTest.class)
@ActiveProfiles(profiles = {"icfwxZs"})
class icfHttpServiceTest {

    @Autowired
    private ComponentIcfwxZsService smsicfHttpService;

    @Test
    public void testIcf(){
        Map<String,String> result = smsicfHttpService.sendMsg(new IcfwxZsSendMsgRequest().setMobile("17362497809").setContent("sendInfos"));
        System.out.println(result);
    }

    @SpringBootApplication(scanBasePackages = "com.cairh.cpe")
    public static class ApplicationTest {
    }
}
