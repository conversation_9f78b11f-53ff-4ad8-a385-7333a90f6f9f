package com.cairh.cpe.counter.service.mw;

import com.cairh.cpe.counter.service.mw.MwHttpServiceTest.ApplicationTest;
import com.cairh.cpe.http.data.component.mengwang.ComponentMengwangHttpService;
import com.cairh.cpe.http.data.component.mengwang.req.MwNoticeRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * zt test
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = ApplicationTest.class)
@ActiveProfiles(profiles = {"mw"})
class MwHttpServiceTest {

    @Autowired
    private ComponentMengwangHttpService componentNoticeMwHttpService;

    @Test
    public void testMwNotice() throws Exception {
        MwNoticeRequest mwNoticeRequest = new MwNoticeRequest();
        mwNoticeRequest.setContent("1111");
        mwNoticeRequest.setMobile("13125109951");
        mwNoticeRequest.setBranch_no("111");
        String result = componentNoticeMwHttpService.sendNotice(mwNoticeRequest);
        Assertions.assertNotNull(result);
    }

    @SpringBootApplication(scanBasePackages = "com.cairh.cpe")
    public static class ApplicationTest {

    }
}