package com.cairh.cpe.http.data.component.xzt.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class XztIdCardCheckRequest {


    private String clientID = "0";

    private List<FaceImage> params;

    @Data
    @Accessors(chain = true)
    public static final class FaceImage {

        private String id = "0";

        private String type = "jpg";

        private String data;
    }
}
