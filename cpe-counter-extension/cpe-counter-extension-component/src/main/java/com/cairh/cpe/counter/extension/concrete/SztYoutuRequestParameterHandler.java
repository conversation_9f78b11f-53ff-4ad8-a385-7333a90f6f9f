//package com.cairh.cpe.counter.extension.concrete;
//
//import com.cairh.cpe.counter.core.request.DefaultRequestParameterHandler;
//import com.cairh.cpe.counter.extension.AbstractGenericRequestParameterHandler;
//import com.cairh.cpe.http.data.component.sztyoutu.ComponentSztyoutuHttpService;
//import org.springframework.stereotype.Component;
//
///**
// * {@link DefaultRequestParameterHandler} for szt youtu all-in-one
// * fixme 挪到了cpe-base-protocol-gateway中
// * <AUTHOR>
// */
//@Component
//public class SztYoutuRequestParameterHandler extends AbstractGenericRequestParameterHandler<ComponentSztyoutuHttpService> {
//
//    public SztYoutuRequestParameterHandler(ComponentSztyoutuHttpService apiServiceType) {
//        super(apiServiceType);
//    }
//}
