//package com.cairh.cpe.ws.data.component.esb;
//
//import com.cairh.cpe.protocol.gateway.annotation.APIMapping;
//import com.cairh.cpe.protocol.gateway.annotation.APIService;
//import com.cairh.cpe.protocol.gateway.constant.TemplateType;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.req.Ws290002Req;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.resp.Ws290002Resp;
//
///**
// * esb通信接口
// *
// * <AUTHOR>
// * @since 2022-10-10
// */
//@APIService(TemplateType.ESB_WS)
//public interface ComponentEsbWsService {
//
//    @APIMapping(value = "interfaceCode = 290002", note = "实时短信发送")
//    Ws290002Resp ws290002(Ws290002Req request);
//}
