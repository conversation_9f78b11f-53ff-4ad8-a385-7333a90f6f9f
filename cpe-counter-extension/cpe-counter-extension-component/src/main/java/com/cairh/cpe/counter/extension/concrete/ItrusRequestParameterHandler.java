//package com.cairh.cpe.counter.extension.concrete;
//
//import com.cairh.cpe.counter.extension.AbstractGenericRequestParameterHandler;
//import com.cairh.cpe.counter.http.request.AbstractHttpRequestEntity;
//import com.cairh.cpe.counter.http.template.component.itrus.ComponentItrusMapping;
//import com.cairh.cpe.counter.support.util.TemplatePropertiesConfigUtil;
//import com.cairh.cpe.http.data.component.itrus.ComponentItrusHttpService;
//import com.cairh.cpe.http.data.component.itrus.cloud.req.ItrusCloudCreateContractRequest;
//import com.cairh.cpe.http.data.component.itrus.cloud.req.ItrusCloudCreateUserRequest;
//import com.cairh.cpe.http.data.component.itrus.cloud.req.ItrusCloudDownloadContractRequest;
//import com.cairh.cpe.http.data.component.itrus.cloud.req.ItrusCloudSignRequest;
//import com.cairh.cpe.http.data.component.shine.req.ShiNeSubmitProcScanInfoRequest;
//import com.cairh.cpe.http.data.component.shine.req.ShiNeTokenRequest;
//import org.springframework.stereotype.Component;
//
//@Component
//public class ItrusRequestParameterHandler extends AbstractGenericRequestParameterHandler<ComponentItrusHttpService> {
//
//    private static final String APIID = "apiId";
//
//    public ItrusRequestParameterHandler(ComponentItrusHttpService apiServiceType) {
//        super(apiServiceType);
//    }
//
//    @Override
//    protected void setExtensibleValues(AbstractHttpRequestEntity requestEntity) {
//
//        String apiId = TemplatePropertiesConfigUtil.getRequiredString(APIID);
//        ComponentItrusMapping mapping = requestEntity.getMethodAnnotation(ComponentItrusMapping.class);
//        if (mapping == null) {
//            return;
//        }
//        Object targetArgument = requestEntity.getArguments()[0];
//        if (targetArgument instanceof ItrusCloudCreateUserRequest) {
//            ItrusCloudCreateUserRequest request = (ItrusCloudCreateUserRequest) targetArgument;
//            request.setApiId(apiId);
//        } else if (targetArgument instanceof ItrusCloudCreateContractRequest) {
//            ItrusCloudCreateContractRequest request = (ItrusCloudCreateContractRequest) targetArgument;
//            request.setApiId(apiId);
//        } else if (targetArgument instanceof ItrusCloudDownloadContractRequest) {
//            ItrusCloudDownloadContractRequest request = (ItrusCloudDownloadContractRequest) targetArgument;
//            request.setApiId(apiId);
//        } else if (targetArgument instanceof ItrusCloudSignRequest) {
//            ItrusCloudSignRequest request = (ItrusCloudSignRequest) targetArgument;
//            request.setApiId(apiId);
//        }
//    }
//}