package com.cairh.cpe.http.data.component.gtja.req;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class GtjaSendMsgParam  {

    /**
     * 方法名
     */
    private String method;

    private GtjaSendMsgEntity param;

    @Data
    public static class GtjaSendMsgEntity{

        /**
         * 时间
         */
        private String time;

        /**
         * 签名
         */
        private String sign;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 验证码
         */
        private String verifyCode;
    }

}