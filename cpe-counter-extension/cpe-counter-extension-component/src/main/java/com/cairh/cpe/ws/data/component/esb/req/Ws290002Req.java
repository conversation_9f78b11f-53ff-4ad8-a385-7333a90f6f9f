//package com.cairh.cpe.protocol.gateway.request.htsec.esb.req;
//
//
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.EsbRequestXmlBean;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.Header;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.MessageRequestHead;
//import lombok.Data;
//
//import javax.xml.bind.annotation.XmlAccessType;
//import javax.xml.bind.annotation.XmlAccessorType;
//import javax.xml.bind.annotation.XmlRootElement;
//import javax.xml.bind.annotation.XmlSeeAlso;
//
///**
// * esb实时短信发送入参
// * <AUTHOR>
// * @since 2022-10-10
// */
//@Data
//@XmlAccessorType(XmlAccessType.FIELD)
//@XmlRootElement(name = "Envelope", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
//@XmlSeeAlso({
//        EsbRequestXmlBean.class, Header.class, MessageRequestHead.class, EsbRequestXmlBean.Body.class, EsbRequestXmlBean.Request.class,
//        Ws290002Req.MessageRequestBody.class})
//public class Ws290002Req extends EsbRequestXmlBean<Header, MessageRequestHead, Ws290002Req.MessageRequestBody> {
//
//    @Data
//    public static class MessageRequestBody {
//        /**
//         * 服务请求方用户名，用于身份校验（ESB分配）
//         */
//        private String userName;
//
//        /**
//         * 服务请求方密码，用于身份校验（ESB分配）
//         */
//        private String passWord;
//
//        /**
//         * 所属营业部
//         */
//        private String subBranch;
//
//        /**
//         * 产品编号(通道组ID)
//         */
//        private Integer productID;
//
//        /**
//         * 唯一序列号 6位唯一序列号，用于标示短信批次
//         */
//        private Long clientSeq;
//
//        /**
//         * 手机号
//         */
//        private String phone;
//
//        /**
//         * 短信内容
//         */
//        private String content;
//
//        /**
//         * 手机号数量  默认1
//         */
//        private String phoneCount;
//    }
//
//
//}