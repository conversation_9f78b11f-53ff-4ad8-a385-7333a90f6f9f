//package com.cairh.cpe.http.data.component.xzt;
//
//import com.cairh.cpe.counter.core.context.APIServiceContext;
//import com.cairh.cpe.counter.http.request.AbstractHttpRequestEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Component;
//
//import java.nio.charset.StandardCharsets;
//import java.security.MessageDigest;
//import java.util.Map;
//
//@Component
//public class XztHeaderHelper {
//
//	private static MessageDigest md5 = null;
//
//	static {
//		try {
//			md5 = MessageDigest.getInstance("MD5");
//		} catch (Exception e) {
//			System.out.println("md5 获取 error");
//		}
//	}
//
//	public String signature(HttpHeaders headers, AbstractHttpRequestEntity request){
//		Map<String,Object> config = APIServiceContext.getTemplateProperties().getConfig();
//		String appId = (String) config.get("midUserId");
//		String appSecret = (String) config.get("salt");
//		Long timestamp = Long.parseLong(headers.get("timestamp").get(0));
//		String sign = getMd5(appId + ":" + appSecret + ":" +timestamp);
//		return sign;
//	}
//
//	public Long timestamp(HttpHeaders headers, AbstractHttpRequestEntity request){
//		return System.currentTimeMillis();
//	}
//
//	public static synchronized String getMd5(String str) {
//		byte[] bs = md5.digest(str.getBytes(StandardCharsets.UTF_8));
//		StringBuilder sb = new StringBuilder(40);
//		for (byte x : bs) {
//			if ((x & 0xff) >> 4 == 0) {
//				sb.append("0").append(Integer.toHexString(x & 0xff));
//			} else {
//				sb.append(Integer.toHexString(x & 0xff));
//			}
//		}
//		return sb.toString();
//	}
//}
