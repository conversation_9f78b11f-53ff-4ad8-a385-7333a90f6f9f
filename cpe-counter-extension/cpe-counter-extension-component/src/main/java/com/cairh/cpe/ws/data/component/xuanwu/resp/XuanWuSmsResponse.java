package com.cairh.cpe.ws.data.component.xuanwu.resp;

import com.cairh.cpe.protocol.gateway.ws.template.xuanwu.xml.XuanWuResponseXmlBean;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Envelope", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
@XmlSeeAlso({
        XuanWuResponseXmlBean.class,XuanWuResponseXmlBean.Body.class, XuanWuResponseXmlBean.PostResponse.class
        , XuanWuResponseXmlBean.PostResult.class})
public class XuanWuSmsResponse extends XuanWuResponseXmlBean {
}
