package com.cairh.cpe.http.data.component.hehe.resp;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class HeheBankCardResponse {
    private Integer error_code;
    private String error_msg;
    private String type;//银行卡类型:借记卡
    private String card_number;
    private String validate;
    private String holder_name;//持卡人
    private String issuer;//发卡机构:建设银行
    private String card_number_image;//卡号区域截图
}
