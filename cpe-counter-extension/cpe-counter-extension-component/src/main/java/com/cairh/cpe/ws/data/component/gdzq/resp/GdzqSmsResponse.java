//package com.cairh.cpe.ws.data.component.gdzq.resp;
//
//import com.cairh.cpe.protocol.gateway.ws.template.gdzq.xml.GdSmsResponseHead;
//import com.cairh.cpe.protocol.gateway.ws.template.gdzq.xml.GdzqResponseXmlBean;
//import com.cairh.cpe.protocol.gateway.ws.template.gdzq.xml.Header;
//import lombok.Getter;
//import lombok.Setter;
//
//import javax.xml.bind.annotation.XmlAccessType;
//import javax.xml.bind.annotation.XmlAccessorType;
//import javax.xml.bind.annotation.XmlRootElement;
//import javax.xml.bind.annotation.XmlSeeAlso;
//
///**
// * <AUTHOR>
// * @since 1.0.0
// */
//@Getter
//@Setter
//@XmlAccessorType(XmlAccessType.FIELD)
//@XmlRootElement(name = "Envelope", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
//@XmlSeeAlso({
//        GdzqResponseXmlBean.class, Header.class, GdSmsResponseHead.class, GdzqResponseXmlBean.Body.class,
//        GdzqSmsResponse.GdSmsResponseBody.class})
//public class GdzqSmsResponse extends GdzqResponseXmlBean<Header, GdSmsResponseHead, GdzqSmsResponse.GdSmsResponseBody> {
//
//  @Getter
//  @Setter
//  public static class GdSmsResponseBody {
//
//    private String srcSeqeunceId;
//
//    private String status;
//  }
//}
