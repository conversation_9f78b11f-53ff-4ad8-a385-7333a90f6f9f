package com.cairh.cpe.t2.data.component;

import com.cairh.cpe.protocol.gateway.annotation.APIMapping;
import com.cairh.cpe.protocol.gateway.annotation.APIService;
import com.cairh.cpe.protocol.gateway.constant.TemplateType;
import com.cairh.cpe.t2.data.component.dto.req.T2_331483_Request;
import com.cairh.cpe.t2.data.component.dto.resp.T2_331483_Response;
import com.cairh.cpe.t2.data.constant.HsT2Function;

/**
 * t2
 *
 * <AUTHOR>
 */
@APIService(TemplateType.COMMON_T2)
public interface ComponentT2Service {

    @APIMapping(value = HsT2Function.FUNC_MOBILE_VERIFY, note = "中登手机实名认证")
    T2_331483_Response T2_331483(T2_331483_Request request);

}
