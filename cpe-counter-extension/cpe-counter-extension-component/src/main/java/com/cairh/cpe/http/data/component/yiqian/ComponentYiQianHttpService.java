package com.cairh.cpe.http.data.component.yiqian;

import com.cairh.cpe.http.data.component.yiqian.req.*;
import com.cairh.cpe.protocol.gateway.annotation.APIService;
import com.cairh.cpe.protocol.gateway.annotation.HttpMapping;
import com.cairh.cpe.protocol.gateway.constant.TemplateType;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

@APIService(TemplateType.COMPONENT_YIQIAN_HTTP)
public interface ComponentYiQianHttpService {
    /**
     * 一步式身份证/居住证
     */
    @HttpMapping(method= HttpMethod.POST,value = "/front-serv/auth.count.do",note = "nciis-yiqian-gatresidence",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String yiQianVerifyForGatResidence(YiQianVerifyForGatResdenceRequest request);

    /**
     * 一步式通行证
     */
    @HttpMapping(method= HttpMethod.POST,value = "/foreign/apply",note = "nciis-yiqian-gatpass",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String yiQianVerifyForGatPass(YiQianVerifyForGatPassRequest request);

    /**
     * 两步式首次请求居住证/身份证
     */
    @HttpMapping(method= HttpMethod.POST,value = "/front-serv/auth.first.do",note = "nciis-yiqian-firststep-gatresidence",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String yiQianFirstStepForGatResidence(YiQianFirstStepForGatResidenceRequest request);

    /**
     * 两步式首次请求通行证
     */
    @HttpMapping(method= HttpMethod.POST,value = "/foreign/step1",note = "nciis-yiqian-firststep-gatpass",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String yiQianFirstStepForGatPass(YiQianFirstStepForGatPassRequest request);

    /**
     * 两步式二次请求居住证/身份证
     */
    @HttpMapping(method = HttpMethod.POST,value= "/front-serv/auth.second.do",note="nciis-yiqian-secondstep-gatresidence",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String yiQianSecondStepForGatResdence(YiQianSecondStepForGatResdenceRequest request);

    /**
     * 两步式二次请求通行证
     */
    @HttpMapping(method = HttpMethod.POST,value = "/foreign/step2",note = "nciis-yiqian-secondstep-gatpass",contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String yiQianSecondStepForGatPass(YiQianSecondStepForGatPassRequest request);
}