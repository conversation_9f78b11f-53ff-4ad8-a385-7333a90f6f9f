//package com.cairh.cpe.counter.extension.concrete;
//
//import cn.hutool.core.util.ArrayUtil;
//import com.cairh.cpe.counter.core.request.DefaultRequestParameterHandler;
//import com.cairh.cpe.counter.extension.AbstractGenericRequestParameterHandler;
//import com.cairh.cpe.counter.http.request.AbstractHttpRequestEntity;
//import com.cairh.cpe.counter.http.template.component.ect888.ComponentEct888Mapping;
//import com.cairh.cpe.counter.support.util.Coder;
//import com.cairh.cpe.counter.support.util.SignatureUtil;
//import com.cairh.cpe.counter.support.util.TemplatePropertiesConfigUtil;
//import com.cairh.cpe.http.data.component.ect888.ComponentEct888HttpService;
//import com.cairh.cpe.http.data.component.ect888.req.*;
//import com.thinkive.base.util.Base64;
//import com.thinkive.base.util.security.AES;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.net.URLEncoder;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.TreeMap;
//
///**
// * {@link DefaultRequestParameterHandler} for ect888 all-in-one
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class Ect888RequestParameterHandler extends AbstractGenericRequestParameterHandler<ComponentEct888HttpService> {
//
//    private static final String PUBLIC_KEY = "publicKey";// 公钥
//    private static final String PTYACCT = "ptyacct";// 机构账号
//    private static final String PTYCD = "ptycd";// 机构号
//    private static final String PTYKEY = "ptyKey";// 会话密钥
//    private static final String PTYPWD = "ptypwd";// 旧密码
//    private static final String NEWPTYPWD = "newptypwd";// 新密码
//    private static final String SYSSEQNB = "sysseqnb";// 获取结果流水号
//
//    public Ect888RequestParameterHandler(ComponentEct888HttpService apiServiceType) {
//        super(apiServiceType);
//    }
//
//
//    @Override
//    protected void setExtensibleValues(AbstractHttpRequestEntity requestEntity) {
//        ComponentEct888Mapping mapping = requestEntity.getMethodAnnotation(ComponentEct888Mapping.class);
//        if (mapping == null) {
//            return;
//        }
//
//        if (argumentTypeOfSpeechRecognition(requestEntity)) {
//            log.info("ect888异步语音请求,跳过extensibleValues阶段处理");
//            return;
//        }
//
//        Object targetArgument = requestEntity.getArguments()[0];
//
//        String ptyacct = TemplatePropertiesConfigUtil.getRequiredString(PTYACCT);
//        String ptyKey = TemplatePropertiesConfigUtil.getRequiredString(PTYKEY);
//        String ptypwd = TemplatePropertiesConfigUtil.getRequiredString(PTYPWD);
//        String newptypwd = TemplatePropertiesConfigUtil.getRequiredString(NEWPTYPWD);
//        String publicKey = TemplatePropertiesConfigUtil.getRequiredString(PUBLIC_KEY);
//        String ptycd = TemplatePropertiesConfigUtil.getRequiredString(PTYCD);
//        log.info("证通配置=ptyacct:{},ptyKey:{},ptypwd:{},newptypwd:{},publicKey:{},ptycd:{}，targetArgument类型:{}"
//        ,ptyacct, ptyKey, ptypwd, newptypwd, publicKey, ptycd, targetArgument.getClass().getName());
//
//        if (targetArgument instanceof Ect888FaceRecognitionRequest) {
//            Ect888FaceRecognitionRequest request = (Ect888FaceRecognitionRequest) targetArgument;
//            request.setTimestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//            TreeMap<String, Object> params = new TreeMap<>();
//            AES aes = new AES(ptyKey);
//            params.put("certseq", aes.encrypt(request.getCertseq(), "utf-8"));// 证件号码
//            params.put(PTYACCT, ptyacct);// 机构帐号
//            params.put(PTYCD, ptycd);// 机构号
//            params.put("sourcechnl", "0");// 来源渠道，pc端传0
//            params.put("placeid", request.getPlaceid());// 业务发生地
//            params.put("biztyp", request.getBiztyp());// 对照接口文档查看
//            params.put("biztypdesc", request.getBiztypdesc());// 服务描述
//            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//            params.put("timestamp", timestamp);// 时间
//            String sign = SignatureUtil.signature(params, ptyKey);
//            request.setSign(sign);
//            try {
//                request.setCertseq(Base64.encodeBytes((URLEncoder.encode(aes.encrypt(request.getCertseq(), "utf-8"), "utf-8")).getBytes(), Base64.DONT_BREAK_LINES));
//            } catch (Exception e) {
//                log.error("填充证件号码错误");
//            }
//        } else if (targetArgument instanceof Ect888GetResultRequest) {
//            Ect888GetResultRequest request = (Ect888GetResultRequest) targetArgument;
//            TreeMap<String, Object> params = new TreeMap<>();
//            params.put(PTYACCT, ptyacct);
//            params.put(SYSSEQNB, request.getSysseqnb());
//            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//            params.put("timestamp", timestamp);// 时间
//            String sign = SignatureUtil.signature(params, ptyKey);
//            request.setSign(sign);
//        } else if (targetArgument instanceof Ect888ModPwdRequest) {
//            Ect888ModPwdRequest request = (Ect888ModPwdRequest) targetArgument;
//            String passworddata = "ptyacct=" + ptyacct + "&ptycd=" + ptycd + "&newptypwd=" + newptypwd + "&ptypwd=" + ptypwd;
//            // 公钥加密且做对应的base64，URLEncoder处理
//            passworddata = getStrByPublic(publicKey, passworddata);
//            // 组装参数调用接口
//            request.setReqdata(passworddata);
//        } else if (targetArgument instanceof Ect888ModSessionSecretRequest) {
//            Ect888ModSessionSecretRequest request = (Ect888ModSessionSecretRequest) targetArgument;
//            String data = "ptyacct=" + ptyacct + "&ptycd=" + ptycd + "&ptypwd=" + newptypwd + "&encrykey=" + ptyKey;
//            request.setReqdata(getStrByPublic(publicKey, data));
//        }
//    }
//
//    private boolean argumentTypeOfSpeechRecognition(AbstractHttpRequestEntity requestEntity) {
//        if ((requestEntity.getArguments()[0] instanceof Ect888SpeechRecognitionSponsorRequest)
//                || (requestEntity.getArguments()[0] instanceof ZhengTongIdCardRequest)
//                || (ArrayUtil.length(requestEntity.getArguments()) > 1 && requestEntity.getArguments()[1] instanceof Ect888SpeechRecognitionResultQueryParam)) {
//            return true;
//        }
//
//        return false;
//    }
//
//    public String getStrByPublic(String publickey, String data) {
//        String reData = "";
//        byte[] encryData;
//        try {
//            encryData = Coder.encryptByPublicKey(data.getBytes(), publickey);
//            reData = URLEncoder.encode(com.thinkive.base.util.Base64.encodeBytes(encryData, com.thinkive.base.util.Base64.DONT_BREAK_LINES), "utf-8");
//        } catch (Exception e) {
//            log.error("公钥加密且做对应的base64，URLEncoder处理失败", e);
//        }
//        return com.thinkive.base.util.Base64.encodeBytes(reData.getBytes(), Base64.DONT_BREAK_LINES);
//    }
//}
