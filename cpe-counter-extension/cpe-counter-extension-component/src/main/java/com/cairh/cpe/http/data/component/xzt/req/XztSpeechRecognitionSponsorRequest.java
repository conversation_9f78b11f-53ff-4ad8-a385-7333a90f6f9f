package com.cairh.cpe.http.data.component.xzt.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class XztSpeechRecognitionSponsorRequest {

    private App app;
    private Audio audio;
    private User user;

    @Data
    @Accessors(chain = true)
    public static final class App {

        private String appid;

        private String token;

        private String cluster;
    }

    @Data
    @Accessors(chain = true)
    public static final class User {

        private String uid;
    }

    @Data
    @Accessors(chain = true)
    public static final class Audio {

        private String url;

        private String format;
    }
}
