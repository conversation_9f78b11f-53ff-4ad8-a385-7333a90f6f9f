package com.cairh.cpe.http.data.component.ect888.req;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * zt queryParam
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Ect888AsrQueryParam {

    private String appkey;

    private String format;

    private Integer sample_rate;

    private Boolean enable_punctuation_prediction;

    private Boolean enable_inverse_text_normalization;

    private Boolean enable_voice_detection;
}
