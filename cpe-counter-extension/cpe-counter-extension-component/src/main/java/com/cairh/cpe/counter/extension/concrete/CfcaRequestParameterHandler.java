//package com.cairh.cpe.counter.extension.concrete;
//
//import com.cairh.cpe.counter.extension.AbstractGenericRequestParameterHandler;
//import com.cairh.cpe.http.data.component.cfca.ComponentCfcaHttpService;
//import org.springframework.stereotype.Component;
//
//@Component
//public class CfcaRequestParameterHandler extends AbstractGenericRequestParameterHandler<ComponentCfcaHttpService> {
//
//    public CfcaRequestParameterHandler(ComponentCfcaHttpService apiServiceType) {
//        super(apiServiceType);
//    }
//}
//
//
//
