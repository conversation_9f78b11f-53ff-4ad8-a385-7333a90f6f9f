package com.cairh.cpe.http.data.component.gdzqseal.req;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.core.io.Resource;

/**
 * gdzq在pdf上加盖印章入参
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@Accessors(chain = true)
public class GdzqSealThirdRequest {

    private String keyword_sign_info;

    private String src_order_no;

    private Resource pdf_template_stream;

    private Resource seal_image_stream;
}