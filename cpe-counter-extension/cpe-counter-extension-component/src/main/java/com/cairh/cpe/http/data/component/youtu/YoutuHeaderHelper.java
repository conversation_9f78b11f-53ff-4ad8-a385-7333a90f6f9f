//package com.cairh.cpe.http.data.component.youtu;
//
//import com.alibaba.fastjson.JSONObject;
//import com.cairh.cpe.counter.http.request.AbstractHttpRequestEntity;
//import com.cairh.cpe.http.data.component.ocr.idcard.youtu.req.YouTuGatPassRequest;
//import com.cairh.cpe.http.data.component.youtu.req.YouTuGreencardRequest;
//import com.cairh.cpe.http.data.component.youtu.req.YouTuNormalRequest;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Component;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
//@Component
//public class YoutuHeaderHelper {
//
//	public String signature(HttpHeaders headers, AbstractHttpRequestEntity request){
//		if (request.getArguments()[0] instanceof YouTuGatPassRequest) {
//			YouTuGatPassRequest youTuGatPassRequest = (YouTuGatPassRequest) request.getArguments()[0];
//			Map<String, Object> body = new LinkedHashMap<>();
//			body.put("app_id", youTuGatPassRequest.getApp_id());
//			body.put("image", youTuGatPassRequest.getImage());
//			return String.valueOf(new JSONObject(body).toJSONString().getBytes().length);
//		}
//		if(request.getArguments()[0] instanceof YouTuGreencardRequest){
//			YouTuGreencardRequest youTuGreencardRequest = (YouTuGreencardRequest) request.getArguments()[0];
//			Map<String, Object> body = new LinkedHashMap<>();
//			body.put("app_id", youTuGreencardRequest.getApp_id());
//			body.put("image", youTuGreencardRequest.getImage());
//			return String.valueOf(new JSONObject(body).toJSONString().getBytes().length);
//		}
//		if(request.getArguments()[0] instanceof YouTuNormalRequest){
//			YouTuNormalRequest youTuNormalRequest = (YouTuNormalRequest) request.getArguments()[0];
//			Map<String, Object> body = new LinkedHashMap<>();
//			body.put("app_id", youTuNormalRequest.getApp_id());
//			body.put("image", youTuNormalRequest.getImage());
//			return String.valueOf(new JSONObject(body).toJSONString().getBytes().length);
//		}else{
//			return "";
//		}
//	}
//}
