package com.cairh.cpe.http.data.component.stocke.req;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StockeFaceDetectResponse {
    private String code;
    private String msg;
    private Data data;

    @lombok.Data
    @Accessors(chain = true)
    public class Data {
        Integer error_code;//人像检测结果：0为正确返回，其他为错误
        String description;//匹配结果描述
        Integer count;//人脸数量
        String request_id;//请求唯一标识符
        String security;//校验码
    }
}
