//package com.cairh.cpe.counter.extension.concrete;
//
//import com.cairh.cpe.counter.core.request.DefaultRequestParameterHandler;
//import com.cairh.cpe.counter.extension.AbstractGenericRequestParameterHandler;
//import com.cairh.cpe.http.data.component.youtu.ComponentYoutuHttpService;
//import org.springframework.stereotype.Component;
//
///**
// * {@link DefaultRequestParameterHandler} for youtu all-in-one
// * fixme 挪到了cpe-base-protocol-gateway中
// * <AUTHOR>
// */
//@Component
//public class YoutuRequestParameterHandler extends AbstractGenericRequestParameterHandler<ComponentYoutuHttpService> {
//
//    public YoutuRequestParameterHandler(ComponentYoutuHttpService apiServiceType) {
//        super(apiServiceType);
//    }
//}
