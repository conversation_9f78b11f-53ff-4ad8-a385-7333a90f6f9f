plugins {
    id 'war'
    id 'org.springframework.boot'
}

version ''

archivesBaseName = 'cpe-products-component-task'

dependencies {
    api("com.cairh:cpe-trace")
    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-actuator')
    api('org.apache.dubbo:dubbo')
    api('org.apache.dubbo:dubbo-spring-boot-starter')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')
    api('com.ctrip.framework.apollo:apollo-client')
    api('com.alibaba.spring:spring-context-support')
    api('com.cairh:cpe-job-core')
    api('com.cairh:cpe-counter-data-http')
    api("com.cairh:cpe-counter-data-kcbp")
    api(project(':cpe-esb-component-server-api'))
    api(project(':cpe-component-common'))
    api(project(':cpe-esb-component-server:cpe-esb-component-file'))
    api('com.cairh:cpe-counter-data-t2')
    api('com.cairh:cpe-core-autoconfigure')
    api('com.cairh:cpe-esb-archive-server-api')


    testImplementation('org.springframework.boot:spring-boot-starter-test')
}
