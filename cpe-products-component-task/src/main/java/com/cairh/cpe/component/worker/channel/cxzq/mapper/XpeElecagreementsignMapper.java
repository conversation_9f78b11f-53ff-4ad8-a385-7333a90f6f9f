package com.cairh.cpe.component.worker.channel.cxzq.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.component.worker.channel.cxzq.entity.XpeElecagreementsign;
import com.cairh.cpe.db.config.MultiDataSource;

import static com.cairh.cpe.component.worker.channel.cxzq.Constant.CXZQ_XPE;


/**
 * 电子协议签署表
 * 功能说明: <br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: Dec 14, 2015<br>
 */
@MultiDataSource(name = CXZQ_XPE)
public interface XpeElecagreementsignMapper extends BaseMapper<XpeElecagreementsign> {

}
