package com.cairh.cpe.component.worker.channel.cxzq.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.component.worker.channel.cxzq.entity.XpeArchfileinfo;
import com.cairh.cpe.db.config.MultiDataSource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import static com.cairh.cpe.component.worker.channel.cxzq.Constant.CXZQ_XPE;

@MultiDataSource(name = CXZQ_XPE)
public interface XpeArchfileinfoMapper extends BaseMapper<XpeArchfileinfo> {


    @Select("SELECT staff_no FROM crh_user.OPERATORINFO WHERE user_id = #{userId}")
    String userIdToStaffNo(@Param("userId") String userId);
}
