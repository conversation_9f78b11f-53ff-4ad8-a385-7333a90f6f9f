plugins {
    id 'war'
    id 'org.springframework.boot'
}

version ''

archivesBaseName = 'cpe-products-component-backend'

dependencies {

    api(project(':cpe-products-component-backend:cpe-products-component-backend-service'))
    api(project(':cpe-products-component-backend:cpe-products-component-backend-core'))
    api(project(':cpe-component-common'))
    api(project(':cpe-esb-component-server-api'))

    api('com.cairh:cpe-auth')
    api('com.cairh:cpe-context')
    api('com.cairh:cpe-mem')
    api('com.cairh:cpe-mq')
    api('com.cairh:cpe-job-core')

    api('org.springframework.boot:spring-boot-starter-activemq')
    api('org.springframework.boot:spring-boot-starter-actuator')
    api('io.micrometer:micrometer-registry-prometheus')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')
    //api('com.tencentcloud.tdsql:tdsql-pg-connector-java8')
    api ('org.postgresql:postgresql')

    api('com.alibaba.spring:spring-context-support')

    implementation 'io.jsonwebtoken:jjwt'
    testImplementation('org.springframework.boot:spring-boot-starter-test')
}
