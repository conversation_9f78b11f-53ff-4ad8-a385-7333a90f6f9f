package com.cairh.cpe.esb.comp.backend.data.counter.t2.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
public class T2_148008_Response implements Serializable {

    private static final long serialVersionUID = -7362955944753442450L;

    /** 试卷类别 字典:2500  */
    private String paper_type;
    /** 试卷子类*/
    private String sub_paper_type;

    /** 试卷类别 翻译成中文  */
    private String paper_type_name;

    private String sub_paper_name;

    /** 产品TA编号 字典:41009  */
    private String prodta_no;

    /** 机构标志 字典:1048  */
    private String organ_flag;
    private String organ_flag_name;

    /** 试卷名称  */
    private String paper_name;

    /** 基础分值  */
    private String base_score;

    /** 试卷最低分 (融资融券征信答卷的时候，若得分低于该分值，则重新答卷) */
    private String min_score;

    /** 可操作标志 字典:2300 (0-否, 1-是) */
    private String enable_flag;

    private String use_flag ;

    /** 备注  */
    private String remark;

    /** 每日测评次数*/
    private String daily_permit_times;

    /**T3 柜台不传入client_id时，该值就是每日测评的允许数**/
    private String daily_permit_times_remainder;
}