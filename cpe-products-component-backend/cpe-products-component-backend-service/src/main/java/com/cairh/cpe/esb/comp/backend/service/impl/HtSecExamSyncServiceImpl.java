package com.cairh.cpe.esb.comp.backend.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamPaper;
import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import com.cairh.cpe.component.common.data.service.IExamPaperService;
import com.cairh.cpe.component.common.data.service.IExamQuestionOptionsService;
import com.cairh.cpe.component.common.data.service.IExamQuestionService;
import com.cairh.cpe.component.common.form.ExamSyncReq;
import com.cairh.cpe.component.common.support.DubboProxyService;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.esb.comp.backend.data.counter.t2.dto.resp.T2_148008_Response;
import com.cairh.cpe.esb.comp.backend.service.IExamSyncService;
import com.cairh.cpe.ws.data.esb.service.EsbWsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 海通实现问卷同步
 *
 * <AUTHOR>
 * @since 2023/8/23 16:49
 */
@Slf4j
@Service
public class HtSecExamSyncServiceImpl implements IExamSyncService {

    private static final String QUESTION_SHOW_FLAG = "comp.risk.exam.ht.questionShowFlag";

    @Resource
    private EsbWsService esbWsService;
    @Autowired
    private IExamQuestionService examquestionService;
    @Autowired
    private IExamQuestionOptionsService examquestionoptionsService;
    @Autowired
    private IExamPaperService exampaperService;
    @Autowired
    private IdentifierGenerator identifierGenerator;
    @Autowired
    private DubboProxyService dubboProxyService;
    @Autowired
    private CompositePropertySources compositePropertySources;

    // String property = "s0_99:12|14;s1_90:13|14";
    public Map<String, Set<String>> getQuestionShowFlagConfigMap() {
        Map<String, Set<String>> questionShowFlagConfigMap = new HashMap<>();
        String property = compositePropertySources.getProperty(QUESTION_SHOW_FLAG);
        if (StringUtils.isBlank(property)) {
            return questionShowFlagConfigMap;
        }
        for (String confStr : property.split(";")) {
            int index = confStr.indexOf(":");
            if (index == -1) {
                log.info("【试题不显示配置】异常！ {}", property);
                throw new BizException("【试题不显示配置】异常！");
            }
            String busType = confStr.substring(0, index);
            String topicIdsStr = confStr.substring(index + 1);
            questionShowFlagConfigMap.put(busType, Stream.of(topicIdsStr.split("\\|")).collect(Collectors.toSet()));

        }
        log.info("【试题不显示配置】： {}", questionShowFlagConfigMap);
        return questionShowFlagConfigMap;
    }

    @Override
    public void examSyncReq(ExamSyncReq examSyncReq) {
        // todo 海通实现同步 注意修改配置脚本增加海通的实现类
        // 柜台问卷编号 counter_paper_no  存 busType 业务原子
        // 试卷子类 sub_paper_type 存 busScope
        // attribute 全部传 0
        syncCounter(examSyncReq);
    }

    public void syncCounter(ExamSyncReq examSyncReq) {
        if (StringUtils.isBlank(examSyncReq.getPaper_type())) {
            throw new BizException(ErrorCode.ERR_SYSERROR, "[paper_type]问卷类型不能为空");
        }

        if (StringUtils.isBlank(examSyncReq.getOrgan_flag())) {
            throw new BizException(ErrorCode.ERR_SYSERROR, "[organ_flag]机构标志不能为空");
        }

        //同步试卷
        ExamPaper examPaper = this.syncPaper(examSyncReq.getPaper_type(), examSyncReq.getOrgan_flag(), examSyncReq);


        ExamPaper matchExampaper = getMatchExampaper(examSyncReq, exampaperService);

        String paperType = examSyncReq.getPaper_type();
        String busType = examPaper.getCounter_paper_no();
        String busScope = examPaper.getSub_paper_type();
        String randomFlag = "a";//a-查询全部
//        List<Ws230240Response.Topic> topicList = queryExamQuestion(busType, busScope, "0", randomFlag);
//        if (CollectionUtils.isEmpty(topicList)) {
//            log.error("账户中心没有相关题目信息,examSyncReq为{}", JSONObject.toJSONString(examSyncReq));
//            throw new BizException(ErrorCode.ERR_SYSERROR, "账户中心没有相关题目信息");
//        }
//
//        HtSecExamSyncServiceImpl service = (HtSecExamSyncServiceImpl) AopContext.currentProxy();
//        service.syncQuestionNew(topicList, busType, examPaper, matchExampaper);
    }

//    @Transactional(rollbackFor = Exception.class)
//    public void syncQuestionNew(List<Ws230240Response.Topic> topicList, String busType, ExamPaper examPaper, ExamPaper oldExampaper) {
//        //如果是初始化状态
//        if (StringUtils.equals(Constant.COMMON_INIT_STATUS, examPaper.getStatus())) {
//            examPaper.setStatus(Constant.COMMON_VALID_STATUS);
//            exampaperService.updateById(examPaper);
//            exampaperService.saveJour(examPaper, true, Constant.BUSINESS_FLAG_MOD);
//        } else {
//            //4.1 获取并设置版本
//            List<ExamPaper> allPapersByType = exampaperService.lambdaQuery().eq(ExamPaper::getPaper_type, examPaper.getPaper_type())
//                    .select(ExamPaper::getSerial_id, ExamPaper::getVersion_no, ExamPaper::getStatus)
//                    .eq(ExamPaper::getOrgan_flag, examPaper.getOrgan_flag())
//                    .list();
//            if (CollectionUtils.isNotEmpty(allPapersByType)) {
//                OptionalDouble max = allPapersByType.stream().mapToDouble(x -> Double.valueOf(StringUtils.defaultIfBlank(x.getVersion_no(), "0"))).max();
//                examPaper.setVersion_no(String.valueOf(max.orElse(0) + 1));
//            }
//            examPaper.setSerial_id(identifierGenerator.nextUUID(null));
//            exampaperService.save(examPaper);
//            exampaperService.saveJour(examPaper, false, Constant.BUSINESS_FLAG_ADD);
//        }

        // 获取本地对应的试卷问题
        String exampaperId = null;
//        List<ExamQuestion> localExamquestionList = Collections.emptyList();
//        if (Objects.nonNull(oldExampaper)) {
//            exampaperId = oldExampaper.getSerial_id();
//            localExamquestionList = examquestionService.lambdaQuery().eq(ExamQuestion::getExampaper_id, exampaperId).list();
//        }

        /**
         * 保存所有题目
         */
//        String paper_id = examPaper.getSerial_id();
//        Map<String, Set<String>> questionShowFlagConfigMap = getQuestionShowFlagConfigMap();
        //保存所有题目答案
        int topicOrderNo = 0;
        int no = 1;
//        for (Ws230240Response.Topic topic : topicList) {
//
//            ExamQuestion examQuestion = new ExamQuestion();
//            examQuestion.setExampaper_id(paper_id); //题目id
//            examQuestion.setQuestion_no(topic.getTopicId());//问题编号
//            examQuestion.setOrder_no(Long.valueOf(topicOrderNo++));//题目顺序
//            examQuestion.setQuestion_kind(StringUtils.equals("2", topic.getTopicType()) ? "1" : "0");//本地题目类型(0单选题、1多选择题)  海通 1单选2多选
//            examQuestion.setQuestion_type("0");//题目类别(指主观客观等)
//
//            examQuestion.setRelation_type(convertRelationType(topic.getTopicAttribute()));//题目属性 关联  关联类型字段
//            examQuestion.setRemark(topic.getTopicAttribute());
//
////            examQuestion.setShow_flag("1"); //展示
//            examQuestion.setShow_flag(getShowFlag(questionShowFlagConfigMap.get(busType), String.valueOf(no++)));
//
//            // 有些题目就是没有分数的，这里就给 -1 分，问卷展示时记得处理
//            examQuestion.setScore(BigDecimal.valueOf(Float.valueOf(StringUtils.isBlank(topic.getRightScore()) ? "-1" : topic.getRightScore())));//题目分值
//            examQuestion.setQuestion_name(topic.getTopicTitle());//题目名称
//            examQuestion.setCreate_by(examPaper.getCreate_by());
//            examQuestion.setCreate_datetime(new Date());
//            examQuestion.setQuestion_answer(topic.getRightAnswer()); //题目答案
//            // 多选题题目类型
//            examQuestion.setQuestion_group(topic.getEvaluationPaperID());
//
//            String localExamQuestionId = fillQuestionPropertiesAndGetLocalQuestionId(localExamquestionList, examQuestion);
//
//            examquestionService.save(examQuestion);
//            exampaperService.saveExamquestionjour(examQuestion);
//
//            log.info("试题同步完成：【{}，[{}] show_flag={}】 TopicId={}, TopicTitle={}", topicOrderNo, busType, examQuestion.getShow_flag(),  topic.getTopicId(), topic.getTopicTitle());
//
//            this.dealGdAnswerNew(examQuestion, topic.getTopicOptionList(), paper_id, exampaperId, localExamQuestionId);// paper_type, organ_flag);//, user_id, prodta_no);//options及流水入库
//            replaceExamOptionSerialId(paper_id, exampaperService, examquestionService, examquestionoptionsService);
//        }

        /**
         * 修改原有套题的有效状态变成删除
         */
//        LambdaUpdateWrapper<ExamPaper> updateWrapper = new UpdateWrapper<ExamPaper>().lambda()
//                .eq(ExamPaper::getPaper_type, examPaper.getPaper_type())
//                .eq(ExamPaper::getOrgan_flag, examPaper.getOrgan_flag())
//                .eq(ExamPaper::getStatus, Constant.COMMON_VALID_STATUS)
//                .ne(ExamPaper::getSerial_id, paper_id) //不等自己
//                .set(ExamPaper::getStatus, Constant.COMMON_DELETE_STATUS);
//        exampaperService.update(updateWrapper);

//    }

    private static String convertRelationType(String topicAttribute) {
        if (StringUtils.isNotBlank(topicAttribute)) {
            switch (topicAttribute) {
                case "0"://柜台正常题目
                    return "";
                case "1": // 1 投资品种
                    return "4";
                case "2": // 2 投资期限
                    return "3";
                default:
                    return "";
            }
        }
        return null;
    }

    /**
     * 保存答案信息
     *
//     * @param examQuestion    问题信息
//     * @param topicOptionList 答案
//     * @param paper_id        保存题目id
//     * @param exampaperId     原有套题id
//     * @param examQuestionId  原有题目id
     */
//    private void dealGdAnswerNew(ExamQuestion examQuestion, List<Ws230240Response.TopicOption> topicOptionList, String paper_id, String exampaperId, String examQuestionId) {
//        if (CollectionUtils.isNotEmpty(topicOptionList)) {
//
//            String question_no = examQuestion.getQuestion_no();
//            String question_id = examQuestion.getSerial_id();
//            List<ExamQuestionOptions> options = new ArrayList<ExamQuestionOptions>();
//
//            List<ExamQuestionOptions> localExamquestionoptionList = Collections.emptyList();
//            if (BooleanUtils.isNotTrue(StringUtils.isAnyBlank(exampaperId, examQuestionId))) {
//                localExamquestionoptionList = examquestionoptionsService.lambdaQuery()
//                        .eq(ExamQuestionOptions::getExampaper_id, exampaperId)
//                        .eq(ExamQuestionOptions::getExamquestion_id, examQuestionId)
//                        .list();
//            }
//            for (Ws230240Response.TopicOption topicOption : topicOptionList) {
//                ExamQuestionOptions examQuestionOptions = new ExamQuestionOptions();
//                examQuestionOptions.setExampaper_id(paper_id);
//                examQuestionOptions.setExamquestion_id(question_id);
//                examQuestionOptions.setCreate_by(examQuestion.getCreate_by());
//                examQuestionOptions.setCreate_datetime(new Date());
//                examQuestionOptions.setOption_content(topicOption.getOptionNo() + "." + topicOption.getOptionContent());//答案名称
//                examQuestionOptions.setScore(new BigDecimal(topicOption.getScore()));
//                examQuestionOptions.setOption_no(Integer.valueOf(topicOption.getOptionId()));//答案编号
//                examQuestionOptions.setOrder_no(Long.valueOf(topicOption.getOptionId()));//答案排序(未找到匹配字段，用答案编号替代)
//                fillQuestionOptionProperties(localExamquestionoptionList, examQuestionOptions);
//                options.add(examQuestionOptions);
//
//                examquestionoptionsService.saveJour(examQuestionOptions, Constant.BUSINESS_FLAG_ADD);
//
//            }
//            examquestionoptionsService.saveBatch(options);
//        }
//    }

    @Override
    public List<T2_148008_Response> queryListFromCounter(ExamSyncReq examSyncReq) {
        // todo 海通实现同步 注意修改配置脚本增加海通的实现类
        return getExamPaperListFromCounter(examSyncReq.getPaper_type(), examSyncReq.getOrgan_flag());
    }

    public List<T2_148008_Response> getExamPaperListFromCounter(String paper_type, String organ_flag) {
        return getExamPaperList(paper_type, organ_flag);
    }

    public List<T2_148008_Response> getExamPaperList(String paper_type, String organ_flag) {
        List<T2_148008_Response> resultList = new ArrayList<T2_148008_Response>();

        VBaseDictQryRequest baseDictQryRequest = new VBaseDictQryRequest();
        baseDictQryRequest.setDict_code("paper_type");
        List<VBaseDictQryResponse> vBaseDictQryResponses = dubboProxyService.baseDataQryDict(baseDictQryRequest);
        log.info("问卷同步paper_type, dubbo baseDataQryDict字典接口查询结果: {}", JSON.toJSONString(vBaseDictQryResponses));

        if (CollectionUtils.isEmpty(vBaseDictQryResponses))
            return Collections.emptyList();

        for (VBaseDictQryResponse basedictionary : vBaseDictQryResponses) {

            T2_148008_Response eligpaper = new T2_148008_Response();
            eligpaper.setPaper_type(basedictionary.getSub_code());
            eligpaper.setPaper_type_name(basedictionary.getSub_name());
            eligpaper.setPaper_name(basedictionary.getSub_name());

            eligpaper.setOrgan_flag("0");
            eligpaper.setOrgan_flag_name("个人");

            eligpaper.setUse_flag("1");
            eligpaper.setEnable_flag(StringUtils.equals("8", basedictionary.getStatus()) ? "1" : "0");
            if (StringUtils.isNotBlank(paper_type) && !StringUtils.equals(eligpaper.getPaper_type(), paper_type)) {
                continue;
            }
            if (StringUtils.isNotBlank(organ_flag) && !StringUtils.equals(eligpaper.getOrgan_flag(), organ_flag)) {
                continue;
            }
            resultList.add(eligpaper);
        }
        log.info("海通问卷同步getExamPaperList 字典转换结果: {}", JSON.toJSONString(resultList));
        return resultList;
    }

    public ExamPaper getPaperByType(String paper_type, String organ_flag) {
        LambdaQueryWrapper<ExamPaper> queryWrapper
                = new LambdaQueryWrapper<ExamPaper>()
                .eq(ExamPaper::getPaper_type, paper_type)
                .eq(StringUtils.isNotBlank(organ_flag), ExamPaper::getOrgan_flag, organ_flag)
                .ne(ExamPaper::getStatus, Constant.COMMON_DELETE_STATUS); //不等于作废
        List<ExamPaper> papers = exampaperService.list(queryWrapper);

        if (CollectionUtils.isNotEmpty(papers)) {
            /**
             * 只有初始化状态版本号最大，且没有试题信息才取初始化的值，同步相关信息，不然就取有效的状态的
             */
            Optional<ExamPaper> initOptional = papers.stream()
                    .filter(temp -> StringUtils.equals(temp.getStatus(), Constant.COMMON_INIT_STATUS))
                    .max(Comparator.comparingDouble(x -> Double.valueOf(StringUtils.defaultIfBlank(x.getVersion_no(), "0"))));
            if (initOptional.isPresent()) {
                Integer count = Math.toIntExact(examquestionService.lambdaQuery().eq(ExamQuestion::getExampaper_id, initOptional.get().getSerial_id()).count());
                if (count < 1) return initOptional.get();
            }
            return papers.stream()
                    .filter(temp -> StringUtils.equals(temp.getStatus(), Constant.COMMON_VALID_STATUS))
                    .max(Comparator.comparingDouble(x -> Double.valueOf(StringUtils.defaultIfBlank(x.getVersion_no(), "0")))).orElse(null);
        }
        log.info("未找到paper_type=" + paper_type + "的试题");
        return null;
    }

    private ExamPaper syncPaper(String paper_type, String organ_flag, ExamSyncReq examSyncReq) {
        ExamPaper examPaper = getPaperByType(paper_type, organ_flag);

        Assert.notNull(examPaper, "未找到题库信息，不允许同步");
        if (StringUtils.isAnyBlank(examPaper.getCounter_paper_no(), examPaper.getSub_paper_type()))
            throw new BizException(ErrorCode.ERR_SYSERROR, "[counter_paper_no] 或 [sub_paper_type] 不能为空");

        /**
         * 初始化赋值，主要是刚导入的时候没有数据时，同步就给默认值，如果有就不给
         */
        String create_by = StringUtils.isNotBlank(examSyncReq.getCreate_by()) ? examSyncReq.getCreate_by() : "";
        create_by = StringUtils.isNotBlank(examSyncReq.getCreate_by()) ? create_by : examPaper.getCreate_by();
         /* VBaseDictQryRequest baseDictQryRequest = new VBaseDictQryRequest();
        baseDictQryRequest.setDict_code("paper_type");
        baseDictQryRequest.setStatus(Constant.COMMON_VALID_STATUS);
        baseDictQryRequest.setDict_code(examSyncReq.getPaper_type());
        List<VBaseDictQryResponse> vBaseDictQryResponses = dubboProxyService.baseDataQryDict(baseDictQryRequest);*/
        List<T2_148008_Response> examPaperList = getExamPaperList(examSyncReq.getPaper_type(), examSyncReq.getOrgan_flag());

        if (CollectionUtils.isNotEmpty(examPaperList))
            examPaper.setPaper_name(examPaperList.get(0).getPaper_name());
        if (StringUtils.isBlank(examPaper.getOrgan_flag()))
            examPaper.setOrgan_flag(examSyncReq.getOrgan_flag());
        if (Objects.isNull(examPaper.getDaily_permit_times()))
            examPaper.setDaily_permit_times(0);
        if (StringUtils.isBlank(examPaper.getIs_from_counter()))
            examPaper.setIs_from_counter("1");
        if (StringUtils.isBlank(examPaper.getStatus()))
            examPaper.setStatus(Constant.COMMON_VALID_STATUS);
        if (StringUtils.isBlank(examPaper.getCreate_by()))
            examPaper.setCreate_by(create_by);
        if (Objects.isNull(examPaper.getCreate_datetime()))
            examPaper.setCreate_datetime(new Date());

        if (Objects.isNull(examPaper.getBase_score()))
            examPaper.setBase_score(BigDecimal.ZERO);
        if (Objects.isNull(examPaper.getMin_score()))
            examPaper.setMin_score(BigDecimal.ZERO);
        if (Objects.isNull(examPaper.getMax_score()))
            examPaper.setMax_score(BigDecimal.ZERO);

        examPaper.setProdta_no(examPaperList.get(0).getProdta_no());
        examPaper.setModify_by(create_by);
        examPaper.setModify_datetime(new Date());

        return examPaper;
    }


    /**
     * 查询账户中心试题问卷
     *
     * @param busType    试题业务原子
     * @param busScope
     * @param attribute  属性
     * @param randomFlag 默认-随机，r-随机，a-全部
     * @return
     */
//    public List<Ws230240Response.Topic> queryExamQuestion(String busType, String busScope, String attribute, String randomFlag) {
//        Assert.hasText(busType, "业务原子不能为空");
//        Ws230240Request.MessageRequestBody request = new Ws230240Request.MessageRequestBody();
//        request.setResType(HtSecCommonUtil.RES_TYPE);
//        request.setBusType(busType);
//        request.setEmp(HtSecCommonUtil.SYSTEM_USER);
//        request.setDept(HtSecCommonUtil.BRANCH_CODE);
//        request.setOptDept(HtSecCommonUtil.BRANCH_CODE);
//        request.setMacAddr(HtSecCommonUtil.SERVER_MAC);
//        request.setBusScope(busScope);
//        request.setAttribute(attribute);
//        //randomFlag 默认-随机，r-随机，a-全部
//        request.setRandomFlag(randomFlag);
//
//
//        Ws230240Request ws230240Request = new Ws230240Request();
//        ws230240Request.setHeader(new Header());
//        ws230240Request.setBody(new EsbRequestXmlBean.Body<>(new EsbRequestXmlBean.Request<>(HtSecCommonUtil.getHtEsbHead(), request)));
//
//
//        Ws230240Response response = esbWsService.ws230240(ws230240Request);
//
//        if (HtSecCommonUtils.isResponseSucceed(response) && HtSecCommonUtil.isSucceed(response.getBody().getRequestResponse().getResponseBody().getRetCode())) {
//            return response.getBody().getRequestResponse().getResponseBody().getTopicList();
//        }
//        log.error("获取题目试题信息出错：busType={}，busScope={}，attribute={}，randomFlag={}", busType, busScope, attribute, randomFlag);
//        return null;
//
//    }

    /**
     * 获取试题显示标识
     * 在试题配置中的试题需要设置为不显示，其他的试题设置为显示
     *
     * @param questionNoSet 不显示的问题ID集合
     * @param topicId topicId
     * @return 试题显示标识
     */
    private String getShowFlag(Set<String> questionNoSet,  String topicId) {
        return (questionNoSet != null && questionNoSet.contains(topicId)) ? "0" : "1";
    }

}
