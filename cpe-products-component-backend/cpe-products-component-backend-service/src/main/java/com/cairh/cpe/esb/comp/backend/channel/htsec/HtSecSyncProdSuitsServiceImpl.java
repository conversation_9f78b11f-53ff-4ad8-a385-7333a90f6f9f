package com.cairh.cpe.esb.comp.backend.channel.htsec;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cairh.cpe.common.sysparameter.pull.channel.htsec.utils.HtSecCommonUtil;
import com.cairh.cpe.common.sysparameter.pull.data.entity.ThirdParameterSyncRelation;
import com.cairh.cpe.common.sysparameter.pull.data.mapper.ThirdParameterSyncMapper;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.constant.HtSecConstant;
import com.cairh.cpe.component.common.data.entity.ProdSuitInfo;
import com.cairh.cpe.component.common.data.entity.ProdSuitInfoJour;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.cairh.cpe.component.common.data.service.IProdSuitInfoService;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleService;
import com.cairh.cpe.component.common.support.DubboProxyService;
import com.cairh.cpe.component.common.utils.JourUtil;
import com.cairh.cpe.component.common.utils.htsecutils.HtSecCommonUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.protocol.gateway.request.htsec.esb.Header;
import com.cairh.cpe.esb.base.rpc.IVBaseHtSecBusinessAtomDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseHtSecBusinessAtomRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseHtSecBusinessAtomResponse;
import com.cairh.cpe.esb.comp.backend.channel.ISyncProdSuitsService;
import com.cairh.cpe.esb.comp.backend.channel.htsec.utils.CompositePropertySourcesUtils;
import com.cairh.cpe.esb.comp.backend.channel.htsec.utils.RiskRuleVerifyUtil;
import com.cairh.cpe.esb.comp.backend.channel.htsec.utils.ThirdParamNotFoundException;
import com.cairh.cpe.protocol.gateway.request.htsec.esb.EsbRequestXmlBean;
import com.cairh.cpe.ws.data.esb.service.EsbWsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 同步海通产品适当性
 *
 * <AUTHOR>
 * @since 2023/8/23 16:27
 */
@Slf4j
@Component
public class HtSecSyncProdSuitsServiceImpl implements ISyncProdSuitsService {

    private static final String HAI_TONG_SPECIAL_PRODUCT_CONFIG = "comp.suit.special.productCode";

    private static final String SPECIAL_PARAM_LEVEL = "scos.risk.s47_0001.type%s.product.level";
    private static final String SPECIAL_PARAM_TERM = "scos.risk.s47_0001.type%s.product.term";
    private static final String SPECIAL_PARAM_VARIETY = "scos.risk.s47_0001.type%s.product.variety";
    private static final String SPECIAL_PARAM_FORBIDEN = "scos.risk.s47_0001.type%s.forbiden";
    private static final String SPECIAL_PARAM_PASS = "scos.risk.s47_0001.type%s.pass";


    @Autowired
    private IProdSuitInfoService iProdsuitinfoService;
    @Autowired
    private ISuitMatchRuleService iSuitMatchRuleService;
    @Autowired
    private DubboProxyService dubboProxyService;
    @DubboReference(check = false, lazy = true)
    private IVBaseHtSecBusinessAtomDubboService baseHtSecBusinessAtomDubboService;
    @Resource
    private EsbWsService esbWsService;
    @Resource
    private CompositePropertySourcesUtils configSource;
    @Resource
    private ThirdParameterSyncMapper thirdParameterSyncMapper;

    @Override
    public void syncProdSuits() {
        List<ProdSuitInfo> productionList = this.getNeedSyncProdSuitslist();
        if (CollectionUtils.isEmpty(productionList))
            throw new BizException(ErrorCode.ERR_SYSWARNING, "未找到需要同步适当性的产品");

        //投资期限
        List<String> investTermList = getSubCodeList("user_invest_term");
        //风险等级
        List<String> riskLevelList = getSubCodeList("user_risk_level");
        //投资品种
        List<String> investKindList = getSubCodeList("user_invest_kind");

        for (ProdSuitInfo prodsuitinfo : productionList) {

            // 海通特殊产品同步
            if (isHaiTongSpecialProduct(prodsuitinfo)) {
                try {
                    haiTongSpecialProductSync(prodsuitinfo, investTermList, riskLevelList, investKindList);
                } catch (Exception e) {
                    log.error("海通产品适当性同步异常,产品编码:{},异常信息:{}", prodsuitinfo.getProd_code(), e.getMessage());
                }
                continue;
            }

            VBaseHtSecBusinessAtomResponse business = baseHtSecBusinessAtomDubboService.queryBusinessAtomByBusinType(prodsuitinfo.getProd_code());
            //BusinessAtomRelation business = iBusinessAtomRelationService.getById(prodsuitinfo.getProd_code());

            // 业务原子
            String zhzxBusCode = Objects.nonNull(business) ? business.getAtom_value() : "";

            Map<String, String> prodsuitinfoMap = getMapByselectBusinessRules(zhzxBusCode, "");
            List<SuitMatchRule> suitmatchruleList = null;
            if (Objects.nonNull(prodsuitinfoMap) && prodsuitinfoMap.size() >= 5) {
                /*
                 * 产品风险等级 查询失败这边会为空
                 */
                if (StringUtils.isNotBlank(prodsuitinfoMap.get("scos.risk.#.product.level".replace("#", zhzxBusCode)))) {
                    String productLevel = prodsuitinfoMap.get("scos.risk.#.product.level".replace("#", zhzxBusCode));
                    prodsuitinfo.setRisk_level(productLevel);
                }
                /*
                 * 产品投资期限
                 */
                if (StringUtils.isNotBlank(prodsuitinfoMap.get("scos.risk.#.product.term".replace("#", zhzxBusCode)))) {
                    String productTerm = prodsuitinfoMap.get("scos.risk.#.product.term".replace("#", zhzxBusCode));
                    prodsuitinfo.setInvest_term(productTerm);

                }
                /*
                 * 产品投资品种
                 */
                if (StringUtils.isNotBlank(prodsuitinfoMap.get("scos.risk.#.product.variety".replace("#", zhzxBusCode)))) {
                    String productVariety = prodsuitinfoMap.get("scos.risk.#.product.variety".replace("#", zhzxBusCode));
                    prodsuitinfo.setInvest_kind(productVariety);
                }


                /*
                 * 产品适当性规则  包括匹配规则和禁止规则
                 */
                if (StringUtils.isNotBlank(prodsuitinfoMap.get("scos.risk.#.forbiden".replace("#", zhzxBusCode)))
                        || StringUtils.isNotBlank(prodsuitinfoMap.get("scos.risk.#.pass".replace("#", zhzxBusCode)))) {
                    String forbiden = prodsuitinfoMap.get("scos.risk.#.forbiden".replace("#", zhzxBusCode));
                    String pass = prodsuitinfoMap.get("scos.risk.#.pass".replace("#", zhzxBusCode));
                    suitmatchruleList = getSuitmatchruleByHtSecAppropriatenessRule(forbiden, pass, investTermList, riskLevelList, investKindList);
                }
                HtSecSyncProdSuitsServiceImpl htSecSyncProdSuitsService = (HtSecSyncProdSuitsServiceImpl) AopContext.currentProxy();
                htSecSyncProdSuitsService.updateProdsuitinfo(prodsuitinfo, suitmatchruleList);
            }

        }


    }

    private boolean isHaiTongSpecialProduct(ProdSuitInfo prodsuitinfo) {
        String specialProductStr = configSource.getConfigValueOrDefault(HAI_TONG_SPECIAL_PRODUCT_CONFIG, "");
        if (StringUtils.isBlank(specialProductStr)) {
            return false;
        }
        return Stream.of(specialProductStr.split(",")).collect(Collectors.toSet()).contains(prodsuitinfo.getProd_code());
    }

    private void haiTongSpecialProductSync(ProdSuitInfo prodsuitinfo, List<String> investTermList, List<String> riskLevelList, List<String> investKindList) {
        String prodCode = prodsuitinfo.getProd_code();
        String type = prodCode.substring(prodCode.length() - 1);
        if (!"1".equals(type) && !"2".equals(type)) {
            throw new BizException("错误的产品编号，产品编号只能以‘1’或‘2’结尾！");
        }
        prodsuitinfo.setRisk_level(getThirdParamValue(String.format(SPECIAL_PARAM_LEVEL, type)));
        prodsuitinfo.setInvest_term(getThirdParamValue(String.format(SPECIAL_PARAM_TERM, type)));
        prodsuitinfo.setInvest_kind(getThirdParamValue(String.format(SPECIAL_PARAM_VARIETY, type)));

        String forbiden = getThirdParamValue(String.format(SPECIAL_PARAM_FORBIDEN, type));
        String pass = getThirdParamValue(String.format(SPECIAL_PARAM_PASS, type));
        List<SuitMatchRule> suitmatchruleList = null;
        if (StringUtils.isNotBlank(forbiden) || StringUtils.isNotBlank(pass)) {
            suitmatchruleList = getSuitmatchruleByHtSecAppropriatenessRule(forbiden, pass, investTermList, riskLevelList, investKindList);
        }
        HtSecSyncProdSuitsServiceImpl htSecSyncProdSuitsService = (HtSecSyncProdSuitsServiceImpl) AopContext.currentProxy();
        htSecSyncProdSuitsService.updateProdsuitinfo(prodsuitinfo, suitmatchruleList);
    }

    private String getThirdParamValue(String thirdParamName) {
        ThirdParameterSyncRelation tpsr = thirdParameterSyncMapper.selectById(thirdParamName);
        if (tpsr == null) {
            throw new ThirdParamNotFoundException("第三方参数同步关联表中不存在名称为‘" + thirdParamName + "’的第三方参数");
        }
        String thirdParamValue = tpsr.getThird_param_value();
        if (StringUtils.isBlank(thirdParamValue)) {
            throw new ThirdParamNotFoundException("第三方参数同步关联表中名称为‘" + thirdParamName + "’的第三方参名对应的值为空或空字符");
        }
        return thirdParamValue;
    }


    /**
     * 获取某个dictCode下的所有SubCode集合
     *
     * @param dictCode 字典编号
     * @return 该字段编号下所有的SubCode组成的集合
     */
    private List<String> getSubCodeList(String dictCode) {
        VBaseDictQryRequest baseDictQryRequest = new VBaseDictQryRequest();
        baseDictQryRequest.setDict_code(dictCode);
        List<VBaseDictQryResponse> investKindDict = dubboProxyService.baseDataQryDict(baseDictQryRequest);
        if (CollectionUtils.isEmpty(investKindDict)) {
            return new ArrayList<>();
        }
        return investKindDict.stream().map(VBaseDictQryResponse::getSub_code).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
    }

    /**
     * 修改适当性信息
     *
     * @param prodsuitinfo
     * @param suitmatchruleList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProdsuitinfo(ProdSuitInfo prodsuitinfo, List<SuitMatchRule> suitmatchruleList) {
        if (Objects.isNull(prodsuitinfo) || StringUtils.isAnyBlank(prodsuitinfo.getSerial_id(), prodsuitinfo.getProd_code(), prodsuitinfo.getProdta_no())) {
            log.warn("传入参数有误，不允许修改适当性信息Prodsuitinfo:{}", prodsuitinfo);
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "传入参数有误，不允许修改适当性信息");
        }

        iProdsuitinfoService.updateById(prodsuitinfo);
        log.info("更新一条产品适当性信息成功，serial_id为:{}", prodsuitinfo.getSerial_id());
        JourUtil.writeJour(prodsuitinfo, ProdSuitInfoJour.class, Constant.BUSINESS_FLAG_MOD, "setProdsuitinfo_id");

        LambdaUpdateWrapper<SuitMatchRule> deleteWrapper = new UpdateWrapper<SuitMatchRule>().lambda()
                .eq(SuitMatchRule::getProd_code, prodsuitinfo.getProd_code())
                .eq(SuitMatchRule::getProdta_no, prodsuitinfo.getProdta_no());
        iSuitMatchRuleService.remove(deleteWrapper);

        if (CollectionUtils.isNotEmpty(suitmatchruleList)) {
            suitmatchruleList.forEach(e -> {
                e.setProd_code(prodsuitinfo.getProd_code());
                e.setProdta_no(prodsuitinfo.getProdta_no());
            });
            iSuitMatchRuleService.saveBatch(suitmatchruleList);
            JourUtil.writeJour(() -> iSuitMatchRuleService.saveJour(suitmatchruleList, false, Constant.BUSINESS_FLAG_ADD));
        }
    }

    /**
     * 获取所有需要同步的产品
     *
     * @return
     */
    private List<ProdSuitInfo> getNeedSyncProdSuitslist() {


        List<VBaseHtSecBusinessAtomResponse> businessAtomRelationList = baseHtSecBusinessAtomDubboService.queryBusinessAtomList(new VBaseHtSecBusinessAtomRequest());

        /*List<BusinessAtomRelation> businessAtomRelationList = iBusinessAtomRelationService.lambdaQuery()
                .isNotNull(BusinessAtomRelation::getBusin_type)
                .isNotNull(BusinessAtomRelation::getAtom_value)
                //.ne(BusinessAtomRelation::getAtom_value, "")
                .list();*/
        if (CollectionUtils.isNotEmpty(businessAtomRelationList)) {
            List<Integer> collect = businessAtomRelationList.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getAtom_value()))
                    .map(VBaseHtSecBusinessAtomResponse::getBusin_type)
                    .collect(Collectors.toList());
            List<ProdSuitInfo> prodSuitInfos = iProdsuitinfoService.lambdaQuery().in(ProdSuitInfo::getProd_code, collect).list();
            return prodSuitInfos;
        }
        return null;
    }

    /**
     * 获取适当性集合
     *
     * @param forbiden
     * @param pass
     * @param investTermList
     * @param riskLevelList
     * @return
     */
    private List<SuitMatchRule> getSuitmatchruleByHtSecAppropriatenessRule(String forbiden, String pass, List<String> investTermList, List<String> riskLevelList, List<String> investKindList) {
        List<SuitMatchRule> suitmatchruleList = new ArrayList<>();
        if (StringUtils.isNotBlank(forbiden) && !StringUtils.equalsIgnoreCase(forbiden, "000")) {
            log.info("正在生产禁止规则适当性生成：{}", forbiden);
            //取禁止匹配规则项
            String invest_variety_forbiden = ""; //投资品种
            String invest_term_forbiden = "";//投资期限
            String risk_level_forbiden = "";//风险等级
            List<String> ban_key_arr = RiskRuleVerifyUtil.splitLogic(forbiden);
            if (ban_key_arr != null && ban_key_arr.size() == 3) {
                invest_variety_forbiden = null == ban_key_arr.get(0) ? "" : ban_key_arr.get(0);
                invest_term_forbiden = null == ban_key_arr.get(1) ? "" : ban_key_arr.get(1);
                risk_level_forbiden = null == ban_key_arr.get(2) ? "" : ban_key_arr.get(2);
            }


            RiskRuleVerifyUtil.RuleSuit tzpzRuleSuit = RiskRuleVerifyUtil.getTzpzRuleSuit(invest_variety_forbiden, investKindList, false);
            if (Objects.nonNull(tzpzRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.EN_INVEST_KIND)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_FORBIDEN)
                        .setEn_cust_suit_prop(tzpzRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(tzpzRuleSuit.getRule_logic())
                        .setRegular_expre(tzpzRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }


            RiskRuleVerifyUtil.RuleSuit tzqxRuleSuit = RiskRuleVerifyUtil.getTzqxRuleSuit(invest_term_forbiden, investTermList, false);
            if (Objects.nonNull(tzqxRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.EN_INVEST_TERM)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_FORBIDEN)
                        .setEn_cust_suit_prop(tzqxRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(tzqxRuleSuit.getRule_logic())
                        .setRegular_expre(tzqxRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }


            RiskRuleVerifyUtil.RuleSuit fxdjRuleSuit = RiskRuleVerifyUtil.getFxdjRuleSuit(risk_level_forbiden, riskLevelList, false);
            if (Objects.nonNull(fxdjRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.CORP_RISK_LEVEL)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_FORBIDEN)
                        .setEn_cust_suit_prop(fxdjRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(fxdjRuleSuit.getRule_logic())
                        .setRegular_expre(fxdjRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }
        }


        if (StringUtils.isNotBlank(pass) && !StringUtils.equalsIgnoreCase(pass, "000")) {
            log.info("正在生产适当性规则生成：{}", pass);
            String invest_variety = "";
            String invest_term = "";
            String risk_level = "";
            List<String> adapt_key_arr = RiskRuleVerifyUtil.splitLogic(pass);
            if (adapt_key_arr != null && adapt_key_arr.size() == 3) {
                invest_variety = null == adapt_key_arr.get(0) ? "" : adapt_key_arr.get(0);
                invest_term = null == adapt_key_arr.get(1) ? "" : adapt_key_arr.get(1);
                risk_level = null == adapt_key_arr.get(2) ? "" : adapt_key_arr.get(2);

            }


            RiskRuleVerifyUtil.RuleSuit tzpzRuleSuit = RiskRuleVerifyUtil.getTzpzRuleSuit(invest_variety, investKindList, true);
            if (Objects.nonNull(tzpzRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.EN_INVEST_KIND)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_PASS)
                        .setEn_cust_suit_prop(tzpzRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(tzpzRuleSuit.getRule_logic())
                        .setRegular_expre(tzpzRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }


            RiskRuleVerifyUtil.RuleSuit tzqxRuleSuit = RiskRuleVerifyUtil.getTzqxRuleSuit(invest_term, investTermList, true);
            if (Objects.nonNull(tzqxRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.EN_INVEST_TERM)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_PASS)
                        .setEn_cust_suit_prop(tzqxRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(tzqxRuleSuit.getRule_logic())
                        .setRegular_expre(tzqxRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }


            RiskRuleVerifyUtil.RuleSuit fxdjRuleSuit = RiskRuleVerifyUtil.getFxdjRuleSuit(risk_level, riskLevelList, true);
            if (Objects.nonNull(fxdjRuleSuit)) {
                SuitMatchRule suitmatchrule = new SuitMatchRule()
                        .setSuit_prop_type(HtSecConstant.CORP_RISK_LEVEL)
                        .setSuit_flag(HtSecConstant.SUIT_FLAG_PASS)
                        .setEn_cust_suit_prop(fxdjRuleSuit.getEn_cust_suit_prop())
                        .setRule_logic(fxdjRuleSuit.getRule_logic())
                        .setRegular_expre(fxdjRuleSuit.getRegular_expre());
                suitmatchruleList.add(suitmatchrule);
            }


        }
        return suitmatchruleList;
    }


    /**
     * 查询业务部单元配置信息
     *
     * @param zhzxBusCode
     * @param businessRules
     * @return String scos.risk.#.product.level:003=
     * scos.risk.#.product.term:003=
     * scos.risk.#.product.variety:002=
     * scos.risk.#.forbiden:{}or{}or{003or004or006}=
     * scos.risk.#.pass:{001and002}and{}and{001or002or005}=
     */
    private String selectBusinessRules(String zhzxBusCode, String businessRules) {
//
//        if (StringUtils.isBlank(businessRules))
//            businessRules = "scos.risk.#.product.level:000=scos.risk.#.product.term:000=scos.risk.#.product.variety:000=scos.risk.#.forbiden:000=scos.risk.#.pass:000=";

        String[] businessRules_arr = businessRules.split("=");
//        if (businessRules_arr.length < 5) return "false";

        String zhzx_businessRules = "";
        for (String key : businessRules_arr) {
            String[] key_arr = key.split(":");
            if (key_arr.length != 2) {
                continue;
            }
//            Ws230055Response response = this.getZhzxSysConfig("scos", key_arr[0].replace("#", zhzxBusCode));
//            if (HtSecCommonUtils.isResponseSucceed(response) && HtSecCommonUtil.isSucceed(response.getBody().getRequestResponse().getResponseBody().getRetCode())) {
//                String value = response.getBody().getRequestResponse().getResponseBody().getValue();
//                if (StringUtils.isBlank(value)) {
//                    zhzx_businessRules += key_arr[0] + ":" + "000";
//                } else {
//                    zhzx_businessRules += key_arr[0] + ":" + value;
//                }
//            } else {
//                log.error("适当性产品配中心查询失败，返回信息为：{}", response);
//                zhzx_businessRules += key_arr[0] + ":" + key_arr[1];
//            }
            zhzx_businessRules += "=";
        }
        return zhzx_businessRules;
    }


    /**
     * 查询业务部单元配置信息
     *
     * @param zhzxBusCode
     * @param businessRules
     * @return String scos.risk.#.product.level:003=
     * scos.risk.#.product.term:003=
     * scos.risk.#.product.variety:002=
     * scos.risk.#.forbiden:{}or{}or{003or004or006}=
     * scos.risk.#.pass:{001and002}and{}and{001or002or005}=
     */
    private Map<String, String> getMapByselectBusinessRules(String zhzxBusCode, String businessRules) {
//        if (StringUtils.isBlank(businessRules))
//            businessRules = "scos.risk.#.product.level:000=scos.risk.#.product.term:000=scos.risk.#.product.variety:000=scos.risk.#.forbiden:000=scos.risk.#.pass:000=";

        String[] businessRules_arr = businessRules.split("=");
//        if (businessRules_arr.length < 5 || StringUtils.isBlank(zhzxBusCode)) return null;

        Map<String, String> prodsuitinfoMap = new HashMap<>();

        for (String key : businessRules_arr) {
            String[] key_arr = key.split(":");
            if (key_arr.length != 2) {
                continue;
            }

            // 将keey中的#替换为业务原子
            String realKey = key_arr[0].replace("#", zhzxBusCode);

            // 根据配置获取value
//            Ws230055Response response = this.getZhzxSysConfig("scos", realKey);
//            if (HtSecCommonUtils.isResponseSucceed(response) && HtSecCommonUtil.isSucceed(response.getBody().getRequestResponse().getResponseBody().getRetCode())) {
//                String value = response.getBody().getRequestResponse().getResponseBody().getValue();
//                if (StringUtils.isBlank(value)) {
//                    prodsuitinfoMap.put(realKey, "000");
//                } else {
//                    prodsuitinfoMap.put(realKey, value);
//                }
//            } else {
//                log.error("适当性产品配中心查询失败，返回信息为：{}", response);
//                //prodsuitinfoMap.put(key_arr[0].replace("#", zhzxBusCode), "");
//            }
        }
        return prodsuitinfoMap;
    }

    /**
     * 查询账户中心配置
     *
     * @param module
     * @param code
     * @return
     */
//    public Ws230055Response getZhzxSysConfig(String module, String code) {
//
//        Ws230055Request.MessageRequestBody request = new Ws230055Request.MessageRequestBody();
//        request.setResType(HtSecCommonUtil.RES_TYPE);
//        request.setModule(module);
//        request.setCode(code);
//
//        Ws230055Request ws230055Request = new Ws230055Request();
//        ws230055Request.setHeader(new Header());
//        ws230055Request.setBody(new EsbRequestXmlBean.Body<>(new EsbRequestXmlBean.Request<>(HtSecCommonUtil.getHtEsbHead(), request)));
//
//        Ws230055Response response = esbWsService.ws230055(ws230055Request);
//        return response;
//
//    }

}
