package com.cairh.cpe.esb.comp.backend.auto.assiger;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cairh.cpe.esb.comp.backend.auto.constant.AutoAssignConstant;
import com.cairh.cpe.esb.comp.backend.auto.constant.VideoConstant;
import com.cairh.cpe.esb.comp.backend.auto.queue.impl.VideoUserGroup;
import com.cairh.cpe.esb.comp.backend.auto.service.IAutoAssignService;
import com.cairh.cpe.component.common.service.IRedisService;
import com.cairh.cpe.esb.comp.backend.auto.service.IVideoOperatorService;
import com.cairh.cpe.component.common.support.ConfigurationSupport;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.model.VideoUser;
import com.cairh.cpe.component.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 分配器
 *
 * <AUTHOR>
 * @since XPE-SP1-PACK6-PATCH3
 */
@Slf4j
@Component
public class Assigner {
    private static final Logger logger = LoggerFactory.getLogger(Assigner.class);
    @Autowired
    private DecisionMaker decisionMaker;
    @Autowired
    private ConfigurationSupport configurationSupport;
    @Resource
    private IAutoAssignService autoAssignService;
    @Autowired
    private IRedisService redisService;
    @Autowired
    private IVideoOperatorService videoOperatorService;
    @Resource
    private RedisTemplate<Object, Object> redisTemplate;
    @Resource
    private VideoUserGroup videoUserGroup;

    /**
     * 自动分配
     */
    public void autoAssign() {
        log.debug("开始执行视频自动分配任务");
        if (!configurationSupport.ifAutoAllocation()) {
            log.debug("未开启视频自动分配任务执行结束");
            return;
        }

        String waitQueueName = AutoAssignConstant.QUEUE_PREFIX_WAIT;
        if (!checkIfNeedBeforeGetLock()) {
            log.debug("无排队的视频队列自动分配任务执行结束");
            return;
        }
        boolean lockFlag = false;
        try {
            lockFlag = getLockOfAutoAssignQueue(waitQueueName); // 获取分配锁
            if (lockFlag) {
                assignQueue(waitQueueName);
            }
        } catch (Exception e) {
            logger.warn("自动分配，分配队列发生异常，queue={}", waitQueueName, e);
        } finally {
            if (lockFlag) {
                try {
                    releaseLockOfAutoAssignQueue(waitQueueName); // 释放锁
                } catch (Exception e) {
                    logger.warn("自动分配释放锁失败", e);
                }
            }
        }
    }

    /**
     * 检测是否须要分配
     */
    private boolean checkIfNeedBeforeGetLock() {
        if (CollectionUtils.isEmpty(getVideoUserList())) {
            return false;
        }
        return true;
    }

    private List<VideoUser> getVideoUserList() {
        Set<Object> videoQueues = redisTemplate.opsForHash().keys(Constant.COMPONENT_VIDEO_REDIS_QUEUE_GROUP);
        if (CollectionUtils.isEmpty(videoQueues)) {
            return Collections.emptyList();
        }
        List<VideoUser> users = new LinkedList<>();
        // 按队列优先级
        videoQueues.stream().mapToInt(q -> Integer.parseInt(q.toString())).sorted().forEachOrdered(q -> {
            Set<Object> userIds = redisTemplate.opsForZSet().range(Constant.COMPONENT_VIDEO_REDIS_VIDEO_QUEUE_UNIFIED_PREFIX + q, 0L, -1L);
            if (CollectionUtil.isNotEmpty(userIds)) {
                // 按用户优先级
                userIds.forEach(user_id -> {
                    Map<Object, Object> videoMap = redisTemplate.opsForHash().entries(Constant.COMPONENT_VIDEO_REDIS_VIDEO_USER_UNIFIED_PREFIX + user_id);
                    if (CollectionUtil.isNotEmpty(videoMap)) {
                        val user = BeanUtil.fillBeanWithMap(videoMap, new VideoUser(), CopyOptions.create().setIgnoreError(false));
                        users.add(user);
                    }
                });
            }
        });
        return users;
    }

    /**
     * 获取分配锁
     */
    private boolean getLockOfAutoAssignQueue(String autoAssignQueue) {
        String lockKey = autoAssignService.getAutoAssignQueueLockKey(autoAssignQueue);
        return redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 40, TimeUnit.SECONDS);
    }

    /**
     * 释放锁
     */
    private void releaseLockOfAutoAssignQueue(String autoAssignQueue) {
        String lockKey = autoAssignService.getAutoAssignQueueLockKey(autoAssignQueue);
        redisTemplate.delete(lockKey);
    }

    /**
     * 队列级别分配
     */
    private void assignQueue(String autoAssignKey) {
        // 找到对应的用户队列
        List<VideoUser> list = getVideoUserList();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        log.debug("排队中的视频为：{}", JSON.toJSONString(list));
        Set<String> operator_nos = autoAssignService.getAutoAssignQueueOperators(autoAssignKey);
        if (CollectionUtil.isEmpty(operator_nos)) {
            return;
        }
        log.debug("目前等待中的坐席有{}", com.alibaba.fastjson2.JSON.toJSONString(operator_nos));
        //XPE-11361 已经有审核或复核任务的，不自动分配视频任务
        Set<String> busy_operator_nos = Optional.ofNullable(redisService.getSortedSet("busyEmp")).orElse(Collections.emptySet());
        log.debug("目前等待中的坐席处于繁忙状态的有{}", com.alibaba.fastjson2.JSON.toJSONString(busy_operator_nos));
        if(!busy_operator_nos.isEmpty()) {
            log.debug("自动分配队列中的坐席={}, 繁忙坐席={}", operator_nos, busy_operator_nos);
            operator_nos.removeAll(busy_operator_nos);
            log.debug("删除繁忙坐席后，自动分配队列中的坐席={}", operator_nos);
        }
        if (CollectionUtil.isEmpty(operator_nos)) {
            return;
        }
        for (VideoUser queueUser : list) {
            try {
                assignUser(queueUser, autoAssignKey, operator_nos);
            } catch (Exception e) {
                logger.warn("分配用户发生异常 queueUser={}", queueUser, e);
            }
        }
    }

    /**
     * 用户级别分配
     */
    private void assignUser(VideoUser queueUser, String autoAssignKey, Set<String> operator_nos) {
        if (CollectionUtil.isEmpty(operator_nos)) {
            log.debug("空闲坐席已被分配完毕，此次不进行分配");
            return;
        }

        log.debug("开始分配视频用户{}详细信息为{}", queueUser.getUnique_id(), JSON.toJSONString(queueUser));
        if (StringUtils.isNotBlank(queueUser.getAssignedEmp())) {
            log.debug("{}已被分配{}", queueUser.getUnique_id(), queueUser.getAssignedEmp());
            return;
        }

        if (!VideoConstant.STATUS_0_WAITING.equals(queueUser.getStatus())) {
            logger.info("用户状态不为0，不进行分配，queueUser={}", queueUser);
            return;
        }
        // 尝试匹配一个坐席
        String operator_no = decisionMaker.suitableOperator(queueUser, autoAssignKey, operator_nos);
        if (StringUtils.isNotBlank(operator_no)) {

            updateUserWithAssignInfo(queueUser, operator_no); // 更新用户的分配信息
            videoOperatorService.updateAssignment(operator_no, queueUser.getUnique_id()); // 更新坐席的分配信息
            tellOperator(queueUser, operator_no); // 发消息通知坐席有分配任务
            operator_nos.remove(operator_no);
        }
    }

    /**
     * 发消息通知坐席有分配任务
     */
    private void tellOperator(VideoUser queueUser, String operator_no) {
        // 前置条件检查
        if (queueUser == null || StringUtils.isBlank(operator_no)) {
            logger.warn("自动分配，系统异常，queueUser={}，operator_no={}", queueUser, operator_no);
            return;
        }
        // 消息发送
        String project = "video-manage/videoDetail";
        String pageAddr = queueUser.getPage_addr();
        String unique_id = StringUtils.removeStart(queueUser.getUnique_id(), queueUser.getSubsys_no());
        if (StringUtils.isNotBlank(pageAddr)) {
            project = "kh-video";
        }
        String actionUrl = "/" + project + "?unique_id="
                + unique_id + "&request_id=" + queueUser.getRequest_id() + "&subsys_no=" + queueUser.getSubsys_no() +
                "&busin_type=" + queueUser.getBusin_type() + "&video_vender=" + queueUser.getService_vender();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("notice_type", "auto_assign");
        jsonObject.put("unique_id", queueUser.getUnique_id());
        jsonObject.put("operator_no", operator_no);
        jsonObject.put("time_out", configurationSupport.getAutoAllocationTimeout());
        jsonObject.put("user_name", queueUser.getUser_name());
        jsonObject.put("actionUrl", actionUrl);
        redisService.publish(VideoConstant.CHANNEL_VIDEO_TASK, jsonObject.toJSONString());
    }

    /**
     * 更新用户的分配信息
     */
    private void updateUserWithAssignInfo(VideoUser queueUser, String operator_no) {
        long time = redisService.getMillisecond();
        Map<String, Object> map = new HashMap<>();
        map.put(VideoUser.AUTO_ASSIGN_TIME, String.valueOf(time));
        map.put(VideoUser.ASSIGNED_EMP, operator_no);
        videoUserGroup.update(queueUser.getUnique_id(), map);
    }

}
