package com.cairh.cpe.esb.comp.backend.auto.constant;

/**
 * 视频组件标准错误号常量
 *
 * <AUTHOR>
 * @since SP1-PACK3
 */
public enum VideoStdErrorNo {

    // 通用 614000-614049

    UNKNOWN("614000", "（视频）系统异常"),

    ILLEGAL_ARGUMENT("614001", "参数传入错误"),

    ILLEGAL_USER_TYPE("614002", "错误的用户类型"),

    NEED_AUTHORIZATION("614003", "此功能需要授权"),

    // 业务系统底层 614050-614099

    OPERATOR_NOT_EXIST("614050", "操作员不存在"),

    ALLBRANCH_NOT_EXIST("614051", "营业部不存在"),

    USER_NOT_LOGGED("614052", "用户未登录"),

    NOT_IN_EN_BRANCH("614053", "查询营业部不再允许营业部内"),

    USER_NOT_EXIST("614054", "用户不存在"),

    NEED_CONFIGURATION("614055", "系统参数未配置"),

    // 双向视频 614100-614199

    QUEUE_USER_NOT_EXIST("614100", "队列用户不存在"),

    CAN_NOT_CAPTURE_WHEN_AUTOASSIGN("614101", "自动分配下不允许调用抢答接口"),

    CAN_NOT_QUEUE_AT_INVALID_REQUEST_STATUS("614102", "用户流程申请状态异常，不允许排队"),

    INVALID_VIDEO_VENDER("614103", "错误的视频厂商类型"),

    NO_VALID_VIDEO_WORK_TIME("614104", "没有有效的视频工作时间"),

    EXCEED_MAX_QUEUE_LENGTH("614105", "超过排队最大人数"),

    CAN_NOT_VERIFY_PASS_WHEN_NOT_VIDEOING("614106", "非视频验证中，不允许验证通过"),

    CAN_NOT_VERIFY_PASS_WHEN_VIDEO_NOT_UPLOAD("614107", "视频文件未上传，不允许验证通过"),

    CAN_NOT_VERIFY_PASS_WHEN_NOT_OPERATOR("614108", "非操作员不允许做验证通过操作"),

    CR_CREATE_ROOM_ERROR("614109", "云屋视频创建房间错误"),

    NO_AVAILABLE_QUEUE_USER("614110", "所有任务已被领取"),

    NO_VIDEO_WORK_TIME("614111", "没有配置视频工作时间"),

    HANG_UP_BY_SYSTEM("614112", "系统挂断"),

    NOT_VERIFY_PASS_SMS_LENGTH_OUT("614113", "视频打回时发送给客户的短信内容长度超出了系统配置的短信内容长度"),

    NOT_VERIFY_PASS_SMS_SEND_FAILD("614114", "视频打回时发送短信给客户失败"),

    DOWNLOAD_FILE_ERROR("614115", "文件下载失败"),

    TRANSTO_FILE_ERROR("614116", "转存临时文件失败"),

    NEED_RE_QUEUE("614117", "请重新排队"),

    VIDEO_INFO_NOT_MATCH("614118", "视频信息不匹配"),

    ZEGO_TOKEN_URL_ERROR("614119", "即构登录token接口调用失败"),

    ZEGO_RECORD_INTERFACE_ERROR("614120", "即构录制接口调用失败"),

    UPDATE_ANT_VIDEO_PARAM_ERROR("614121", "更新蚂蚁视频参数异常"),

    INVOKE_SHINE_INTERFACE_ERROR("614122", "调用新意接口异常"),

    GET_TENCENT_CLOUD_RECORED_VIDEO_ERROR("614123", "获取腾讯云录制视频异常"),

    HAVE_NOT_RECORD_VIDEO_YET("614124", "还未录制视频文件"),

    INVOKE_CIF_INTERFACE_ERROR("614125", "调用CIF接口异常"),

    DELETE_TENCENT_CLOUD_RECORDED_VIDEO_ERROR("614126", "删除腾讯云录制视频异常"),

    /** 调用搜狗接口异常 */
    INVOKE_SOUGOU_INTERFACE_ERROR("614127", "调用搜狗接口异常"),

    // 自动分配 614200-614249

    USER_HAVE_LEAVE("614200", "用户已离开队列"),

    DEALED_MESSAGE("614201", "任务已被处理"),

    // 文件上传 614250-614299

    FAIL_TO_UPLOAD_TO_ARCH("614250", "上传到档案服务器失败"),

    NOT_MULTIPART("614251", "HTTP请求不是multipart"),

    USERVIDEOVERIFY_NOT_EXIST("614252", "视频验证记录不存在"),

    FILE_NOT_EXIST("614253", "文件不存在"),

    CONVERT_VIDEO_FORMAT_ERROR("614254", "转化视频文件编码异常"),

    // 语音转写 614300-614349

    E614300_IFLYTEK_INIT_FAIL("614300", "科大讯飞初始化异常"),

    E614301_TEXT_TO_AUDIO_ERROR("614301", "文字转语音异常"),

    E614302_AUDIO_TO_TEXT_ERROR("614302", "语音转文字异常"),

    E614303_IFLYTEK_API_INVOKE_ERROR("614303", "科大讯飞接口调用异常"),

    E614304_COMPOSE_FILE_ERROR("614304", "文件合并失败"),

    // 其他 614950-614999

    DATA_NOT_UNIQUE("614950", "数据不唯一"),

    INVOKE_ARCH_ERROR("614995", "调用档案组件接口异常"),

    THIRD_PART_INVOKE_ERROR("614996", "第三方接口调用异常"),

    RECORD_NOT_EXIST("614997", "记录不存在"),

    RECORD_ALREADY_EXIST("614998", "记录已存在，无法重复添加"),

    FAIL_TO_GET_WEB_ADDR("614999", "获取web服务地址发生错误"),

    SERIAL_NO_NOY_UNIQUE("615000", "视频流水号对应的任务不唯一"),

    SERIAL_NO_NOY_EXIST("615001", "缓存中视频流水号不存在"),

    CONVERT_VIDEO_ERROR("615002", "FFMPEG转换视频失败"),

    FILE_NO_NOY_EXIST("615003", "文件不存在"),

    TENCENT_RECORD_INTERFACE_ERROR("615004", "腾讯录制接口调用失败"),

    PARAMS_ALL_NOT_EXIST("615005", "参数同时不存在"),

    EXCEED_MAX_CHANNERL_QUEUE_LENGTH("615006", "超过渠道排队最大人数"),

    EXCEED_MAX_CHANNERL_CONVERT_FAIL("615007", "渠道最大排队数配置转换失败");

    /** 错误号 */
    private final String errorCode;

    /** 错误信息 */
    private final String errorInfo;

    /**
     * 构造器
     *
     * @param errorCode 错误号
     * @param errorInfo 错误信息
     */
    VideoStdErrorNo(String errorCode, String errorInfo) {
        this.errorCode = errorCode;
        this.errorInfo = errorInfo;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    @Override
    public String toString() {
        return "{" + "errorCode='" + errorCode + ", errorInfo='" + errorInfo + "}";
    }

}
