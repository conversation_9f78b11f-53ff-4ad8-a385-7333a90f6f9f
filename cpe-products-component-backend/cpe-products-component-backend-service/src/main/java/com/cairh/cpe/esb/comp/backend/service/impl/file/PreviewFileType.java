package com.cairh.cpe.esb.comp.backend.service.impl.file;

/**
 * <AUTHOR>
 * @since 2022-09-06
 */
public enum PreviewFileType {

    PDF("pdfPreviewHandler", ".pdf"),

    DOCX("wordPreviewHandler", ".docx"),

    DOC("wordPreviewHandler", ".doc"),

    HTML("htmlPreviewHandler", ".html");

    private String name;
    private String index;

    private PreviewFileType(String name, String index) {
        this.name = name;
        this.index = index;
    }

    public static String getName(String index) {
        for (PreviewFileType c : PreviewFileType.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return "otherPreviewHandler";
    }


    public static String getIndex(String name) {
        for (PreviewFileType c : PreviewFileType.values()) {
            if (c.getName().equals(name)) {
                return c.index;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getIndex() {
        return index;
    }

}
