package com.cairh.cpe.esb.comp.backend.channel.gdzq.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.util.json.FastJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class AgreeUtils {

	/**
	 * 获取下一个版本号
	 * @param version 当前版本号
	 * @return
	 */
	public static String getNextVersion(String version){
		if(StringUtils.isBlank(version)){
			return "";
		}
		if(version.length() == 1 && ".".equals(version)){
			return version + "0";
		}
		//获取最后一个.后面的字符串
		int index = version.lastIndexOf(".");
		if(index < 0){
			return version + ".0";
		}
		String last = version.substring(index+1);
		if(StringUtils.isBlank(last)){
			if(index > 0){
				return version + "0";
			}else{
				return version + ".0";
			}
		}
		//如果last全是数字组成，则直接将数字加1，否则直接拼上.0
		Pattern pattern = Pattern.compile("[0-9]*");
		Matcher isNum = pattern.matcher(last);
		if(isNum.matches()){
			String last_str = String.valueOf(Integer.parseInt(last) + 1);
			String pre_str = version.substring(0, index+1);
			version = pre_str + last_str;
		}else{
			version = version + ".0";
		}
		return version;
	}

    /**
     * 获取档案文件类型:如果传入的档案文件类型不为空，则用传入的，如果传入的为空，模版表的不为空，则用模版表的，如果两个都为空，则取默认值8F
     *
     * @param input_archfile_no 外部传入的档案文件类型
     * @param model_archfile_no 协议模版表的档案文件类型
     * @return
     */
    public static String getArchfileno(String input_archfile_no, String model_archfile_no) {
        String archfile_no = "8F";
        if (StringUtils.isNotBlank(input_archfile_no)) {
            archfile_no = input_archfile_no;
        } else if (StringUtils.isNotBlank(model_archfile_no)) {
            archfile_no = model_archfile_no;
        }
        return archfile_no;
    }

	/**
	 * 将特有key的值写到以有的json串中
	 * @param key
	 * @param value
	 * @param sourceJson
	 * @return
	 */
	public static String setDataToJsonStr(String key,String value,String sourceJson){
		if(StringUtils.isNotBlank(value)){
			if(StringUtils.isBlank(sourceJson)){
				JSONObject json = new JSONObject();
				json.put(key,value);
				return FastJsonUtil.toJSONString(json);
			}else{
				JSONObject json = JSON.parseObject(sourceJson);
				json.put(key,value);
				return FastJsonUtil.toJSONString(json);
			}
		}else{
			return sourceJson;
		}
	}

	public static boolean checkIsJsonFormat(String str) {
		if (StringUtils.isNotBlank(str)) {
			try {
				str = str.replace("“", "\"").replace("”", "\"");
				FastJsonUtil.parseObject(str, Map.class);
			} catch (Exception e) {
				log.error("-->> 字符串[" + str + "]解析Json格式失败", e);
				return false;
			}
		}
		return true;
	}
}