package com.cairh.cpe.esb.comp.backend.controller;

import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.comp.backend.service.IFileManageService;
import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@RestController
@RequestMapping("/filemanage")
public class FileManageController {

    @Autowired
    private IFileManageService fileManageService;

    /**
     * 文件上传
     *
     * @param file   文件
     * @param remark 备注
     * @return
     */
    @RequestMapping(value = "/doElectUploadFile", method = RequestMethod.POST)
    public Result<ElectUploadFileResponse> doElectUploadFile(MultipartFile file, String remark,String file_arch_type) {
        return Result.success(fileManageService.doElectUploadFile(file, remark,file_arch_type));
    }

    /**
     * 根据文件路径上传文件
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electUploadFileByUri", method = RequestMethod.POST)
    public Result<ElectUploadFileByUriResponse> electUploadFileByUri(@RequestBody ElectUploadFileByUriRequest request) {
        return Result.success(fileManageService.electUploadFileByUri(request));
    }

    /**
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electUploadFileByBase64", method = RequestMethod.POST)
    public Result<ElectUploadFileResponse> electUploadFileByBase64(@RequestBody ElectUploadFileRequest request) {
        return Result.success(fileManageService.doElectUploadFile(request));
    }

    /**
     * 文件下载
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electDownloadFile", method = {RequestMethod.POST,RequestMethod.GET})
    public void electDownloadFile( ElectDownloadFileRequest request)throws Exception {
        fileManageService.electDownloadFile(request);
    }

    /**
     * 视频预览
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/authless/electPreviewVideo", method = {RequestMethod.POST,RequestMethod.GET})
    public void electPreviewVideo( ElectDownloadFileRequest request)throws Exception {
        fileManageService.electPreviewVideo(request);
    }

    /**
     * 图片上传
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electUploadImage", method = RequestMethod.POST)
    public Result<ElectUploadImageResponse> electUploadImage(@RequestBody ElectUploadImageRequest request) {
        return Result.success(fileManageService.electUploadImage(request));
    }

    /**
     * 根据路径进行图片上传
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electUploadImageByUri", method = RequestMethod.POST)
    public Result<ElectUploadImageByUriResponse> electUploadImageByUri(@RequestBody ElectUploadImageByUriRequest request) {
        return Result.success(fileManageService.electUploadImageByUri(request));
    }

    /**
     * 图片下载
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/electDownloadImage", method = {RequestMethod.POST,RequestMethod.GET})
    public Result<ElectDownloadImageResponse> electDownloadImage( ElectDownloadImageRequest request) {
        return Result.success(fileManageService.electDownloadImage(request));
    }


    @RequestMapping(value = "/electUploadPdf2Image", method = RequestMethod.POST)
    public Result<ElectUploadPdf2ImageResponse> uploadPdf2Image(@RequestBody ElectUploadPdf2ImageRequest request) {
        return Result.success(fileManageService.electUploadPdf2Image(request));
    }
}