package com.cairh.cpe.esb.comp.backend.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.component.common.cache.ApplicationContextHolder;
import com.cairh.cpe.component.common.cache.dict.CacheDict;
import com.cairh.cpe.component.common.data.entity.SuitMatchRecord;
import com.cairh.cpe.component.common.data.entity.SuitMatchRule;
import com.cairh.cpe.component.common.data.service.ISuitMatchRecordService;
import com.cairh.cpe.component.common.data.service.ISuitMatchRuleService;
import com.cairh.cpe.component.common.form.SuitMatchRecordForm;
import com.cairh.cpe.component.common.form.SuitmatchruleForm;
import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.Basedictionary;
import lombok.AllArgsConstructor;
import lombok.Lombok;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * 适当性匹配流水
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/Suitmatchrecord")
public class SuitMatchRecordController {

    private final ISuitMatchRecordService service;

    /**
     * 分页查询适当性匹配流水
     * @param param
     * @return
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.POST)
    public Result<Page<SuitMatchRecord>> queryByPage(@RequestBody SuitMatchRecordForm param) {

        Date[] curr_date_range = param.getCurr_date_range();

        QueryWrapper<SuitMatchRecord> queryWrapper = new QueryWrapper();
        //判断时间范围，查询条件封装
        if (curr_date_range != null) {
            queryWrapper.ge("create_datetime", curr_date_range[0]);
            queryWrapper.le("create_datetime", curr_date_range[1]);
        }
        LambdaQueryWrapper<SuitMatchRecord> ge = queryWrapper.lambda()
                .like(StringUtils.isNotBlank(param.getClient_name()),SuitMatchRecord::getClient_name,param.getClient_name())
                .like(StringUtils.isNotBlank(param.getId_no()),SuitMatchRecord::getId_no,param.getId_no())
                .like(StringUtils.isNotBlank(param.getClient_id()),SuitMatchRecord::getClient_id,param.getClient_id())
                .eq(StringUtils.isNotBlank(param.getId_kind()),SuitMatchRecord::getId_kind,param.getId_kind())
                .eq(StringUtils.isNotBlank(param.getOrgan_flag()),SuitMatchRecord::getOrgan_flag,param.getOrgan_flag());

        SuitMatchRecordForm page = service.page(param, ge);
        List<SuitMatchRecord> records = page.getRecords();
        CacheDict cacheDictionaryService = ApplicationContextHolder.get(CacheDict.class);

        List<Basedictionary> organFlagList = cacheDictionaryService.baseDataQryDictList("organ_flag");

        List<Basedictionary> idKindList = cacheDictionaryService.baseDataQryDictList("id_kind");


        records.stream().forEach(r->{

            for (Iterator<Basedictionary> iterator = organFlagList.iterator(); iterator.hasNext(); ) {
                Basedictionary dictionary = iterator.next();
                if(org.apache.commons.lang3.StringUtils.equals(dictionary.getSub_code(), r.getOrgan_flag())){
                    r.setOrgan_flag(dictionary.getSub_name());
                }
            }

            for (Iterator<Basedictionary> iterator = idKindList.iterator(); iterator.hasNext(); ) {
                Basedictionary dictionary = iterator.next();
                if(org.apache.commons.lang3.StringUtils.equals(dictionary.getSub_code(), r.getId_kind())){
                    r.setId_kind(dictionary.getSub_name());
                }
            }
        });
        for(SuitMatchRecord record : records){
            record.setId_no_desen(record.getId_no());
        }
        page.setRecords(records);
        return Result.success(page);
    }
}
