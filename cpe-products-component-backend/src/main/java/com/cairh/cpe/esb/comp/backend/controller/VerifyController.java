package com.cairh.cpe.esb.comp.backend.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.service.IIdVerifyRecordService;
import com.cairh.cpe.component.common.form.VerifyQueryForm;
import com.cairh.cpe.component.common.form.response.VerifyQueryResp;
import com.cairh.cpe.context.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 公安认证查询
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("verify")
public class VerifyController {


    @Autowired
    private IIdVerifyRecordService iidverifyrecordService;

    /**
     * 公安认证查询
     *
     * @return 分页结果
     */
    @PostMapping("queryByPage")
    public Result<Page<VerifyQueryResp>> queryByPage(@Valid @RequestBody VerifyQueryForm form) {
        Page<VerifyQueryResp> page = iidverifyrecordService.verifyQuery(form);
        return Result.success(page);
    }

}
