package com.cairh.cpe.esb.comp.backend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.component.common.data.service.IVideoAssignJourService;
import com.cairh.cpe.component.common.form.VideoAssignJourForm;
import com.cairh.cpe.component.common.model.VideoAssignJourEntity;
import com.cairh.cpe.context.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 功能说明: 视频派单流水
 * 公司名称: 杭州财人汇网络股份有限公司
 * 开发人员: <EMAIL>
 * 开发时间: 2023-12-28 14:14
 */
@Slf4j
@RestController
@RequestMapping("videoAssignJour")
public class VideoAssignJourController {

    @Autowired
    private IVideoAssignJourService videoAssignJourService;


    @RequestMapping(value = "/queryByPage", method = RequestMethod.POST)
    public Result<Page<VideoAssignJourEntity>> queryByPage(@RequestBody VideoAssignJourForm form) {
        return Result.success(videoAssignJourService.queryByPage(form));
    }


}
