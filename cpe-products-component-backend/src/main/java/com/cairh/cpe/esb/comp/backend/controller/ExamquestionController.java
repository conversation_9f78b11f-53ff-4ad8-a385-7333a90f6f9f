package com.cairh.cpe.esb.comp.backend.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.ExamQuestion;
import com.cairh.cpe.component.common.data.service.IExamQuestionService;
import com.cairh.cpe.component.common.form.ExamquestionDto;
import com.cairh.cpe.component.common.form.ExamquestionForm;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 问卷试题管理
 */
@RestController
@RequestMapping("examquestion")
public class ExamquestionController {
    @Autowired
    private IExamQuestionService examquestionService;

    /**
     * 问卷试题详情查询
     *
     * @param entity
     * @return
     */
    @PostMapping("queryOne")
    public Result<ExamQuestion> queryOne(@RequestBody ExamQuestion entity) {
        return Result.success(examquestionService.getById(entity.getSerial_id()));
    }

    /**
     * 问卷试题列表查询
     *
     * @param param
     * @return
     */
    @PostMapping("queryByPage")
    public Result<Page<ExamQuestion>> queryByPage(@RequestBody ExamquestionForm param) {
        ExamQuestion examQuestion = BaseBeanUtil.copyProperties(param, ExamQuestion.class);
        Page<ExamQuestion> pageInfo = examquestionService.page(param, new QueryWrapper<>(examQuestion));
        return Result.success(pageInfo);
    }

    /**
     * 新增问卷试题
     *
     * @param entity
     * @return
     */
    @PostMapping("insert")
    public Result<ExamQuestion> insert(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ExamquestionDto entity) {
        examquestionService.baseSave(baseUser, entity);
        return Result.success(entity);
    }

    /**
     * 修改问卷试题
     *
     * @param entity
     * @return
     */
    @PostMapping("update")
    public Result<String> update(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ExamquestionDto entity) {
        examquestionService.baseUpdate(baseUser, entity);
        return Result.success(Constant.SUCCESS);
    }

    /**
     * 删除问卷试题
     *
     * @param entity
     * @return
     */
    @PostMapping("delete")
    public Result<String> delete(@AuthenticationPrincipal BaseUser baseUser, @RequestBody ExamQuestion entity) {
        examquestionService.baseDelete(baseUser, entity);
        return Result.success(Constant.SUCCESS);
    }

    /**
     * 试题分组查询
     */
    @PostMapping("queryGroup")
    public Result<Map<String, Object>> queryGroup(@AuthenticationPrincipal BaseUser baseUser,@RequestBody ExamQuestion entity){
        return Result.success(examquestionService.baseQueryGourp(baseUser,entity));
    }

    /**
     * 查询分组下的试题
     */
    @PostMapping("queryQuestions")
    public Result<Map<String,List<ExamQuestion>>> queryQuestions(@AuthenticationPrincipal BaseUser baseUser,@RequestBody ExamQuestion entity){
        return Result.success(examquestionService.baseQueryQuestions(baseUser,entity));
    }
}