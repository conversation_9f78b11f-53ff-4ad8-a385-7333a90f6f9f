package com.cairh.cpe.esb.comp.backend.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.component.common.constant.Constant;
import com.cairh.cpe.component.common.data.entity.NoticeModel;
import com.cairh.cpe.component.common.data.service.INoticeModelService;
import com.cairh.cpe.component.common.form.NoticemodelForm;
import lombok.AllArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * 消息模板
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@RestController
@RequestMapping("/noticemodel")
@AllArgsConstructor
public class NoticemodelController {

    private final INoticeModelService service;

    /**
     * 添加消息模板
     *
     * @param entity
     * @return
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public Result<String> insert(@AuthenticationPrincipal BaseUser baseUser, @RequestBody NoticeModel entity) {
        service.baseSave(baseUser, entity);
        return Result.success(Constant.SUCCESS);

    }

    /**
     * 编辑消息模板
     *
     * @param entity
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result<String> update(@AuthenticationPrincipal BaseUser baseUser, @RequestBody NoticeModel entity) {
        entity.setModify_by(baseUser.getStaff_no());
        service.updateById(entity);
        return Result.success(Constant.SUCCESS);
    }

    /**
     * 查询消息模板详情
     *
     * @param entity
     * @return
     */
    @RequestMapping(value = "/queryOne", method = RequestMethod.POST)
    public Result<NoticeModel> queryOne(@RequestBody NoticeModel entity) {
        return Result.success(service.getById(entity.getSerial_id()));
    }

    /**
     * 分页查询消息模板列表
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.POST)
    public Result<Page<NoticeModel>> queryByPage(@RequestBody NoticemodelForm param) {
        return Result.success(service.page(param,
                new QueryWrapper<>(BaseBeanUtil.copyProperties(param, NoticeModel.class))));
    }

    /**
     * 删除消息模板(传serial_id)
     *
     * @param entity
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result<String> delete(@AuthenticationPrincipal BaseUser baseUser, @RequestBody NoticeModel entity) {
        entity.setModify_by(baseUser.getStaff_no());
        //逻辑删除
        entity.setStatus(Constant.COMMON_DELETE_STATUS);
        service.updateById(entity);
        return Result.success(Constant.SUCCESS);
    }

}
