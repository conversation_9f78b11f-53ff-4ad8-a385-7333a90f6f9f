package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 授权认证表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("assemblyaccept")
public class AssemblyAccept extends Model<AssemblyAccept> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("request_id")
    private String request_id;

    /**
     * req_content
     */
    @TableField("req_content")
    private String req_content;

    /**
     * rep_content
     */
    @TableField("rep_content")
    private String rep_content;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * tohis_flag
     */
    @TableField(value = "tohis_flag")
    private String tohis_flag;

    /**
     * create_datetime
     */
    @TableField(value = "create_datetime", fill = FieldFill.INSERT)
    private Date create_datetime;

    /**
     * modify_datetime
     */
    @TableField(value = "modify_datetime", fill = FieldFill.INSERT_UPDATE)
    private Date modify_datetime;


    /**
     * remark
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 清算日期
     */
    @TableField(value = "date_clear")
    private Integer date_clear;

    @Override
    public Serializable pkVal() {
        return this.request_id;
    }

}
