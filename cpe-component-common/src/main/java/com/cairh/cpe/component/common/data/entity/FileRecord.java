package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;


@Getter
@Setter
@TableName("FILERECORD")
public class FileRecord extends Model<FileRecord> {
    private static final long serialVersionUID = 1110534659956414484L;
    /**serial_id*/
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;
    /**档案文件类型(视频、图片、文档等)*/
    private String file_type;
    /**文件时长(视频文件属性)*/
    private Integer file_time;
    /**档案文件大小*/
    private Integer file_size;
    /**档案文件路径*/
    private String file_path;
    /**加密类型*/
    private String encrypt_type;
    /**加密值*/
    private String encrypt_content;
    /**状态(0初始 1删除)*/
    private String status;
    /**remark*/
    private String remark;
    /**创建时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date create_datetime;
    /**执行时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date update_datetime;
    /**文件存储方式*/
    private String storage_type;
    /**归历史标志*/
    private String tohis_flag;
    /**归历史时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("tohis_datetime")
    private Date tohis_datetime;
    @TableField("arch_file_path")
    private String arch_file_path;
    /** 上传文件的url （如cos上传后产生的文件路径） **/
    @TableField("http_url")
    private String http_url;

    /**文件删除标志*/
    private String file_delete_flag;
    /**清算日期*/
    private Integer date_clear;
    public String getFile_suit_path() {
        return StringUtils.isBlank(arch_file_path) ? file_path : arch_file_path;
    }
}