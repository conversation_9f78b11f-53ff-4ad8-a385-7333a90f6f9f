package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 产品适当性信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Prodsuitinfojour")
public class ProdSuitInfoJour extends Model<ProdSuitInfoJour> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 产品适当性信息表ID
     */
    @TableField("prodsuitinfo_id")
    private String prodsuitinfo_id;

    /**
     * 产品代码
     */
    @TableField("prod_code")
    private String prod_code;

    /**
     * 产品TA编号
     */
    @TableField("prodta_no")
    private String prodta_no;

    /**
     * 产品名称
     */
    @TableField("prod_name")
    private String prod_name;

    /**
     * 产品风险等级
     */
    @TableField("risk_level")
    private String risk_level;

    /**
     * 产品投资品种
     */
    @TableField("invest_kind")
    private String invest_kind;

    /**
     * 产品投资期限
     */
    @TableField("invest_term")
    private String invest_term;

    /**
     * 产品最大亏损率
     */
    @TableField("max_deficit_rate")
    private BigDecimal max_deficit_rate;

    /**
     * 产品收益类型
     */
    @TableField("income_type")
    private String income_type;

    /**
     * 产品预期收益
     */
    @TableField("prodpre_income")
    private BigDecimal prodpre_income;

    @TableField(value = "business_flag")
    private Integer business_flag;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
