package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频话术规则配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("videowordsbusiness")
public class VideoWordsBusiness extends Model<VideoWordsBusiness> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "serial_id", type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 视频话术模板id
     */
    @TableField("videowordsmodel_id")
    private String videowordsmodel_id;

    /**
     * 子系统编号
     */
    @TableField("subsys_no")
    private Integer subsys_no;

    /**
     * 产品代码
     */
    @TableField("prod_code")
    private String prod_code;

    /**
     * 产品TA编号
     */
    @TableField("prodta_no")
    private String prodta_no;

    /**
     * 业务编号
     */
    @TableField("busin_type")
    private Integer busin_type;

    /**
     * 机构标识
     */
    @TableField("organ_flag")
    private String organ_flag;//默认0

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "create_datetime", fill = FieldFill.INSERT)
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "modify_datetime", fill = FieldFill.INSERT_UPDATE)
    private Date modify_datetime;

    /**
     * 状态
     */
    @TableField(value = "status", fill = FieldFill.INSERT)
    private String status;

    /**
     * 规则表达式
     */
    @TableField("regular_expre")
    private String regular_expre;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
