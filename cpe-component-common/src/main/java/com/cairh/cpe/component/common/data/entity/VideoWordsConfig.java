package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频话术配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Videowordsconfig")
public class VideoWordsConfig extends Model<VideoWordsConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "serial_id", type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 所属视频话术模板id
     */
    @TableField("videowordsmodel_id")
    private String videowordsmodel_id;

    /**
     * 话术类型
     */
    @TableField("words_type")
    private String words_type;

    /**
     * 话术内容
     */
    @TableField("words_content")
    private String words_content;

    /**
     * 状态
     */
    @TableField(value = "status", fill = FieldFill.INSERT)
    private String status;

    /**
     * 音频文件id
     */
    @TableField("voice_fileid")
    private String voice_fileid;

    /**
     * 排序
     */
    @TableField("order_no")
    private Long order_no;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "create_datetime", fill = FieldFill.INSERT)
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("modify_by")
    private String modify_by;

    /**
     * 答案黑名单
     */
    @TableField("error_answer")
    private String error_answer;

    /**
     * 答案白名单
     */
    @TableField("correct_answer")
    private String correct_answer;
    /**
     * 是否系统播报
     */
    @TableField("auto_play")
    private String auto_play;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "modify_datetime", fill = FieldFill.INSERT_UPDATE)
    private Date modify_datetime;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
