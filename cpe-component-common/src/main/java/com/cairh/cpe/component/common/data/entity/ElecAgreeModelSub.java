package com.cairh.cpe.component.common.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 电子协议模板子表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("elecagreemodelsub")
public class ElecAgreeModelSub extends Model<ElecAgreeModelSub> {

    private static final long serialVersionUID = 5470701147629069223L;

    /**
     * id
     */
    @TableId(value = "serial_id",type = IdType.ASSIGN_UUID)
    private String serial_id;

    /**
     * 协议编号
     */
    @TableField("elecagreemodel_id")
    private String elecagreemodel_id;

    /**
     * 协议内容
     */
    @TableField("agreement_content")
    private String agreement_content;

    /**
     * 协议文件类型
     */
    @TableField("agreement_file_type")
    private String agreement_file_type;

    /**
     * 协议文件ID
     */
    @TableField("agreement_file_id")
    private String agreement_file_id;

    /**
     * 风险揭示提示书页码
     */
    @TableField("serial_number")
    private String serial_number;


    @Override
    public Serializable pkVal() {
        return this.serial_id;
    }

}
