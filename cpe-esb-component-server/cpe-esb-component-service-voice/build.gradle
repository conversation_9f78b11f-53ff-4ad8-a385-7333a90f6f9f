dependencies {
    api(project(':cpe-esb-component-server:cpe-esb-component-core'))
    api(project(':cpe-esb-component-server-api'))
    api(project(':cpe-esb-component-server:cpe-esb-component-file'))


    api(project(':cpe-counter-extension:cpe-counter-extension-component'))

    api('com.iflytek.ai.platform:api-java-sdk:1.0')
    api('net.sourceforge.lame:lame:3.98.4')
//    api('com.cairh:cpe-counter-websocket:0.3.30')

    testImplementation('org.springframework.boot:spring-boot-starter-test')
}