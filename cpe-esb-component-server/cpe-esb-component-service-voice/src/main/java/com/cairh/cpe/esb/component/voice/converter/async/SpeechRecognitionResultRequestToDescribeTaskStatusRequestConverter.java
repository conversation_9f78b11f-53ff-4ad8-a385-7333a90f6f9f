package com.cairh.cpe.esb.component.voice.converter.async;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionResultRequest;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import org.springframework.stereotype.Component;

/**
 * {@link SpeechRecognitionResultRequest} to {@link DescribeTaskStatusRequest} converter
 *
 * <AUTHOR>
 */
@Component
public class SpeechRecognitionResultRequestToDescribeTaskStatusRequestConverter implements Converter<SpeechRecognitionResultRequest, DescribeTaskStatusRequest> {

    @Override
    public DescribeTaskStatusRequest convert(SpeechRecognitionResultRequest source, DescribeTaskStatusRequest target) {
        target.setTaskId(Long.parseLong(source.getTask_id()));

        return target;
    }
}
