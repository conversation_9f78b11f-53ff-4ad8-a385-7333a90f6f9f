package com.cairh.cpe.esb.component.voice.core.concrete.asr;

import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.voice.core.AsrVoiceService;
import com.cairh.cpe.esb.component.voice.core.support.AsrFileDownloadService;
import com.cairh.cpe.esb.component.voice.dto.req.AsrRequest;
import com.cairh.cpe.esb.component.voice.handler.asr.AsrSztYoutuServiceIntroducer;
import com.cairh.cpe.http.data.component.sztyoutu.req.SztyoutuAsrQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * sztyoutu implementation of {@link AsrVoiceService}
 *
 * <AUTHOR>
 */
@Service
public class SztYoutuAsrVoiceService extends AsrSztYoutuServiceIntroducer implements AsrVoiceService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Autowired
    private AsrFileDownloadService asrFileDownloadService;


    @Override
    public String voiceAsr(AsrRequest request) {
        // no request to byte[] allowed
        byte[] apiRequest = asrFileDownloadService.downloadFile(request.getFilerecord_id());
        SztyoutuAsrQueryParam queryParam = converterApplyFactory.convert(request, new SztyoutuAsrQueryParam());
        RepresentWrapper<?> apiResponse = super.asr(apiRequest, queryParam);
        return converterApplyFactory.convert(apiResponse, StrUtil.EMPTY);
    }
}
