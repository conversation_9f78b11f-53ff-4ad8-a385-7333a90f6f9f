package com.cairh.cpe.esb.component.voice.factory;

import com.cairh.cpe.esb.component.core.factory.BaseServiceFactory;
import com.cairh.cpe.esb.component.voice.core.VoiceService;

import java.util.function.Supplier;

/**
 * asr voice implementation of {@link BaseServiceFactory}, simply delegate getVoiceService operation
 * to getAsrVoiceService
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface AsrVoiceServiceFactory extends VoiceServiceFactory {

    @Override
    default VoiceService getVoiceService(Supplier<String> serviceNameSupplier) {
        return getAsrVoiceService(serviceNameSupplier);
    }

    VoiceService getAsrVoiceService(Supplier<String> serviceNameSupplier);
}
