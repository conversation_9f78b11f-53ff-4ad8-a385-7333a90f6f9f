package com.cairh.cpe.esb.component.voice.core.concrete.tts;

import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.voice.core.TtsVoiceService;
import com.cairh.cpe.esb.component.voice.dto.req.TtsRequest;
import com.cairh.cpe.esb.component.voice.handler.tts.TtsSztYoutuServiceIntroducer;
import com.cairh.cpe.http.data.component.sztyoutu.req.SztyoutuTtsRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * sztyoutu implementation of {@link TtsVoiceService}
 *
 * <AUTHOR>
 */
@Service
public class SztYoutuTtsVoiceService extends TtsSztYoutuServiceIntroducer implements TtsVoiceService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;


    @Override
    public byte[] voiceTts(TtsRequest request) {
        SztyoutuTtsRequest apiRequest = converterApplyFactory.convert(request, new SztyoutuTtsRequest());
        RepresentWrapper<?> apiResponse = super.tts(apiRequest);
        return converterApplyFactory.convert(apiResponse, new byte[0]);
    }
}
