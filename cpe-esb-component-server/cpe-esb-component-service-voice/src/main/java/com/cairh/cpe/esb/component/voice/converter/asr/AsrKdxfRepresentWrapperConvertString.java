package com.cairh.cpe.esb.component.voice.converter.asr;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.voice.core.standard.asr.KdxfAsrResponse;
import com.cairh.cpe.esb.component.voice.factory.represent.asr.AsrKdxfRepresentWrapper;
import org.springframework.stereotype.Component;

@Component
public class AsrKdxfRepresentWrapperConvertString implements Converter<AsrKdxfRepresentWrapper, String> {

    @Override
    public String convert(AsrKdxfRepresentWrapper source, String target) {
        KdxfAsrResponse response = source.getObject();

        return response.getLattices().stream().findFirst().get().getOnebest();
    }
}
