package com.cairh.cpe.esb.component.voice.core.concrete.tts;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.template.tencentcloud.TencentCloudService;
import com.cairh.cpe.esb.component.core.template.tencentcloud.manual.TencentCloudActionLookup;
import com.cairh.cpe.esb.component.voice.core.TtsVoiceService;
import com.cairh.cpe.esb.component.voice.dto.req.TtsRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022-07-12
 */
@Slf4j
@Service
public class TencentCloudTtsVoiceService implements TtsVoiceService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Autowired
    private TencentCloudService tencentCloudService;


    @Override
    public byte[] voiceTts(TtsRequest request) {
        String voiceText = request.getVoiceText();
        if(voiceText.length() > 150){
            throw new BizException("文字长度不能超过150字");
        }
        TextToVoiceRequest apiRequest = converterApplyFactory.convert(request, new TextToVoiceRequest());
        JSONObject json = (JSONObject)JSONObject.toJSON(apiRequest);
        for (String key : json.keySet()) {
            Object value = json.get(key);
            if (ObjectUtils.isNotEmpty(value)) {
                String str = value.toString();
                if (str.length() > 500) {
                    json.put(key, "日志内容太长，省略打印");
                }
            }
        }
        log.info("腾讯云TTS调用服务入参，apiRequest:{}", json);
        TextToVoiceResponse apiResponse = (TextToVoiceResponse) tencentCloudService.invoke(TencentCloudActionLookup.TEXT_TO_VOICE_ACTION, apiRequest);
        return converterApplyFactory.convert(apiResponse, new byte[0]);
    }
}