package com.cairh.cpe.esb.component.voice.converter.tts;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.voice.converter.tts.support.AudioParams;
import com.cairh.cpe.esb.component.voice.factory.represent.tts.TtsKdxfRepresentWrapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.util.ByteUtils;
import org.springframework.stereotype.Component;

@Component
public class TtsKdxfRepresentWrapperConvertByteArray implements Converter<TtsKdxfRepresentWrapper, byte[]> {

    @Override
    public byte[] convert(TtsKdxfRepresentWrapper source, byte[] target) {
        String response = source.getObject();

        String[] responseArr = StringUtils.split(response, "\n");
        byte[] wavHeader = createAudio(16, response.length()).createWAVHeader();
        byte[] wavByte = ByteUtils.concat(new byte[0], wavHeader);
        for (String rSec : responseArr) {
            JSONObject rSection = JSON.parseObject(rSec);
            wavByte = ByteUtils.concat(wavByte, Base64.decodeBase64(rSection.getJSONObject("result").getString("data")));
        }

        return wavByte;
    }

    public static AudioParams createAudio(int sampleRate, int length) {
        return new AudioParams(sampleRate * 1000, AudioParams.CHANNEL_MONO, AudioParams.ENCODING_PCM_16BIT, length);
    }
}
