package com.cairh.cpe.esb.component.voice.converter.tts;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.voice.core.standard.tts.SztYoutuTtsResponse;
import com.cairh.cpe.esb.component.voice.factory.represent.tts.TtsSztYoutuRepresentWrapper;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class TtsSztYoutuRepresentWrapperToByteArrayConverter implements Converter<TtsSztYoutuRepresentWrapper, byte[]> {

    @Override
    public byte[] convert(TtsSztYoutuRepresentWrapper source, byte[] target) {
        SztYoutuTtsResponse response = source.getObject();
        String audio = response.getResponse().getAudio();

        return Base64.decodeBase64(audio);
    }
}
