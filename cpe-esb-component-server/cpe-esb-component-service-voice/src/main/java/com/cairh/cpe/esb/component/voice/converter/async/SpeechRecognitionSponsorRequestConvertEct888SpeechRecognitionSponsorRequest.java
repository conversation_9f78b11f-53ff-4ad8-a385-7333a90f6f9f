package com.cairh.cpe.esb.component.voice.converter.async;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadFileResponse;
import com.cairh.cpe.esb.component.voice.core.support.SpeechRecognitionAudioPathThreadLocal;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionSponsorRequest;
import com.cairh.cpe.protocol.gateway.handler.component.ect888.Ect888SpeechRecognitionSponsorRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

@Slf4j
@Component
public class SpeechRecognitionSponsorRequestConvertEct888SpeechRecognitionSponsorRequest implements Converter<SpeechRecognitionSponsorRequest, Ect888SpeechRecognitionSponsorRequest> {

    @DubboReference(lazy = true, check = false, timeout = 5000)
    private IEsbComponentElectDubboService componentElectDubboService;

    @Autowired
    private CompositePropertySources compositePropertySources;


    @SneakyThrows
    @Override
    public Ect888SpeechRecognitionSponsorRequest convert(SpeechRecognitionSponsorRequest source, Ect888SpeechRecognitionSponsorRequest target) {
        ElectDownloadFileRequest electDownloadFileRequest = new ElectDownloadFileRequest();
        electDownloadFileRequest.setFilerecord_id(source.getFilerecord_id());
        ElectDownloadFileResponse electDownloadFileResponse = componentElectDubboService.electDownloadFile(electDownloadFileRequest);

        String filePath = electDownloadFileResponse.getFilePath();
        log.info("档案标识[{}]对应的音频路径:[{}]", source.getFilerecord_id(), filePath);

        String audioTempDir = compositePropertySources.getProperty(ConfigConst.COMP_VOICE_AUDIO_TEMP_DIR);
        Assert.notBlank(audioTempDir, "音频临时目录不能为空");

        String audioTempPath = StringUtils.join(audioTempDir, FileUtil.FILE_SEPARATOR, UUID.randomUUID(true).toString(true), StrUtil.DOT, StrUtil.subAfter(filePath, StrUtil.DOT, true));
        log.info("档案标识[{}]对应的音频临时路径:[{}]", source.getFilerecord_id(), audioTempPath);
        FileCopyUtils.copy(electDownloadFileResponse.getFile(), FileUtil.file(audioTempPath));

        Ect888SpeechRecognitionSponsorRequest ect888SpeechRecognitionSponsorRequest = new Ect888SpeechRecognitionSponsorRequest();
        String audioTempUrl = compositePropertySources.getProperty(ConfigConst.COMP_VOICE_AUDIO_TEMP_URL);
        Assert.notBlank(audioTempUrl, "音频临时URL不能为空");

        String fileLink = StringUtils.join(audioTempUrl, StrUtil.removePrefix(audioTempPath, audioTempDir));
        log.info("档案标识[{}]对应的音频链接地址:[{}]", source.getFilerecord_id(), fileLink);
        ect888SpeechRecognitionSponsorRequest.setFile_link(fileLink);

        SpeechRecognitionAudioPathThreadLocal.setAudioPath(audioTempPath);
        return ect888SpeechRecognitionSponsorRequest;
    }
}
