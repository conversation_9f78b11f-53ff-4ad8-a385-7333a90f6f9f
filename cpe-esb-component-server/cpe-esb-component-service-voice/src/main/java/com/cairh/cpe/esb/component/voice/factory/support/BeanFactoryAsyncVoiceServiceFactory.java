package com.cairh.cpe.esb.component.voice.factory.support;

import com.cairh.cpe.esb.component.voice.core.AsyncVoiceService;
import com.cairh.cpe.esb.component.voice.factory.AsyncVoiceServiceFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import java.util.function.Supplier;

/**
 * default implementation of {@link AsyncVoiceServiceFactory} which use {@link BeanFactory} for
 * detecting service
 *
 * <AUTHOR>
 */
@Service
@Primary
public class BeanFactoryAsyncVoiceServiceFactory extends WebApplicationObjectSupport implements AsyncVoiceServiceFactory {

    @Override
    public AsyncVoiceService getAsyncVoiceService(Supplier<String> serviceNameSupplier) {
        return getWebApplicationContext().getBean(serviceNameSupplier.get(), AsyncVoiceService.class);
    }
}
