package com.cairh.cpe.esb.component.voice.core.standard.async;

import com.cairh.cpe.esb.component.voice.core.standard.async.support.Ect888SpeechRecognitionResultHeader;
import com.cairh.cpe.esb.component.voice.core.standard.async.support.Ect888SpeechRecognitionResultPayload;
import lombok.Data;

@Data
public class Ect888SpeechRecognitionResultResponse {

    private Ect888SpeechRecognitionResultHeader header;

    private Ect888SpeechRecognitionResultPayload payload;
}
