package com.cairh.cpe.esb.component.voice.factory.support;

import com.cairh.cpe.esb.component.voice.core.VoiceService;
import com.cairh.cpe.esb.component.voice.factory.AsrVoiceServiceFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import java.util.function.Supplier;

/**
 * default implementation of {@link AsrVoiceServiceFactory} which use {@link BeanFactory} for
 * detecting service
 *
 * <AUTHOR>
 */
@Service
@Primary
public class BeanFactoryAsrVoiceServiceFactory extends WebApplicationObjectSupport implements AsrVoiceServiceFactory {

    @Override
    public VoiceService getAsrVoiceService(Supplier<String> serviceNameSupplier) {
        return getWebApplicationContext().getBean(serviceNameSupplier.get(), VoiceService.class);
    }
}
