package com.cairh.cpe.esb.component.voice.converter.async;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.voice.core.support.AsrFileDownloadService;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionSponsorRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * {@link SpeechRecognitionSponsorRequest} to {@link CreateRecTaskRequest} converter
 *
 * <AUTHOR>
 */
@Component
public class SpeechRecognitionSponsorRequestToCreateRecTaskRequestConverter implements Converter<SpeechRecognitionSponsorRequest, CreateRecTaskRequest> {

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Autowired
    private AsrFileDownloadService asrFileDownloadService;


    @Override
    public CreateRecTaskRequest convert(SpeechRecognitionSponsorRequest source, CreateRecTaskRequest target) {
        target.setEngineModelType(compositePropertySources.getProperty(ConfigConst.TENCENTCLOUD_ASR_ENGINE_MODEL, "8k_zh"));
        target.setChannelNum(1L);
        target.setResTextFormat(2L);
        target.setSourceType(1L);
        target.setData(Base64.encodeBase64String(asrFileDownloadService.downloadFile(source.getFilerecord_id())));

        // no audio-path generated
        return target;
    }
}
