package com.cairh.cpe.esb.component.file.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.core.util.BaseBeanUtil;
import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.QryFileListByDiyConfResponse;
import com.cairh.cpe.esb.component.elect.dto.resp.UploadImageByDiyConfResponse;
import com.cairh.cpe.esb.component.file.model.*;
import com.cairh.cpe.esb.component.file.service.IFileOperationService;
import com.cairh.cpe.esb.component.file.utils.FileHelper;
import com.cairh.cpe.esb.component.file.utils.FileUploadUtils;
import com.cairh.cpe.util.file.MimeTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Lazy
@Slf4j
@Service
public class OssOperationService implements IFileOperationService {

    public static OSS getOssClient(OssProperties config){
        OSS ossClient = new OSSClientBuilder().build(config.getEndpoint(), config.getAccessKeyId(),
                config.getAccessKeySecret());
        return ossClient;
    }

    public static String getBaseUrl(OssProperties config){
        return StringUtils.isNotBlank(config.getResUrl()) ?
                config.getResUrl():
                "https://"+config.getBucketName() + "." +config.getEndpoint();
    }

    @Override
    public FileOperationSaveResp saveFile(FileOperationSaveRequest request) {
        return null;
    }

    @Override
    public byte[] readFile(String filePath) {
        return new byte[0];
    }

    @Override
    public void deleteFile(String filePath) {

    }

    @Override
    public FileOperationDiySaveResp saveFileByDiyConf(FileOperationDiySaveRequest request) {
        byte[] file = request.getFileContent();
        if(ArrayUtils.isEmpty(file)){
            throw new BizException("上传文件为空");
        }
        String fileName = request.getFileName();
        OssProperties config = JSONObject.parseObject(request.getStorage_properties(), OssProperties.class);
        OSS client = getOssClient(config);
        String dir = request.getDir();
        client.putObject(config.getBucketName(), dir + StrUtil.SLASH + fileName, new ByteArrayInputStream(file));
        String baseUrl = getBaseUrl(config);
        FileOperationDiySaveResp response = new FileOperationDiySaveResp();
        String purl = baseUrl + StrUtil.SLASH + dir + StrUtil.SLASH;
        response.setPurl(purl);
        response.setServerFileName(dir + StrUtil.SLASH + fileName);
        String url = purl + fileName;
        response.setUrl(url);
        client.shutdown();
        return response;
    }

    public static String getLocalPath(String httpaddress){
        if(httpaddress.startsWith(StrUtil.SLASH)){
            return httpaddress.substring(1);
        }
        return httpaddress;
    }

    @Override
    public QryFileListByDiyConfResponse listFileByDiyConf(QryFileListByDiyConfRequest req) {
        QryFileListByDiyConfResponse response = new QryFileListByDiyConfResponse();
        String path = getLocalPath(req.getHttpaddress());
        List<String> ret = fileList(path,req.getIncludeThumbnail(),JSONObject.parseObject(req.getParam_json(),OssProperties.class));
        response.setFile_path_list(ret);
        return response;
    }

    private static List<String> fileList(String path, boolean includeThumbnail,
                                  OssProperties config) {
        List<String> ret = new ArrayList<>();
        ListObjectsRequest request = new ListObjectsRequest();
        request.setBucketName(config.getBucketName());
        request.setPrefix(path);
        request.setMaxKeys(100);
        OSS client = getOssClient(config);
        ObjectListing objectListing = client.listObjects(request);
        List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
        for (OSSObjectSummary s : sums) {
            if(s.getKey().indexOf("/thumbnail/")!=-1){
                if(!includeThumbnail) {
                    //跳过缩略图
                    continue;
                }
            }
            ret.add(s.getKey());
        }
        client.shutdown();
        return ret;
    }

    @Override
    public void copyFileByDiyConf(CopyFileByDiyConfRequest request) {
        copyFile(request.getFromhttpaddress(), request.getTohttpaddress(), JSONObject.parseObject(request.getParam_json(), OssProperties.class));
    }

    public static void copyFile(String fromhttpaddress, String tohttpaddress, OssProperties config) {
        String frompath = getLocalPath(fromhttpaddress);
        log.info("来源对象路径："+frompath);
        String topath = getLocalPath(tohttpaddress);
        log.info("目标对象路径："+topath);
        if(frompath.equals(topath)){
            throw new BizException("源地址不能与目标地址一致");
        }

        if (frompath.endsWith(StrUtil.DOT) || frompath.endsWith(StrUtil.SLASH)) {
            System.out.println("复制的是文件夹");

            List<String> list = fileList(fromhttpaddress, true, config);
            for (String s : list) {
                String fileName = s.substring(frompath.length());
                copyFile(s, FileHelper.addPathDirEnd(tohttpaddress)+fileName, config);
            }

        }else {
            OSS client = getOssClient(config);
            client.copyObject(config.getBucketName(), frompath, config.getBucketName(), topath);
            String thumbhttpaddress = haveImage(fromhttpaddress, config);
            if (null != thumbhttpaddress) {
                String fileName = FileHelper.getHttpFileName(tohttpaddress);
                String rpath = topath;
                int index = rpath.lastIndexOf("/");
                if (index == -1) return;
                rpath = rpath.substring(0, index);
                client.copyObject(config.getBucketName(), getLocalPath(thumbhttpaddress),
                        config.getBucketName(), rpath+"/thumbnail/"+fileName);
                client.shutdown();
            }
        }

    }

    @Override
    public void deleteFileByDiyConf(DeleteFileByDiyConfRequest request) {
        String path = getLocalPath(request.getHttpaddress());
        log.info("删除对象路径:{}",path);
        OssProperties config = JSONObject.parseObject(request.getParam_json(), OssProperties.class);
        deleteFile(path, config);
    }

    private void deleteFile(String path, OssProperties config) {
        if (path.endsWith(StrUtil.DOT) || path.endsWith(StrUtil.SLASH)) {
            log.info("删除的是文件夹");
            while(true){
                List<String> list = fileList(path, false, config);
                if(list.isEmpty()){
                    break;
                }
                for (String s : list) {
                    deleteFile(s, config);
                }
            }
        }else{
            OSS client = getOssClient(config);
            log.info("删除对象："+ path);
            client.deleteObject(config.getBucketName(), path);
            String thumbhttpaddress = haveImage(path, config);
            if(null != thumbhttpaddress) {
                client.deleteObject(config.getBucketName(), getLocalPath(thumbhttpaddress));
            }
            client.shutdown();
        }
    }

    public static String haveImage(String httpaddress, OssProperties config){
        String fileExtension = FileHelper.getHttpImageFileExtensionName(httpaddress);
        boolean isImage = Arrays.asList(MimeTypeUtils.IMAGE_EXTENSION).contains(fileExtension);
        if(isImage){
            int index = httpaddress.lastIndexOf(StrUtil.SLASH);
            String preDir = httpaddress.substring(0,index+1);
            String fileName = httpaddress.substring(index+1);
            String thumbhttpaddress = preDir + "thumbnail/" + fileName;
            boolean isExist = isExist(thumbhttpaddress, config);
            if(isExist){
                return thumbhttpaddress;
            }
        }
        return null;
    }

    public static boolean isExist(String httpaddress, OssProperties config) {
        String path = getLocalPath(httpaddress);
        OSS client = getOssClient(config);
        boolean b = client.doesObjectExist(config.getBucketName(), path);
        client.shutdown();
        return b;
    }

    @Override
    public UploadImageByDiyConfResponse uploadImageByDiyConf(UploadImageByDiyConfRequest request) {
        byte[] file = request.getFile();
        if(ArrayUtils.isEmpty(file)){
            throw new BizException("上传文件为空");
        }
        String fileName = request.getName();
        if (!isImageFile(fileName)) {
            FileOperationDiySaveRequest fileOperationDiySaveRequest = new FileOperationDiySaveRequest();
            fileOperationDiySaveRequest.setFileName(fileName);
            fileOperationDiySaveRequest.setFileContent(request.getFile());
            fileOperationDiySaveRequest.setStorage_properties(request.getParam_json());
            FileOperationDiySaveResp fileOperationDiySaveResp = saveFileByDiyConf(fileOperationDiySaveRequest);
            return BaseBeanUtil.copyProperties(fileOperationDiySaveResp, UploadImageByDiyConfResponse.class);
        }
        byte[] image = new byte[0];
        try {
            image = FileUploadUtils.getPicByteForScaling(file, request.getMaxWidth(), request.getQuality());
        } catch (Exception e) {
            throw new BizException("图片上传失败",e);
        }
        OssProperties config = JSONObject.parseObject(request.getParam_json(), OssProperties.class);
        OSS client = getOssClient(config);
        String dir = request.getDir();
        client.putObject(config.getBucketName(), dir + StrUtil.SLASH + fileName, new ByteArrayInputStream(image));
        String baseUrl = getBaseUrl(config);
        UploadImageByDiyConfResponse response = new UploadImageByDiyConfResponse();
        String purl = baseUrl + StrUtil.SLASH + dir + StrUtil.SLASH;
        response.setPurl(purl);
        response.setServerFileName(dir + StrUtil.SLASH + fileName);
        String url = purl + fileName;
        response.setUrl(url);
        client.shutdown();
        return response;
    }

    @Override
    public boolean isFileExist(FileExistRequest request) {
        return isExist(request.getFilePath(), JSONObject.parseObject(request.getParam_json(), OssProperties.class));
    }

    private boolean isImageFile(String file_name) {
        for (String type : MimeTypeUtils.IMAGE_EXTENSION) {
            if (file_name.endsWith(type)) {
                return true;
            }
        }
        return false;
    }
}