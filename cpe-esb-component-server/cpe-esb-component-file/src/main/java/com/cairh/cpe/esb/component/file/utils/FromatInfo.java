package com.cairh.cpe.esb.component.file.utils;

import java.util.Map;

/**
 * 功能说明: 格式信息<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2018年12月12日<br>
 */
public class FromatInfo {
	
	/**
	 * 系统默认字体大小
	 */
	private String defaultFontSize;
	/**
	 * 左边距
	 */
	private String leftMargin;
	/**
	 * 右边距
	 */
	private String rightMargin;
	
	/**
	 * 字体大小
	 */
	Map<String, String> fontSize;

	public String getDefaultFontSize() {
		return defaultFontSize;
	}

	public void setDefaultFontSize(String defaultFontSize) {
		this.defaultFontSize = defaultFontSize;
	}

	public String getLeftMargin() {
		return leftMargin;
	}

	public void setLeftMargin(String leftMargin) {
		this.leftMargin = leftMargin;
	}

	public String getRightMargin() {
		return rightMargin;
	}

	public void setRightMargin(String rightMargin) {
		this.rightMargin = rightMargin;
	}

	public Map<String, String> getFontSize() {
		return fontSize;
	}

	public void setFontSize(Map<String, String> fontSize) {
		this.fontSize = fontSize;
	}
	
	
}