package com.cairh.cpe.esb.component.file.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.util.FileCopyUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 图片转PDT工具类
 */
public class Image2PdfUtil {

    public static void toPdf(String[] imagePaths, String pdfPath) throws IOException {
        try (PDDocument doc = new PDDocument()) {
            for (String imagePath : imagePaths) {
                BufferedImage image = ImageIO.read(new File(imagePath));
                PDPage page = new PDPage(new PDRectangle(image.getWidth(), image.getHeight()));
                doc.addPage(page);

                try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                    PDImageXObject pdImage = PDImageXObject.createFromFile(imagePath, doc);
                    contents.drawImage(pdImage, 0, 0, pdImage.getWidth(), pdImage.getHeight());
                }
            }
            doc.save(pdfPath);
        }
    }

    public static byte[] toPdf(byte[] ...images) throws IOException {
        try (PDDocument doc = new PDDocument()) {
            for (byte[] imageBytes : images) {
                BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
                PDPage page = new PDPage(new PDRectangle(image.getWidth(), image.getHeight()));
                doc.addPage(page);

                try (PDPageContentStream contents = new PDPageContentStream(doc, page)) {
                    PDImageXObject pdImage = PDImageXObject.createFromByteArray(doc, imageBytes, "image");
                    contents.drawImage(pdImage, 0, 0, pdImage.getWidth(), pdImage.getHeight());
                }
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            doc.save(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }

    public static byte[] toPdf(File ...imageFiles) throws IOException {
        List<byte[]> byteList = Stream.of(imageFiles).map(f -> {
            try {
                return FileCopyUtils.copyToByteArray(f);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
        return toPdf(byteList.toArray(new byte[0][0]));
    }

    public static byte[] toPdf(String ...imageBase64s) throws IOException {
        List<byte[]> byteList = Stream.of(imageBase64s).map(ImageBase64Util::getStrToBytes).collect(Collectors.toList());
        return toPdf(byteList.toArray(new byte[0][0]));
    }

    public static byte[] toPdf(Collection<String> imageBase64s) throws IOException {
        return toPdf(imageBase64s.toArray(new String[0]));
    }

    public static void main(String[] args) throws IOException {

        File[] paths = {
                new File("D:\\web\\qingtou\\5\\8kyib-20240524.jpeg"),
                new File("D:\\web\\qingtou\\5\\qgZdm-20240524.jpeg")
        };
        byte[] pdf = toPdf(paths);
        FileCopyUtils.copy(pdf, new File("D:\\web\\qingtou\\5\\t.pdf"));
    }

}
