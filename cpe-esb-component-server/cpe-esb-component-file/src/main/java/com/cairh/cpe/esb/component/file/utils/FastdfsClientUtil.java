package com.cairh.cpe.esb.component.file.utils;

import java.util.HashMap;
import java.util.Map;
import com.cairh.cpe.esb.component.file.utils.CoreStdErrorNo;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.file.utils.FastdfsStorageClientFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.csource.common.IniFileReader;
import org.csource.common.NameValuePair;
import org.csource.fastdfs.ClientGlobal;
import org.csource.fastdfs.FileInfo;
import org.csource.fastdfs.StorageClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FastdfsClientUtil {

	protected static Logger logger = LoggerFactory.getLogger(FastdfsClientUtil.class);

//	private static GenericObjectPool<TrackerServer> trackerServerPool;
	private static GenericObjectPool<StorageClient> storageClientPool;
//
//	/**
//	 * 获取 trackerServer 连接实例
//	 * @return
//	 */
//	public static synchronized GenericObjectPool<TrackerServer> getTrackerPool() {
//		if (trackerServerPool != null) {
//			return trackerServerPool;
//		}
//		Map<String, Object> configMap = parseConfig();
//		try
//		{
//			ClientGlobal.init(EnvironmentUtils.getFileAbsolutePath("/fastdfs/client.conf"));
//		}
//		catch (Exception e) {
//			logger.warn("fastdfs初始化配置发送错误", e);
//		}
//
//		FastdfsTrackerServerFactory factory = new FastdfsTrackerServerFactory();
//		GenericObjectPoolConfig config = new GenericObjectPoolConfig();
//		// 最大连接数, 默认8个
//		config.setMaxTotal(Integer.parseInt((String)configMap.get("maxConn").toString()));
//		// 最大空闲连接数, 默认8个
//		config.setMaxIdle(Integer.parseInt((String)configMap.get("maxIdle").toString()));
//		// 获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常,
//		// 小于零:阻塞不确定的时间, 默认-1
//		config.setMaxWaitMillis(Long.parseLong((String)configMap.get("maxBusyTime").toString()));
//		try {
//			trackerServerPool = new GenericObjectPool<TrackerServer>(factory,config);
////			logger.info(String.format("连接trackerServer服务器"));
//		} catch (Exception e) {
//			logger.warn("获取trackerServer服务发生错误", e);
//		}
//		return trackerServerPool;
//	}

	/**
	 * 获取 StorageClient 连接实例
	 * @return
	 */
	public static synchronized GenericObjectPool<StorageClient> getStoragePool() {
		if (storageClientPool != null) {
			return storageClientPool;
		}
		try
		{
			ClientGlobal.init(EnvironmentUtils.getFileAbsolutePath("/fastdfs/client.conf"));
		}
		catch (Exception e) {
			logger.warn("fastdfs初始化配置发送错误", e);
			return null;
		}
		FastdfsStorageClientFactory factory = new FastdfsStorageClientFactory();
		GenericObjectPoolConfig config = new GenericObjectPoolConfig();
		Map<String, Object> configMap = parseConfig();
		// 最大连接数, 默认8个
		config.setMaxTotal(Integer.parseInt((String)configMap.get("maxConn").toString()));
		// 最大空闲连接数, 默认8个
		config.setMaxIdle(Integer.parseInt((String)configMap.get("maxIdle").toString()));
		// 获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常,
		// 小于零:阻塞不确定的时间, 默认-1
		config.setMaxWaitMillis(Long.parseLong((String)configMap.get("maxBusyTime").toString()));
		try {
			storageClientPool = new GenericObjectPool<StorageClient>(factory,config);
//			logger.info(String.format("连接StorageServer服务器"));
		} catch (Exception e) {
			logger.warn("获取storageServer服务发生错误", e);
		}
		return storageClientPool;
	}

	/**
	 * 解析fastdfs配置
	 * @return
	 */
	public static Map<String, Object> parseConfig() {
		Map<String, Object> map = new HashMap<String, Object>();
		String filePath;
		try {
			filePath = EnvironmentUtils.getFileAbsolutePath("/fastdfs/client.conf");
			IniFileReader iniReader = new IniFileReader(filePath);
			map.put("maxConn", iniReader.getIntValue("maxConn",8));
			map.put("maxIdle", iniReader.getIntValue("maxIdle",8));
			map.put("maxBusyTime", iniReader.getIntValue("maxBusyTime",-1));

		} catch (Exception e) {
			logger.warn("fastdfs 未配置，请正确配置", e);
			System.exit(0);
		}
		return map;
	}


	/**
	 * 关闭连接池
	 */
	public static void close() {
		if (storageClientPool != null) {
			storageClientPool.close();
		}
	}

//	/**
//	 * 上传文件
//	 */
//	public static String[] uploadFile(String group_name,byte[] fileContent, String extName, NameValuePair[] metas) {
//		String[] reStr = null;
//		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
//		if (storageClientPool == null) {
//			return reStr;
//		}
//		StorageClient storageClient = null;
//		try {
//			storageClient = storageClientPool.borrowObject();
//			reStr = storageClient.upload_file(group_name,fileContent, extName, metas);
//		} catch (Exception e) {
//			logger.warn(e.getMessage(), e);
//		} finally {
//			if(storageClient != null){
//				storageClientPool.returnObject(storageClient);
//			}
//		}
//	   return reStr;
//	}
//
//	/**
//	 * 上传文件
//	 */
//	public static String[] uploadFile(byte[] fileContent, String extName, NameValuePair[] metas) {
//		String[] reStr = null;
//		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
//		if (storageClientPool == null) {
//			return reStr;
//		}
//		StorageClient storageClient = null;
//		try {
//			storageClient = storageClientPool.borrowObject();
//			reStr = storageClient.upload_file(fileContent, extName, metas);
//		} catch (Exception e) {
//			logger.warn(e.getMessage(), e);
////			e.printStackTrace();
//		} finally {
//			if(storageClient != null){
//				storageClientPool.returnObject(storageClient);
//			}
//		}
//	   return reStr;
//	}
//

	/**
	 * 上传文件
	 */
	public static String[] uploadFile(String group_name, String remote_filename,byte[] fileContent, String fileName, NameValuePair[] metas) {
		String[] reStr = null;
		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
		if (storageClientPool == null) {
			return reStr;
		}

		StorageClient storageClient = null;
		try {
			storageClient = storageClientPool.borrowObject();
			if(StringUtils.isNotBlank(group_name) && StringUtils.isNotBlank(remote_filename)){
				FileInfo fi = storageClient.query_file_info(group_name, remote_filename);
				if( fi != null){
					if(storageClient.delete_file(group_name, remote_filename) != 0){
						throw new Exception("文件删除失败");
					}
				}
			}

			if(metas == null){
				metas = new NameValuePair[]{
		                new NameValuePair("filename", fileName)
		        };
			}
			else{
//				List<NameValuePair> lsmetas = java.util.Arrays.asList(metas);
//				lsmetas.add(new NameValuePair("filename", fileName));
//				metas = (NameValuePair[]) lsmetas.toArray();
			}

			if(StringUtils.isNotBlank(group_name)){
				reStr = storageClient.upload_file(group_name,fileContent, FileUtil.getFileExt(fileName), metas);
			}
			else{
				reStr = storageClient.upload_file(fileContent, FileUtil.getFileExt(fileName), metas);
			}

		} catch (Exception e) {
			logger.warn(e.getMessage(), e);
            throw new BizException(CoreStdErrorNo.FASTDFS_UPLOAD_ERROR, "fastdfs上传文件失败", e);
//			e.printStackTrace();
		} finally {
			if(storageClient != null){
				storageClientPool.returnObject(storageClient);
			}
		}
	   return reStr;
	}

	/**
	 * 删除文件
	 */
	public static int delete_file(String group_name, String remote_filename) {
		int reInt = 0;
		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
		if (storageClientPool == null) {
			return reInt;
		}
		StorageClient storageClient = null;
		try {
			storageClient = storageClientPool.borrowObject();
			reInt = storageClient.delete_file(group_name, remote_filename);
		} catch (Exception e) {
			logger.warn(e.getMessage(), e);
//			e.printStackTrace();
		} finally {
			if(storageClient != null){
				storageClientPool.returnObject(storageClient);
			}
		}
	   return reInt;
	}

	/**
	 *下载文件
	 */
	public static byte[] download_file(String group_name, String remote_filename,	Map<String, Object> resMap) {
		byte[] fileContent = null;
		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
		if (storageClientPool == null) {
			return fileContent;
		}
		StorageClient storageClient = null;
		try {
			storageClient = storageClientPool.borrowObject();
			if(resMap != null){
				NameValuePair nvps [] = storageClient.get_metadata(group_name, remote_filename);
				if(nvps != null){
					 for(NameValuePair nvp : nvps){
						 resMap.put(nvp.getName() , nvp.getValue());
			         }
				}
			}
			fileContent = storageClient.download_file(group_name, remote_filename);
		} catch (Exception e) {
			logger.warn(e.getMessage(), e);
//			e.printStackTrace();
		} finally {
			if(storageClient != null){
				storageClientPool.returnObject(storageClient);
			}
		}
	   return fileContent;
	}

	public static byte[] download_file(String group_name, String remote_filename,	long start,long length,Map<String, Object> resMap) {
 		byte[] fileContent = null;
 		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
 		if (storageClientPool == null) {
 			return fileContent;
 		}
 		StorageClient storageClient = null;
 		try {
 			storageClient = storageClientPool.borrowObject();
 			if(resMap != null){
 				NameValuePair nvps [] = storageClient.get_metadata(group_name, remote_filename);
 				if(nvps != null){
 					 for(NameValuePair nvp : nvps){
 						 resMap.put(nvp.getName() , nvp.getValue());
 			         }
 				}
 			}
 			fileContent = storageClient.download_file(group_name, remote_filename, start, length);
 //			fileContent = storageClient.download_file(group_name, remote_filename);
 		} catch (Exception e) {
 			logger.warn(e.getMessage(), e);
 //			e.printStackTrace();
 		} finally {
 			if(storageClient != null){
 				storageClientPool.returnObject(storageClient);
 			}
 		}
 	   return fileContent;
 	}

	/**
	 *下载文件
	 */
	public static int download_file(String group_name, String remote_filename,	String real_filepath,Map<String, Object> resMap) {
		int reInt = -1;
		GenericObjectPool<StorageClient> storageClientPool = getStoragePool();
		if (storageClientPool == null) {
			return reInt;
		}
		StorageClient storageClient = null;
		try {
			storageClient = storageClientPool.borrowObject();
			if(resMap != null){
				NameValuePair nvps [] = storageClient.get_metadata(group_name, remote_filename);
				if(nvps != null){
					 for(NameValuePair nvp : nvps){
						 resMap.put(nvp.getName() , nvp.getValue());
			         }
				}
			}
			reInt = storageClient.download_file(group_name, remote_filename,real_filepath);
		} catch (Exception e) {
			logger.warn(e.getMessage(), e);
//			e.printStackTrace();
		} finally {
			if(storageClient != null){
				storageClientPool.returnObject(storageClient);
			}
		}
	   return reInt;
	}
}
