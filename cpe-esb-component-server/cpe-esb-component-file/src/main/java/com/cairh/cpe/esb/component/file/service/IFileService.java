package com.cairh.cpe.esb.component.file.service;

import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.*;

public interface IFileService {

    ElectUploadFileResponse doElectUploadFile(ElectUploadFileRequest request) ;

    ElectUploadFileByUriResponse doElectUploadFileByUri(ElectUploadFileByUriRequest request);

    ElectDownloadFileResponse doElectDownloadFile(ElectDownloadFileRequest request);

    ElectUploadImageResponse doElectUploadImage(ElectUploadImageRequest request);

    ElectDownloadImageResponse doElectDownloadImage(ElectDownloadImageRequest request);

    ElectDownloadImageMultResponse doElectDownloadImageMult(ElectDownloadImageMultRequest request);

    void doElectFilePathUpdateByNewPath(String oldFilerecordId ,String newFilerecordId);

    ElectUploadImageByUriResponse doElectUploadImageByUri(ElectUploadImageByUriRequest request);

    ElectRotateImageResponse doElectRotateImage(ElectRotateImageRequest request);

    /**
     * 等比例压缩图片 如果当前图片大小符合要求则不进行压缩
     * @param filerecord_id 文件id
     * @param target_kb 目标大小 kb
     * @return
     */
    String compressImg(String filerecord_id, String target_kb);

    void doElectDeleteFile(ElectDeleteFileRequest request);

    UploadFileByDiyConfResponse uploadFileByDiyConf(UploadFileByDiyConfRequest request);

    QryFileListByDiyConfResponse qryFileListByDiyConf(QryFileListByDiyConfRequest request);

    void copyFileByDiyConf(CopyFileByDiyConfRequest request);

    void deleteFileByDiyConf(DeleteFileByDiyConfRequest request);

    UploadImageByDiyConfResponse uploadImageByDiyConf(UploadImageByDiyConfRequest request);

    ElectUploadFileToVenderResponse electUploadFileToVender(ElectUploadFileToVenderRequest request);

    FileExistResponse isFileExist(FileExistRequest request);
}
