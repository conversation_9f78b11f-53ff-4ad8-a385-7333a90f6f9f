package com.cairh.xpe.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 功能说明: <br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2015年8月27日<br>
 */
public class ScaleImageUtil {

    private static double support = (double)3.00;
    private static double PI = Math.PI;
    static Logger logger = LoggerFactory.getLogger(ScaleImageUtil.class);

    /**
     * 判断给定图片的宽或高是否超过给定值
     * @param maxWidth
     * @param maxHeight
     * @param srcFile
     * @return
     */
    public static boolean isWidthOrHeightOver(int maxWidth, int maxHeight, File srcFile) {
        boolean over = false;
        BufferedImage srcImage;
        try {
            srcImage = ImageIO.read(srcFile);
            int imageWidth = srcImage.getWidth(null);
            int imageHeight = srcImage.getHeight(null);
            if (maxWidth < 1 && (maxHeight > 0 && imageHeight > maxHeight)) {
                over = true;
            } else if (maxHeight < 1 && (maxWidth > 0 && imageWidth > maxWidth)) {
                over = true;
            } else if ((maxWidth > 0 && imageWidth > maxWidth) || (maxHeight > 0 && imageHeight > maxHeight)) {
                over = true;
            }
        } catch (IOException e) {
            logger.error("IO流异常", e);
        }
        return over;
    }

    /**
     * 按宽度等比缩放图片
     * @param saveFile
     * @param w
     * @param format
     * @param srcFile
     */
    public static void jpgImgZoomOutByWidth(File saveFile, int w, String format, File srcFile) {
        BufferedImage srcImage;
        try {
            srcImage = ImageIO.read(srcFile);
            int imageWidth = srcImage.getWidth(null);
            int imageHeight = srcImage.getHeight(null);

            double rate = w / (double)imageWidth;
            int h = (int)(imageHeight * rate);
            srcImage = imageZoomOut(srcImage, w, h, true);
            ImageIO.write(srcImage, format, saveFile);
        } catch (IOException e1) {
            logger.error("IO流异常", e1);
        }
    }

    /**
     * 保持原先的方法 默认等比缩放
     * @param saveFile
     * @param formatWidth
     * @param formatHeight
     * @param format
     * @param isScale
     * @param srcFile
     * @throws IOException
     */
    public static void saveImageAsJpg(File saveFile, int formatWidth, int formatHeight, String format, boolean isScale, File srcFile) throws IOException {
        int changeToWidth = 0;
        int changeToHeight = 0;
        BufferedImage srcImage = ImageIO.read(srcFile);
        if (srcImage == null) {
            return;
        }
        int imageWidth = srcImage.getWidth(null);
        int imageHeight = srcImage.getHeight(null);
        if (isScale && formatWidth != 0) {
            int max = imageWidth > imageHeight ? imageWidth : imageHeight;// 取最大边
            double d = max > formatWidth ? (max / (double)formatWidth) : ((double)formatWidth / max);// 等比系数
            boolean flag = max > formatWidth ? false : true;// 放大true OR 缩小false
            if (flag) {
                // 等比放大
                changeToWidth = (int)(imageWidth * d);
                changeToHeight = (int)(imageHeight * d);
            } else {
                // 等比缩小
                changeToWidth = (int)(imageWidth / d);
                changeToHeight = (int)(imageHeight / d);
            }
            // 指定图像缩放大小
        } else {
            if (formatWidth == 0) {
                changeToWidth = imageWidth;
            } else {
                changeToWidth = formatWidth;
            }

            if (formatHeight == 0) {
                changeToHeight = imageHeight;
            } else {
                changeToHeight = formatHeight;
            }
        }
        srcImage = imageZoomOut(srcImage, changeToWidth, changeToHeight, isScale);
        ImageIO.write(srcImage, format, saveFile);

    }

    /**
     * 压缩图片
     * @param srcBufferImage
     * @param w
     * @param h
     * @param isScale
     * @return
     */
    public static BufferedImage imageZoomOut(BufferedImage srcBufferImage, int w, int h, boolean isScale) {

        int width = srcBufferImage.getWidth();
        int height = srcBufferImage.getHeight();
        int scaleWidth = w;

        if (isScale && DetermineResultSize(w, h, width, height) == 1) {
            return srcBufferImage;
        }
        Map<String, Object> map = CalContrib(width, scaleWidth);
        BufferedImage pbOut = HorizontalFiltering(srcBufferImage, w, map);
        BufferedImage pbFinalOut = VerticalFiltering(pbOut, h, map);
        return pbFinalOut;
    }

    /**
     * 决定图像尺寸
     */
    private static int DetermineResultSize(int w, int h, int width, int height) {
        double scaleH, scaleV;
        scaleH = (double)w / (double)width;
        scaleV = (double)h / (double)height;
        if (scaleH >= 1.0 && scaleV >= 1.0) {
            return 1;
        }
        return 0;
    }

    private static Map<String, Object> CalContrib(int width, int scaleWidth) {
        Map<String, Object> map = new HashMap<String, Object>();
        int nHalfDots = (int)((double)width * support / (double)scaleWidth);
        int nDots = nHalfDots * 2 + 1;
        double[] contrib = null;
        double[] normContrib = null;
        double[] tmpContrib = null;
        try {
            contrib = new double[nDots];
            normContrib = new double[nDots];
            tmpContrib = new double[nDots];
        } catch (Exception e) {}
        int center = nHalfDots;
        contrib[center] = 1.0;
        double weight = 0.0;
        int i = 0;
        for (i = 1; i <= center; i++) {
            contrib[center + i] = Lanczos(i, width, scaleWidth, support);
            weight += contrib[center + i];
        }
        for (i = center - 1; i >= 0; i--) {
            contrib[i] = contrib[center * 2 - i];
        }
        weight = weight * 2 + 1.0;
        for (i = 0; i <= center; i++) {
            normContrib[i] = contrib[i] / weight;
        }
        for (i = center + 1; i < nDots; i++) {
            normContrib[i] = normContrib[center * 2 - i];
        }
        map.put("nHalfDots", nHalfDots);
        map.put("nDots", nDots);
        map.put("tmpContrib", tmpContrib);
        map.put("normContrib", normContrib);
        map.put("contrib", contrib);
        return map;
    }

    private static double Lanczos(int i, int inWidth, int outWidth, double Support) {
        double x = (double)i * (double)outWidth / (double)inWidth;
        return Math.sin(x * PI) / (x * PI) * Math.sin(x * PI / Support) / (x * PI / Support);
    }

    // 图片水平滤波
    private static BufferedImage HorizontalFiltering(BufferedImage bufImage, int iOutW, Map<String, Object> map) {
        int nHalfDots = (int)map.get("nHalfDots");
        int nDots = (int)map.get("nDots");
        double[] tmpContrib = (double[])map.get("tmpContrib");
        double[] normContrib = (double[])map.get("normContrib");
        double[] contrib = (double[])map.get("contrib");

        int dwInW = bufImage.getWidth();
        int dwInH = bufImage.getHeight();
        int value = 0;
        BufferedImage pbOut = new BufferedImage(iOutW, dwInH, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < iOutW; x++) {
            int startX;
            int start;
            int X = (int)(((double)x) * ((double)dwInW) / ((double)iOutW) + 0.5);
            int y = 0;
            startX = X - nHalfDots;
            if (startX < 0) {
                startX = 0;
                start = nHalfDots - X;
            } else {
                start = 0;
            }
            int stop;
            int stopX = X + nHalfDots;
            if (stopX > (dwInW - 1)) {
                stopX = dwInW - 1;
                stop = nHalfDots + (dwInW - 1 - X);
            } else {
                stop = nHalfDots * 2;
            }
            if (start > 0 || stop < nDots - 1) {
                CalTempContrib(start, stop, contrib, tmpContrib);
                for (y = 0; y < dwInH; y++) {
                    value = HorizontalFilter(bufImage, startX, stopX, start, stop, y, tmpContrib);
                    pbOut.setRGB(x, y, value);
                }
            } else {
                for (y = 0; y < dwInH; y++) {
                    value = HorizontalFilter(bufImage, startX, stopX, start, stop, y, normContrib);
                    pbOut.setRGB(x, y, value);
                }
            }
        }
        return pbOut;
    }

    // 处理边缘
    private static void CalTempContrib(int start, int stop, double[] contrib, double[] tmpContrib) {
        double weight = 0;
        int i = 0;
        for (i = start; i <= stop; i++) {
            weight += contrib[i];
        }
        for (i = start; i <= stop; i++) {
            tmpContrib[i] = contrib[i] / weight;
        }
    }

    /** 行水平滤波* */
    private static int HorizontalFilter(BufferedImage bufImg, int startX, int stopX, int start, int stop, int y, double[] pContrib) {
        double valueRed = 0.0;
        double valueGreen = 0.0;
        double valueBlue = 0.0;
        int valueRGB = 0;
        int i, j;
        for (i = startX, j = start; i <= stopX; i++, j++) {
            valueRGB = bufImg.getRGB(i, y);
            valueRed += GetRedValue(valueRGB) * pContrib[j];
            valueGreen += GetGreenValue(valueRGB) * pContrib[j];
            valueBlue += GetBlueValue(valueRGB) * pContrib[j];
        }
        valueRGB = ComRGB(Clip((int)valueRed), Clip((int)valueGreen), Clip((int)valueBlue));
        return valueRGB;
    }

    private static int GetRedValue(int rgbValue) {
        int temp = rgbValue & 0x00ff0000;
        return temp >> 16;
    }

    private static int GetGreenValue(int rgbValue) {
        int temp = rgbValue & 0x0000ff00;
        return temp >> 8;
    }

    private static int GetBlueValue(int rgbValue) {
        return rgbValue & 0x000000ff;
    }

    private static int Clip(int x) {
        if (x < 0) return 0;
        if (x > 255) return 255;
        return x;
    }

    private static int ComRGB(int redValue, int greenValue, int blueValue) {
        return (redValue << 16) + (greenValue << 8) + blueValue;
    }

    private static BufferedImage VerticalFiltering(BufferedImage pbImage, int iOutH, Map<String, Object> map) {
        int iW = pbImage.getWidth();
        int iH = pbImage.getHeight();
        int value = 0;
        int nHalfDots = (int)map.get("nHalfDots");
        int nDots = (int)map.get("nDots");
        double[] tmpContrib = (double[])map.get("tmpContrib");
        double[] normContrib = (double[])map.get("normContrib");
        BufferedImage pbOut = new BufferedImage(iW, iOutH, BufferedImage.TYPE_INT_RGB);
        for (int y = 0; y < iOutH; y++) {
            int startY;
            int start;
            int Y = (int)(((double)y) * ((double)iH) / ((double)iOutH) + 0.5);
            startY = Y - nHalfDots;
            if (startY < 0) {
                startY = 0;
                start = nHalfDots - Y;
            } else {
                start = 0;
            }
            int stop;
            int stopY = Y + nHalfDots;
            if (stopY > (int)(iH - 1)) {
                stopY = iH - 1;
                stop = nHalfDots + (iH - 1 - Y);
            } else {
                stop = nHalfDots * 2;
            }
            if (start > 0 || stop < nDots - 1) {
                CalTempContrib(start, stop, tmpContrib, normContrib);
                for (int x = 0; x < iW; x++) {
                    value = VerticalFilter(pbImage, startY, stopY, start, stop, x, tmpContrib);
                    pbOut.setRGB(x, y, value);
                }
            } else {
                for (int x = 0; x < iW; x++) {
                    value = VerticalFilter(pbImage, startY, stopY, start, stop, x, normContrib);
                    pbOut.setRGB(x, y, value);
                }
            }
        }
        return pbOut;
    }

    private static int VerticalFilter(BufferedImage pbInImage, int startY, int stopY, int start, int stop, int x, double[] pContrib) {
        double valueRed = 0.0;
        double valueGreen = 0.0;
        double valueBlue = 0.0;
        int valueRGB = 0;
        int i, j;
        for (i = startY, j = start; i <= stopY; i++, j++) {
            valueRGB = pbInImage.getRGB(x, i);
            valueRed += GetRedValue(valueRGB) * pContrib[j];
            valueGreen += GetGreenValue(valueRGB) * pContrib[j];
            valueBlue += GetBlueValue(valueRGB) * pContrib[j];
        }
        valueRGB = ComRGB(Clip((int)valueRed), Clip((int)valueGreen), Clip((int)valueBlue));
        return valueRGB;
    }

}
