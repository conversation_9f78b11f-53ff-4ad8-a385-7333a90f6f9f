package com.cairh.cpe.esb.component.file.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class FileOperationSaveRequest {
    private String lastfilePath;
    private String fileName;
    private String file_arch_type;
    private byte[] fileContent;
    private String storage_properties;
    /**
     * 值为1时，直接上传厂商，不走缓存目录
     */
    private String direct_upload;
    /**
     * 文件夹
     */
    private String dir;
}
