package com.cairh.cpe.esb.component.file.utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 功能说明: 别名，用于结果集字段转换<br>
 * 系统版本: v1.0<br>
 * 开发人员: <AUTHOR>
 * 开发时间: 2015年7月20日<br>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Alias {

	public String value();
}