package com.cairh.cpe.esb.component.risk.core.concrete;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.component.common.data.entity.*;
import com.cairh.cpe.component.common.data.service.*;
import com.cairh.cpe.component.common.support.DubboProxyService;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.esb.base.rpc.IVBaseDictConvertDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictToLocalRequest;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictToOutRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictToLocalResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictToOutResponse;
import com.cairh.cpe.esb.component.core.constant.BusiConstant;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.constant.DictTransKindConstant;
import com.cairh.cpe.esb.component.core.util.CpeAssert;
import com.cairh.cpe.esb.component.core.util.DateUtils;
import com.cairh.cpe.esb.component.core.util.StringUtil;
import com.cairh.cpe.esb.component.risk.core.RiskService;
import com.cairh.cpe.esb.component.risk.dto.req.ExamCaculateExamRequest;
import com.cairh.cpe.esb.component.risk.dto.req.ExamConfirmExamResultRequest;
import com.cairh.cpe.esb.component.risk.dto.resp.ExamCaculateExamResponse;
import com.cairh.cpe.esb.component.risk.dto.resp.ExamConfirmExamResultResponse;
import com.cairh.cpe.http.data.hsfund.AccountService;
import com.cairh.cpe.http.data.hsfund.req.SubmitPaperInfoRequest;
import com.cairh.cpe.http.data.hsfund.resp.SubmitPaperInfoResponse;
import com.cairh.cpe.util.math.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class HSRiskService extends RiskBaseService implements RiskService {
    @Autowired
    private IExamPaperService examPaperService;
    @Autowired
    private IRiskInfoService riskInfoService;
    @Autowired
    private IExamTestResultService examTestResultService;
    @Autowired
    private IExamResultCommentService examResultCommentService;

    @Autowired
    private IExamQuestionOptionsService examQuestionOptionsService;
    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private DubboProxyService dubboProxyService;

    @DubboReference(check = false)
    private IVBaseDictConvertDubboService baseDictConvertDubboService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Override
    public ExamCaculateExamResponse examCaculateExam(ExamCaculateExamRequest request) {
        // 获取问卷
        ExamPaper examPaper = examPaperService.getById(request.getExampaper_id());

        if (null == examPaper) {
            throw new BizException(ErrorCode.ERR_SYSERROR, "[exampaper_id]问卷id:" + request.getExampaper_id() + "不存在");
        }

        ExamTestResult examTestResult = new ExamTestResult();
        ExamCaculateExamResponse response = new ExamCaculateExamResponse();

        // 是否来自柜台
        log.info("恒生问卷examCaculateExam is_from_counter={}", examPaper.getIs_from_counter());
        if ("1".equals(examPaper.getIs_from_counter())) {
            examCaculateExamCounter(request, examPaper, response, examTestResult);
        } else {
            localCalc(examPaper, examTestResult, request, response);
        }

        String examtestresultId = idGenerator.nextUUID(null);
        examTestResult.setSerial_id(examtestresultId);
        examTestResult.setExampaper_id(examPaper.getSerial_id());
        examTestResult.setStatus("1");//1提交、2确认

        Integer cooling_period = 0;

        response.setExamtestresult_id(examTestResult.getSerial_id());
        response.setScore(examTestResult.getScore());
        response.setRisk_begin_date(examTestResult.getRisk_begin_date());
        response.setRisk_end_date(examTestResult.getRisk_end_date());
        response.setCooling_period(cooling_period.toString());

        // 保存
        HSRiskService service = (HSRiskService) AopContext.currentProxy();
        service.toDbAndGet(request, response.getInvest_term(), response.getEn_invest_kind(), cooling_period, response.getCorp_risk_level(), examTestResult);

        return response;
    }

    // 柜台问卷试算
    public void examCaculateExamCounter(ExamCaculateExamRequest request, ExamPaper examPaper, ExamCaculateExamResponse response, ExamTestResult examTestResult) {
        List answers = request.getAnswer_list();

        // 拼接恒生接口答案字段
        StringBuffer gdAnswerStr = new StringBuffer();
        for (Object answer : answers) {
            Map answermap = (Map) answer;
            // 题目id
            Object examquestion_id = answermap.get("examquestion_id");
            CpeAssert.hasText((String) examquestion_id, "无效字段 answer_list.examquestion_id");
            ExamQuestion examQuestion = examQuestionService.getById(examquestion_id.toString());
            if (null == examQuestion) {
                throw new BizException(ErrorCode.ERR_SYSERROR, "[examquestion_id]题目id:" + examquestion_id + "不存在");
            }
            if (QUESTION_TYPE_QA.equals(examQuestion.getQuestion_kind())) {
                log.info("question_id为{}的问答题跳过算分:", examQuestion.getSerial_id());
                continue;
            }
            String question_no = examQuestion.getQuestion_no();
            String option_ids = (String) answermap.get("option_ids");
            CpeAssert.hasText(option_ids, "无效字段 answer_list.option_ids");
            String option_nos = getOptionsNoStr(option_ids);
            //问卷答案：每个选项之间用‘;’分隔，多选题每个答案之间顺序排列，格式：题目id|答案;题目id|答案（示例：qid:A|qid:ACD）
            gdAnswerStr.append(question_no).append(":").append(option_nos.replaceAll(",", "")).append("|");
        }

        // 调用恒生算分接口
        SubmitPaperInfoResponse hundSunSubmitPaperInfoResponse = calc(examPaper, gdAnswerStr.substring(0, gdAnswerStr.length() - 1), request);
        log.info("恒生问卷测算接口响应参数: {}", JSONObject.toJSONString(hundSunSubmitPaperInfoResponse));


        //Double score = 0.0;//多选题算分规则：当question_kind为3时，累加算分；当question_kind为1时取最高分数
        BigDecimal score = StringUtils.isNotBlank(hundSunSubmitPaperInfoResponse.getRisk_score())
                ? new BigDecimal(hundSunSubmitPaperInfoResponse.getRisk_score())
                : new BigDecimal(hundSunSubmitPaperInfoResponse.getPaper_score());

        String risk_level_value = hundSunSubmitPaperInfoResponse.getInvest_risk_tolerance();// //客户风险等级 | 投资人风险承受能力 C 1 0 N V4.3.1.0  数据字典（现场可调整）：1安全型2保守型3稳健型4积极型5进取型
        String invest_kind = hundSunSubmitPaperInfoResponse.getInvestmentvarieties();//产品投资品种(示例1,2,9) | 投资品种
        String invest_term = hundSunSubmitPaperInfoResponse.getTc_time_flag();//产品投资期限(示例1,2) | 投资期限标志

        // yyyyMMdd
        Integer risk_begin_date = Integer.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));// 有效开始日期
        Integer risk_end_date = null;
        if (StringUtils.isNotBlank(hundSunSubmitPaperInfoResponse.getRisk_end_date())) {
            risk_end_date = Integer.valueOf(hundSunSubmitPaperInfoResponse.getRisk_end_date());

        } else {
            Integer erpire_in = examPaper.getExpire_in();
            if (0 != erpire_in) {
                int risk_valid = NumberUtils.toInt(String.valueOf(erpire_in * 365), 0) / 30;
                Date riskEndDate = DateUtil.offsetDay(DateUtil.offsetMonth(new Date(), risk_valid), -1);

                String risk_end_date_str = DateUtil.format(riskEndDate, "yyyyMMdd");
                risk_end_date = Integer.valueOf(risk_end_date_str);

            } else {
                risk_end_date = Integer.valueOf(DateUtil.format(new Date(DateUtil.offsetDay(new Date(), 365).getTime()), "yyyyMMdd")); // 有效截止日期
            }
        }


        List<ExamResultComment> list = examResultCommentService.lambdaQuery()
                .eq(ExamResultComment::getExampaper_id, request.getExampaper_id())
                .list();
        ExamResultComment resultComment = null;

        for (ExamResultComment examResultComment : list) {
            int right = BigDecimalUtil.compareTo(examResultComment.getMax_score(), score);
            int left = BigDecimalUtil.compareTo(examResultComment.getMin_score(), score);

            if (right >= 0 && left < 0) {
                resultComment = examResultComment;

            }
        }
        String advice = "";
        if (Objects.nonNull(resultComment)) {
            advice = resultComment.getInvest_advice();
            risk_end_date = DateUtils.toInteger(DateUtils.toLocalDate(DateUtils.addDays(new Date(), resultComment.getValid_days())));
        }

        examTestResult.setScore(score);
        examTestResult.setRisk_begin_date(risk_begin_date);
        examTestResult.setRisk_end_date(risk_end_date);

        response.setInvest_advice(advice);

        // 兼容性转换corp_risk_level, en_invest_kind, invest_term
        convert(response, risk_level_value, invest_kind, invest_term);
    }

    private void convert(ExamCaculateExamResponse response, String corp_risk_level, String prod_invest_kind, String prod_invest_term) {
        log.info("恒生问卷参数转换参数: corp_risk_level: {}, prod_invest_kind: {}, prod_invest_term: {}",
                corp_risk_level, prod_invest_kind, prod_invest_term);
        Map<String, String> riskLevelConvertMap = new HashMap<>();
        Map<String, String> prodInvestKindConvertMap = new HashMap<>();
        Map<String, String> prodInvestTermConvertMap = new HashMap<>();

        //设置请求参数，准备调用字典转换,外围转本地的dubbo接口
        VBaseDictToLocalRequest dictToLocalRequest = new VBaseDictToLocalRequest();
        dictToLocalRequest.setDict_trans_kind(DictTransKindConstant.LOCAL_TO_HS);

        //处理映射关系
        if (StringUtils.isNotBlank(corp_risk_level)) {
            dealRiskLevel(corp_risk_level, riskLevelConvertMap, dictToLocalRequest);
        }
        if (StringUtils.isNotBlank(prod_invest_kind)) {
            dealInvestKind(prod_invest_kind, prodInvestKindConvertMap, dictToLocalRequest);
        }
        if (StringUtils.isNotBlank(prod_invest_term)) {
            dealInvestTerm(prod_invest_term, prodInvestTermConvertMap, dictToLocalRequest);
        }

        //如果没有找到转换关系则原样返回，如果有对应转换关系后续操作则会将其替换后返回
        response.setCorp_risk_level(corp_risk_level);
        response.setEn_invest_kind(prod_invest_kind);
        response.setInvest_term(prod_invest_term);

        //获取唯一的 key,设置sub_code为转换结果
        if (!riskLevelConvertMap.isEmpty()) {
            String riskLevel_Key = riskLevelConvertMap.keySet().iterator().next();
            response.setCorp_risk_level(riskLevelConvertMap.get(riskLevel_Key));
        }

        if (!prodInvestKindConvertMap.isEmpty()) {
            String investKind_Key = prodInvestKindConvertMap.keySet().iterator().next();
            response.setEn_invest_kind(prodInvestKindConvertMap.get(investKind_Key));
        }

        if (!prodInvestTermConvertMap.isEmpty()) {
            String investTerm_Key = prodInvestTermConvertMap.keySet().iterator().next();
            response.setInvest_term(prodInvestTermConvertMap.get(investTerm_Key));
        }
    }

    /**
     * 处理客户风险等级的映射关系
     */
    private void dealRiskLevel(String corp_risk_level, Map<String, String> custRiskLevelConvertMap, VBaseDictToLocalRequest dictToLocalRequest) {
        dictToLocalRequest.setDict_code("user_risk_level");
        dictToLocalRequest.setOuter_sub_code(corp_risk_level);
        //调用字典转换，外围转本地dubbo接口查询字典转换表
        VBaseDictToLocalResponse riskLevelRes = baseDictConvertDubboService.baseDataOutDictToLocal(dictToLocalRequest);
        if (riskLevelRes != null) {
            //将查询结果中的数据映射
            custRiskLevelConvertMap.put(riskLevelRes.getOuter_sub_code(), riskLevelRes.getSub_code());
        }
    }

    /**
     * 处理投资品种的映射关系
     */
    private void dealInvestKind(String prod_invest_kind, Map<String, String> prodInvestKindConvertMap, VBaseDictToLocalRequest dictToLocalRequest) {
        dictToLocalRequest.setDict_code("invest_kind");
        dictToLocalRequest.setOuter_sub_code(prod_invest_kind);
        VBaseDictToLocalResponse investKindRes = baseDictConvertDubboService.baseDataOutDictToLocal(dictToLocalRequest);
        if (investKindRes != null) {
            prodInvestKindConvertMap.put(investKindRes.getOuter_sub_code(), investKindRes.getSub_code());
        }
    }

    /**
     * 处理投资期限的映射关系
     */
    private void dealInvestTerm(String prod_invest_term, Map<String, String> prodInvestTermConvertMap, VBaseDictToLocalRequest dictToLocalRequest) {
        dictToLocalRequest.setDict_code("invest_term");
        dictToLocalRequest.setOuter_sub_code(prod_invest_term);
        VBaseDictToLocalResponse investTermRes = baseDictConvertDubboService.baseDataOutDictToLocal(dictToLocalRequest);
        if (investTermRes != null) {
            prodInvestTermConvertMap.put(investTermRes.getOuter_sub_code(), investTermRes.getSub_code());
        }
    }


    @Override
    public ExamConfirmExamResultResponse examConfirmExamResult(ExamConfirmExamResultRequest request) {

        if (StringUtils.isBlank(request.getUser_name())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[user_name] 客户全称不能为空");
        }

        if (StringUtils.isBlank(request.getId_kind())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[id_kind] 证件类型不能为空");
        }

        if (StringUtils.isBlank(request.getId_no())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[id_no] 证件号码不能为空");
        }

        ExamTestResult examTestResult = examTestResultService.getById(request.getExamtestresult_id());

        if (examTestResult == null) {
            throw new BizException("-1", "根据入参答题流水号，未找到对应的答题流水");
        }

        if ("2".equals(examTestResult.getStatus())) {
            throw new BizException("-1", "问卷确认已确认");
        }

        ExamPaper examPaper = examPaperService.getById(examTestResult.getExampaper_id());

        if (examPaper == null) {
            throw new BizException("-1", "根据答题流水中的试卷编号，未找到对应的试卷");
        }


        ExamConfirmExamResultResponse response = new ExamConfirmExamResultResponse();
        response.setExamtestresult_id(request.getExamtestresult_id());

        // 是否来自柜台
        log.info("恒生问卷examConfirmExamResult is_from_counter={}", examPaper.getIs_from_counter());
        // 非柜台问卷不调用接口
        if (StringUtils.equals(examPaper.getIs_from_counter(), "1")) {
            List<AnswerData> answerData = JSON.parseArray(examTestResult.getPaper_answer(), AnswerData.class);
            String paperAnswer = parsePaperAnswer(answerData);

            //问卷答案：每个选项之间用‘;’分隔，多选题每个答案之间用 ‘-’分隔，格式：题目id|答案;题目id|答案（示例：qid:A|qid:A）
            SubmitPaperInfoRequest hundSunSubmitPaperInfoRequest = new SubmitPaperInfoRequest();
            hundSunSubmitPaperInfoRequest.setElig_content(paperAnswer);
            hundSunSubmitPaperInfoRequest.setFull_name(request.getUser_name());
            hundSunSubmitPaperInfoRequest.setId_kind_gb(covertIdKind(request.getId_kind()));
            hundSunSubmitPaperInfoRequest.setId_no(request.getId_no());

            log.info("恒生问卷确认接口请求参数: {}", JSON.toJSONString(hundSunSubmitPaperInfoRequest));
            SubmitPaperInfoResponse hundSunSubmitPaperInfoResponse = accountService.submitPaperInfo(hundSunSubmitPaperInfoRequest);
            log.info("恒生问卷确认接口响应参数: {}", JSONObject.toJSONString(hundSunSubmitPaperInfoResponse));

            // 问卷有效期截至时间
            if (StringUtils.isNotBlank(hundSunSubmitPaperInfoResponse.getRisk_end_date())) {
                Integer risk_end_date = Integer.valueOf(hundSunSubmitPaperInfoResponse.getRisk_end_date());
                examTestResult.setRisk_end_date(risk_end_date);
            }

            response.setPaper_score(hundSunSubmitPaperInfoResponse.getPaper_score());
        } else {
            response.setPaper_score(String.valueOf(examTestResult.getScore()));
        }

        // 更新
        examTestResult.setStatus("2");//status	c1		状态	1提交、2确认
        examTestResultService.updateById(examTestResult);

        return response;
    }

    /**
     * 数据入库并返回
     *
     * @param request
     * @param invest_term
     * @param en_invest_kind
     * @param cooling_period
     * @param corp_risk_level
     * @param examTestResult
     * @return
     */
    @Transactional
    public void toDbAndGet(ExamCaculateExamRequest request, String invest_term, String en_invest_kind, Integer cooling_period, String corp_risk_level, ExamTestResult examTestResult) {
        //循环插入其他原因
        List<Map<String, String>> answer_list = request.getAnswer_list();
        for (Map<String, String> map : answer_list) {
            String otherReason = map.get(BusiConstant.EXAM_TEST_RESULT_OTHER_REASON);
            if (StringUtils.isNotBlank(otherReason)) {
                ExamTestResultOther examtestresultother = new ExamTestResultOther().setContent(otherReason);
                examtestresultother.insert();
                //插入的原因id  覆盖map的key BusiConstant.EXAM_TEST_RESULT_OTHER_REASON
                map.put(BusiConstant.EXAM_TEST_RESULT_OTHER_REASON, examtestresultother.getSerial_id());
            }

        }
        examTestResult.setPaper_answer(JSONObject.toJSONString(answer_list));
        examTestResult.setRender_data(request.getRender_data());
        examTestResult.setRequest_no(request.getRequest_no());
        examTestResultService.save(examTestResult);

        RiskInfo riskInfo = new RiskInfo();
        riskInfo.setSerial_id(idGenerator.nextUUID(null));

        riskInfo.setExamtestresult_id(examTestResult.getSerial_id());
        riskInfo.setCorp_risk_level(corp_risk_level);
        riskInfo.setEn_invest_term(invest_term);
        riskInfo.setEn_invest_kind(en_invest_kind);
        riskInfo.setCooling_period(cooling_period);
        riskInfoService.save(riskInfo);
    }


    /**
     * 调用恒生柜台，获取分值及风险等级
     */
    public SubmitPaperInfoResponse calc(ExamPaper examPaper, String paperAnswer, ExamCaculateExamRequest request) {
        if (StringUtils.isBlank(request.getFull_name())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[full_name] 客户全称不能为空");
        }

        if (StringUtils.isBlank(request.getId_kind_gb())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[id_kind_gb] 证件类型不能为空");
        }

        if (StringUtils.isBlank(request.getId_no())) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_NOT_EMPTY, "[id_no] 证件号码不能为空");
        }

        SubmitPaperInfoRequest hundSunSubmitPaperInfoRequest = new SubmitPaperInfoRequest();
        hundSunSubmitPaperInfoRequest.setElig_content(paperAnswer);
        hundSunSubmitPaperInfoRequest.setPre_check_flag("1");//预校验
        hundSunSubmitPaperInfoRequest.setFull_name(request.getFull_name());
        hundSunSubmitPaperInfoRequest.setId_kind_gb(covertIdKind(request.getId_kind_gb()));
        hundSunSubmitPaperInfoRequest.setId_no(request.getId_no());

        log.info("恒生问卷测算接口请求参数: {}", JSONObject.toJSONString(hundSunSubmitPaperInfoRequest));
        return accountService.submitPaperInfo(hundSunSubmitPaperInfoRequest);
    }

    private String getAdvice(List<ExamResultComment> list, BigDecimal score) {
        for (ExamResultComment examResultComment : list) {
            int right = BigDecimalUtil.compareTo(examResultComment.getMax_score(), score);
            int left = BigDecimalUtil.compareTo(examResultComment.getMin_score(), score);

            if (right > 0 && left <= 0) {
                return examResultComment.getInvest_advice();
            }
        }
        return null;
    }

    /**
     * 答案串转换成指定类型
     * <p>
     * paper_answer 格式 题号|答案;题号|答案 。例如：20|2;24|23;25|*********;27|1;28|1; 其中20号题的答案为2，24号题的答案为2、3。
     */
    private String parsePaperAnswer(List<AnswerData> answer_data) {
        StringBuilder paperAnswerBuilder = new StringBuilder();
        for (AnswerData answer_datum : answer_data) {
            ExamQuestion examQuestion = examQuestionService.getById(answer_datum.getExamquestion_id());
            if (null == examQuestion) {
                throw new BizException(ErrorCode.ERR_SYSERROR, "[examquestion_id]题目id:" + answer_datum.getExamquestion_id() + "不存在");
            }
            paperAnswerBuilder.append(examQuestion.getQuestion_no()).append(":");
            // 去除答案中可能存在的'[',‘]‘
            String options = answer_datum.getOption_ids().replaceAll("\\[|]", "");
            String optionsNoStr = getOptionsNoStr(options);
            paperAnswerBuilder.append(optionsNoStr);
            paperAnswerBuilder.append("|");
        }
        // 删除多余的"
        return StringUtils.remove(paperAnswerBuilder.substring(0, paperAnswerBuilder.length() - 1), "\"");
    }

    private String covertIdKind(String idKind) {
        VBaseDictQryRequest baseDictQryRequest = new VBaseDictQryRequest();
        baseDictQryRequest.setDict_code("id_kind");
        baseDictQryRequest.setSub_code(idKind);
        log.info("[id_kind]证件类型转换 baseDataQryDict本地转恒生请求参数: {}", baseDictQryRequest);
        List<VBaseDictQryResponse> vBaseDictQryResponses = dubboProxyService.baseDataQryDict(baseDictQryRequest);
        log.info("[id_kind]证件类型转换 baseDataQryDict本地转恒生响应参数: {}", baseDictQryRequest);

        if (vBaseDictQryResponses != null && vBaseDictQryResponses.size() != 0) {
            VBaseDictQryResponse basedictionary = vBaseDictQryResponses.get(0);
            VBaseDictToOutRequest vBaseDictToOutRequest = new VBaseDictToOutRequest();
            vBaseDictToOutRequest.setDict_code("id_kind")
                    .setSub_code(basedictionary.getSub_code())
                    .setDict_trans_kind(DictTransKindConstant.LOCAL_TO_HS);

            log.info("[id_kind]证件类型转换 baseDataLocalDictToOut本地转恒生请求参数: {}", baseDictQryRequest);
            VBaseDictToOutResponse vBaseDictToOutResponse = baseDictConvertDubboService.baseDataLocalDictToOut(vBaseDictToOutRequest);
            log.info("[id_kind]证件类型转换 baseDataLocalDictToOut本地转恒生响应参数: {}", vBaseDictToOutResponse);
            if (vBaseDictToOutResponse != null) {
                return vBaseDictToOutResponse.getOuter_sub_code();
            }
        }

        log.info("没有配置恒生证件类型转换");
        return idKind;
    }

    public void localCalc(ExamPaper examPaper, ExamTestResult examTestResult, ExamCaculateExamRequest request, ExamCaculateExamResponse response) {
        List<ExamQuestion> examQuestions = examQuestionService.lambdaQuery()
                .eq(ExamQuestion::getExampaper_id, examPaper.getSerial_id()).list();
        Map<String, ExamQuestion> examQuestionMap = new HashMap<>();
        for (ExamQuestion examQuestion : examQuestions) {
            examQuestionMap.put(examQuestion.getSerial_id(), examQuestion);
        }
        List<ExamQuestionOptions> examQuestionOptionss = examQuestionOptionsService.lambdaQuery()
                .eq(ExamQuestionOptions::getExampaper_id, examPaper.getSerial_id()).list();
        Map<String, ExamQuestionOptions> examQuestionOptionsMap = new HashMap<>();
        for (ExamQuestionOptions examQuestionOptions : examQuestionOptionss) {
            examQuestionOptionsMap.put(examQuestionOptions.getSerial_id(), examQuestionOptions);
        }

        List answers = request.getAnswer_list();
        //Double score = 0.0;//多选题算分规则：当question_kind为3时，累加算分；当question_kind为1时取最高分数
        BigDecimal score = BigDecimal.valueOf(0);
        String min_risk_level_flag = "0";//0:否；1：是
        String invest_term = " ";
        String en_invest_kind = " ";
        for (Object answer : answers) {
            Map answermap = (Map) answer;
            Object examquestion_id = answermap.get("examquestion_id");
            CpeAssert.hasText((String) examquestion_id, "无效字段 answer_list.examquestion_id");
            ExamQuestion examQuestion = examQuestionMap.get(examquestion_id);
            if (examQuestion == null) {
                throw new BizException("-1", "根据入参的试题编号，未找到对应的试题");
            }
            BigDecimal score_tmp = BigDecimal.valueOf(0);
            if (QUESTION_TYPE_QA.equals(examQuestion.getQuestion_kind())) {
                String question_answer = (String) answermap.get(ANSWER_CONTENT);
                CpeAssert.hasText(question_answer, "主观题answer_list." + ANSWER_CONTENT + "需必传");
                score_tmp = examQuestion.getScore();
                continue;
            }
            //relation_type 3:投资期限；4：投资品种
            String relation_type = examQuestion.getRelation_type();
            String option_ids = (String) answermap.get("option_ids");
            CpeAssert.hasText(option_ids, "无效字段 answer_list.option_ids");
            String[] option_id_list = option_ids.split(",");
            String question_kind = examQuestion.getQuestion_kind();
            for (String option_id : option_id_list) {
                ExamQuestionOptions examQuestionOptions = examQuestionOptionsMap.get(option_id);
                if (examQuestionOptions == null) {
                    throw new BizException("-1", "根据入参的选项编号，未找到对应的试题选项");
                }
                if ("1".equals(question_kind)) {
                    if (examQuestionOptions.getScore().compareTo(score_tmp) > 0) {
                        score_tmp = examQuestionOptions.getScore();
                    }
                } else if ("3".equals(question_kind)) {
                    score_tmp = score_tmp.add(examQuestionOptions.getScore());
                } else {
                    score_tmp = examQuestionOptions.getScore();
                }
                if ("3".equals(relation_type)) {
                    if ("1".equals(question_kind) || "3".equals(question_kind)) {
                        invest_term = StringUtils.isBlank(examQuestionOptions.getRelation_value()) ?
                                invest_term : invest_term + examQuestionOptions.getRelation_value() + ",";
                    } else {
                        invest_term = examQuestionOptions.getRelation_value();
                    }
                } else if ("4".equals(relation_type)) {
                    if ("1".equals(question_kind) || "3".equals(question_kind)) {
                        en_invest_kind = StringUtils.isBlank(examQuestionOptions.getRelation_value()) ?
                                en_invest_kind : en_invest_kind + examQuestionOptions.getRelation_value() + ",";
                    } else {
                        en_invest_kind = examQuestionOptions.getRelation_value();
                    }
                }
                if (StringUtils.isNotBlank(examQuestionOptions.getMin_risk_level_flag()) && ("0".equals(min_risk_level_flag))) {
                    min_risk_level_flag = examQuestionOptions.getMin_risk_level_flag();
                }
            }
            score = score.add(score_tmp);
        }

        List<ExamResultComment> examResultComments = examResultCommentService.listByMap(Collections.singletonMap("exampaper_id", examPaper.getSerial_id()));
        ExamResultComment examComment = null;
        if (CollectionUtils.isNotEmpty(examResultComments)) {
            for (ExamResultComment examResultComment : examResultComments) {
                BigDecimal minScore = examResultComment.getMin_score();
                BigDecimal maxScore = examResultComment.getMax_score();
                if (score.compareTo(minScore) > 0 && score.compareTo(maxScore) <= 0) {
                    examComment = examResultComment;
                    break;
                }
            }
        }

        //移除拼接后字符串末尾逗号
        en_invest_kind = StringUtil.removeComma(en_invest_kind);

        invest_term = StringUtil.removeComma(invest_term);

        String corp_risk_level = null;
        Integer risk_begin_date = 0;
        Integer risk_end_date = 0;
        String min_corplevel_flag = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_MIN_CORPLEVEL_FLAG, "false");
        String min_corplevel = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_MIN_CORPLEVEL, "0");
        String conservative_corplevel = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_CONSERVATIVE_CORPLEVEL, "1");
        log.info("min_corplevel_flag={}, min_corplevel={}, conservative_corplevel={}", min_corplevel_flag, min_corplevel, conservative_corplevel);
        if (StringUtils.equals(min_corplevel_flag, "false")) {
            if (examComment != null) {
                corp_risk_level = examComment.getRisk_level().toString();
                risk_begin_date = Integer.parseInt(DateUtils.dateTime());
                risk_end_date = DateUtils.toInteger(DateUtils.toLocalDate(DateUtils.addDays(new Date(), examComment.getValid_days())));
            }
            if (StringUtils.isNotBlank(corp_risk_level)) {
                if (StringUtils.equals(corp_risk_level, conservative_corplevel) && "1".equals(min_risk_level_flag)) {
                    corp_risk_level = min_corplevel;
                }
            } else {
                corp_risk_level = min_corplevel;
            }
        } else {
            if ("1".equals(min_risk_level_flag)) {
                corp_risk_level = min_corplevel;
            } else {
                if (examComment != null) {
                    corp_risk_level = examComment.getRisk_level().toString();
                    risk_begin_date = Integer.parseInt(DateUtils.dateTime());
                    risk_end_date = DateUtils.toInteger(DateUtils.toLocalDate(DateUtils.addDays(new Date(), examComment.getValid_days())));
                } else {
                    corp_risk_level = min_corplevel;
                }
            }
        }
        log.info("corp_risk_level={}", corp_risk_level);
        response.setCorp_risk_level(corp_risk_level);
        response.setEn_invest_kind(en_invest_kind);
        response.setInvest_term(invest_term);

        examTestResult.setScore(score);
        examTestResult.setRisk_begin_date(risk_begin_date);
        examTestResult.setRisk_end_date(risk_end_date);
    }

    private String getOptionsNoStr(String option_ids) {
        StringBuffer optionsSb = new StringBuffer();
        String[] split = option_ids.split(",");
        for (String optionId : split) {
            ExamQuestionOptions questionOptions = examQuestionOptionsService.getById(optionId);
            if (null == questionOptions) {
                throw new BizException(ErrorCode.ERR_SYSERROR, "[question_option_id]选项id:" + optionId + "不存在");
            }
            Integer option_no = questionOptions.getOption_no();
            optionsSb.append(option_no).append(",");
        }

        return optionsSb.substring(0, optionsSb.length() - 1);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AnswerData {

        /**
         * Y 试题编号
         */
        private String examquestion_id;
        /**
         * Y 选项编号
         */
        private String option_ids;

    }
}
