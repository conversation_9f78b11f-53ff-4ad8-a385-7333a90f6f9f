//package com.cairh.cpe.esb.component.risk.core.concrete;
//
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.cairh.cpe.component.common.data.entity.*;
//import com.cairh.cpe.component.common.data.service.*;
//import com.cairh.cpe.component.common.utils.htsecutils.HtBaseDictConvertUtils;
//import com.cairh.cpe.component.common.utils.htsecutils.HtSecCommonUtils;
//import com.cairh.cpe.context.BizException;
//import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
//import com.cairh.cpe.db.config.IdGenerator;
//import com.cairh.cpe.esb.component.core.constant.BusiConstant;
//import com.cairh.cpe.esb.component.core.constant.ConfigConst;
//import com.cairh.cpe.esb.component.core.util.CpeAssert;
//import com.cairh.cpe.esb.component.core.util.DateUtils;
//import com.cairh.cpe.esb.component.risk.core.RiskService;
//import com.cairh.cpe.esb.component.risk.dto.req.ExamCaculateExamRequest;
//import com.cairh.cpe.esb.component.risk.dto.req.ExamConfirmExamResultRequest;
//import com.cairh.cpe.esb.component.risk.dto.resp.ExamCaculateExamResponse;
//import com.cairh.cpe.esb.component.risk.dto.resp.ExamConfirmExamResultResponse;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.EsbRequestXmlBean;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.Header;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.req.Ws230124Request;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.req.Ws230212Request;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.resp.Ws230124Response;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.resp.Ws230212Response;
//import com.cairh.cpe.util.math.BigDecimalUtil;
//import com.cairh.cpe.ws.data.esb.service.EsbWsService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.aop.framework.AopContext;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.*;
//
//@Slf4j
//@Service
//public class HtSecRiskService extends RiskBaseService implements RiskService {
//
//    @Autowired
//    private IExamPaperService examPaperService;
//    @Autowired
//    private IExamQuestionOptionsService examQuestionOptionsService;
//    @Autowired
//    private IExamQuestionService examQuestionService;
//    @Autowired
//    private IExamResultCommentService examResultCommentService;
//    @Autowired
//    private IdGenerator idGenerator;
//    @Autowired
//    private CompositePropertySources compositePropertySources;
//    @Autowired
//    private IExamTestResultService examTestResultService;
//    @Autowired
//    private IRiskInfoService riskInfoService;
//    @Autowired
//    private EsbWsService esbWsService;
//    @Autowired
//    private HtBaseDictConvertUtils htBaseDictConvertUtils;
//
//    @Override
//    public ExamCaculateExamResponse examCaculateExam(ExamCaculateExamRequest request) {
//        if (StringUtils.isBlank(request.getResType())) {
//            throw new BizException("-1", "[resType]渠道来源不能为空");
//        }
//        ExamCaculateExamResponse response = new ExamCaculateExamResponse();
//        ExamPaper examPaper = examPaperService.getById(request.getExampaper_id());
//        if (examPaper == null) {
//            throw new BizException("-1", "根据入参的试卷编号，未找到对应的试卷");
//        }
//
//        if (StringUtils.isAnyBlank(request.getId_no(), request.getFull_name(), request.getId_kind_gb()))
//            throw new BizException("-1", "客户全称、证件类型、证件号码不允许为空");
//
//        /**
//         * 查询所有试题
//         */
//        List<ExamQuestion> examQuestions = examQuestionService.lambdaQuery()
//                .eq(ExamQuestion::getExampaper_id, examPaper.getSerial_id()).list();
//        Map<String, ExamQuestion> examQuestionMap = new HashMap<>();
//        for (ExamQuestion examQuestion : examQuestions) {
//            examQuestionMap.put(examQuestion.getSerial_id(), examQuestion);
//        }
//        /**
//         * 查询所有试题答案
//         */
//        List<ExamQuestionOptions> examQuestionOptionss = examQuestionOptionsService.lambdaQuery()
//                .eq(ExamQuestionOptions::getExampaper_id, examPaper.getSerial_id()).list();
//        Map<String, ExamQuestionOptions> examQuestionOptionsMap = new HashMap<>();
//        for (ExamQuestionOptions examQuestionOptions : examQuestionOptionss) {
//            examQuestionOptionsMap.put(examQuestionOptions.getSerial_id(), examQuestionOptions);
//        }
//
//        String busType = examPaper.getCounter_paper_no();
//        String busScope = request.getBus_scope();
//
//
//        List<Ws230212Request.Topic> topicList = new ArrayList<>();
//        List answers = request.getAnswer_list();
//
//        BigDecimal score = BigDecimal.valueOf(0);
//        String min_risk_level_flag = "0";//0:否；1：是
//        for (Object answer : answers) {
//            Map answermap = (Map) answer;
//            Object examquestion_id = answermap.get("examquestion_id");
//            CpeAssert.hasText((String) examquestion_id, "无效字段 answer_list.examquestion_id");
//            ExamQuestion examQuestion = examQuestionMap.get(examquestion_id);
//            if (examQuestion == null) {
//                throw new BizException("-1", "根据入参的试题编号，未找到对应的试题");
//            }
//
//
//            /*if (QUESTION_TYPE_QA.equals(examQuestion.getQuestion_kind())) {
//                String question_answer = (String) answermap.get(ANSWER_CONTENT);
//                CpeAssert.hasText(question_answer, "主观题answer_list." + ANSWER_CONTENT + "需必传");
//                continue;
//            }*/
//
//            Ws230212Request.Topic topic = new Ws230212Request.Topic();
//            topic.setTopicId(examQuestion.getQuestion_no());
//            topic.setEvaluationPaperID(examQuestion.getQuestion_group());
//
//            String option_ids = (String) answermap.get("option_ids");
//            CpeAssert.hasText(option_ids, "无效字段 answer_list.option_ids");
//            String[] option_id_list = option_ids.split(",");
//
//            List<Ws230212Request.TopicOption> topicOptionList = new ArrayList<>();
//            for (String option_id : option_id_list) {
//                ExamQuestionOptions examQuestionOptions = examQuestionOptionsMap.get(option_id);
//                if (examQuestionOptions == null) {
//                    throw new BizException("-1", "根据入参的选项编号，未找到对应的试题选项");
//                }
//
//                Ws230212Request.TopicOption topicOption = new Ws230212Request.TopicOption();
//                topicOption.setOptionId(String.valueOf(examQuestionOptions.getOption_no()));
//                topicOption.setOptionNo(StringUtils.substringBefore(examQuestionOptions.getOption_content(), "."));
//
//                topicOptionList.add(topicOption);
//            }
//
//            topic.setTopicOptionList(topicOptionList);
//            topicList.add(topic);
//        }
//
//        Ws230212Response.MessageResponseBody responseBody = questionAnswer(request.getResType(), request.getFull_name(), request.getId_kind_gb(), request.getId_no(), busScope, busType, request.getSrcOrderNo(), topicList);
//        if (responseBody == null || !HtSecCommonUtils.isSucceed(responseBody.getRetCode())) {
//            throw new BizException("-1", "考试题目评分信息查询失败");
//        }
//
//        score = new BigDecimal(responseBody.getScore());
//        String buzOrderNo = responseBody.getBuzOrderNo();
//        //投资期限
//        String investTerm = responseBody.getInvestTerm();
//        //投资品种类别
//        String investVariety = responseBody.getInvestVariety();
//        //适用产品风险等级
//        String suitableProductRiskLevel = responseBody.getEvaluateGrade();
//
//        String startDate = responseBody.getStartDate();
//
//        String endDate = responseBody.getEndDate();
//
//
//        List<ExamResultComment> examResultComments = examResultCommentService.listByMap(Collections.singletonMap("exampaper_id", examPaper.getSerial_id()));
//        ExamResultComment examComment = null;
//        if (CollectionUtils.isNotEmpty(examResultComments)) {
//            for (ExamResultComment examResultComment : examResultComments) {
//                BigDecimal minScore = examResultComment.getMin_score();
//                BigDecimal maxScore = examResultComment.getMax_score();
//                if (score.compareTo(minScore) > 0 && score.compareTo(maxScore) <= 0) {
//                    examComment = examResultComment;
//                    break;
//                }
//            }
//        }
//
//        String corp_risk_level = null;
//        Integer risk_begin_date = 0;
//        Integer risk_end_date = 0;
//        String min_corplevel_flag = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_MIN_CORPLEVEL_FLAG, "false");
//        String min_corplevel = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_MIN_CORPLEVEL, "0");
//        String conservative_corplevel = compositePropertySources.getProperty(ConfigConst.COMP_EXAM_CONSERVATIVE_CORPLEVEL, "1");
//        log.info("min_corplevel_flag={}, min_corplevel={}, conservative_corplevel={}", min_corplevel_flag, min_corplevel, conservative_corplevel);
//        if(StringUtils.equals(min_corplevel_flag, "false")){
//            if (examComment != null) {
//                corp_risk_level = examComment.getRisk_level().toString();
//                risk_begin_date = Integer.parseInt(DateUtils.dateTime());
//                risk_end_date = DateUtils.toInteger(DateUtils.toLocalDate(DateUtils.addDays(new Date(), examComment.getValid_days())));
//            }
//            if(StringUtils.isNotBlank(corp_risk_level)){
//                if(StringUtils.equals(corp_risk_level, conservative_corplevel) && "1".equals(min_risk_level_flag)){
//                    corp_risk_level = min_corplevel;
//                }
//            }else{
//                corp_risk_level = min_corplevel;
//            }
//        }else{
//            if ("1".equals(min_risk_level_flag)) {
//                corp_risk_level = min_corplevel;
//            } else {
//                if (examComment != null) {
//                    corp_risk_level = examComment.getRisk_level().toString();
//                    risk_begin_date = Integer.parseInt(DateUtils.dateTime());
//                    risk_end_date = DateUtils.toInteger(DateUtils.toLocalDate(DateUtils.addDays(new Date(), examComment.getValid_days())));
//                }else{
//                    corp_risk_level = min_corplevel;
//                }
//            }
//        }
//        Integer cooling_period = 0;
//        log.info("corp_risk_level={}", corp_risk_level);
//        ExamTestResult examTestResult = new ExamTestResult();
//        String examtestresultId = idGenerator.nextUUID(null);
//        examTestResult.setSerial_id(examtestresultId);
//        examTestResult.setExampaper_id(examPaper.getSerial_id());
//        examTestResult.setScore(score);
//        examTestResult.setStatus("1");//1提交、2确认
//        examTestResult.setRisk_begin_date(risk_begin_date);
//        examTestResult.setRisk_end_date(risk_end_date);
//        List<ExamResultComment> list = examResultCommentService.lambdaQuery().eq(ExamResultComment::getExampaper_id, request.getExampaper_id()).list();
//        String advice = getAdvice(list, score);
//        HtSecRiskService service = (HtSecRiskService) AopContext.currentProxy();
//        service.toDbAndGet(request, response.getInvest_term(), response.getEn_invest_kind(), cooling_period, response.getCorp_risk_level(), examTestResult);
//
//
//        response.setExamtestresult_id(examTestResult.getSerial_id());
//        response.setScore(examTestResult.getScore());
//        response.setRisk_begin_date(examTestResult.getRisk_begin_date());
//        response.setRisk_end_date(examTestResult.getRisk_end_date());
//        response.setCooling_period(corp_risk_level);
//        response.setInvest_advice(advice);
//        response.setCooling_period(cooling_period.toString());
//        response.setBuz_order_no(buzOrderNo);
//        response.setInvest_term(investTerm);
//        response.setEn_invest_kind(investVariety);
//        response.setCorp_risk_level(suitableProductRiskLevel);
//        response.setRisk_begin_date(Integer.valueOf(startDate));
//        response.setRisk_end_date(Integer.valueOf(endDate));
//
//        return response;
//    }
//
//    @Override
//    public ExamConfirmExamResultResponse examConfirmExamResult(ExamConfirmExamResultRequest request) {
//        if (StringUtils.isBlank(request.getResType())) {
//            throw new BizException("-1", "[resType]渠道来源不能为空");
//        }
//
//        Ws230124Request.MessageRequestBody requestBody = new Ws230124Request.MessageRequestBody();
//        String operatorNo = request.getOperator_no();
//        operatorNo = StringUtils.isBlank(operatorNo) ? HtSecCommonUtils.BRANCH_CODE : operatorNo;
//
//        requestBody.setCustName(request.getUser_name());
////        requestBody.setCertType(request.getId_kind());
//        // 海通证件类型转换
//        requestBody.setCertType(htBaseDictConvertUtils.getHtIdKind(request.getId_kind()));
//        requestBody.setCertNo(request.getId_no());
//        requestBody.setBuzOrderNo(request.getBuz_order_no());
//
//        requestBody.setResType(request.getResType());
//        requestBody.setEmp(HtSecCommonUtils.SYSTEM_USER);
//        requestBody.setDept(operatorNo);
//        requestBody.setOptDept(operatorNo);
//        requestBody.setMacAddr(HtSecCommonUtils.SERVER_MAC);
//
//        Ws230124Request ws230124Request = new Ws230124Request();
//        ws230124Request.setHeader(new Header());
//        ws230124Request.setBody(new EsbRequestXmlBean.Body<>(new EsbRequestXmlBean.Request<>(HtSecCommonUtils.getHtEsbHead(), requestBody)));
//
//        Ws230124Response response = esbWsService.ws230124(ws230124Request);
//
//        if (!HtSecCommonUtils.isResponseSucceed(response)) {
//            log.info("海通考试结果评分确认失败");
//            throw new BizException("-1", "考试结果评分确认失败");
//        }
//
//        Ws230124Response.MessageResponseBody responseBody = response.getBody().getRequestResponse().getResponseBody();
//        if (response == null || !HtSecCommonUtils.isSucceed(responseBody.getRetCode())) {
//            log.info("海通考试结果评分确认失败，失败信息为：", response);
//            throw new BizException("-1", "考试题目评分信息查询失败,失败原因：" + responseBody.getRetMsg());
//        }
//        ExamConfirmExamResultResponse resultResponse = new ExamConfirmExamResultResponse();
//        return resultResponse;
//    }
//
//    /**
//     * 数据入库并返回
//     *
//     * @param request
//     * @param invest_term
//     * @param en_invest_kind
//     * @param cooling_period
//     * @param corp_risk_level
//     * @param examTestResult
//     * @return
//     */
//    @Transactional
//    public void toDbAndGet(ExamCaculateExamRequest request, String invest_term, String en_invest_kind, Integer cooling_period, String corp_risk_level, ExamTestResult examTestResult) {
//        //循环插入其他原因
//        List<Map<String, String>> answer_list = request.getAnswer_list();
//        for (Map<String, String> map : answer_list) {
//            String otherReason = map.get(BusiConstant.EXAM_TEST_RESULT_OTHER_REASON);
//            if (StringUtils.isNotBlank(otherReason)) {
//                ExamTestResultOther examtestresultother = new ExamTestResultOther().setContent(otherReason);
//                examtestresultother.insert();
//                //插入的原因id  覆盖map的key BusiConstant.EXAM_TEST_RESULT_OTHER_REASON
//                map.put(BusiConstant.EXAM_TEST_RESULT_OTHER_REASON, examtestresultother.getSerial_id());
//            }
//
//        }
//        examTestResult.setPaper_answer(JSONObject.toJSONString(answer_list));
//        examTestResult.setRender_data(request.getRender_data());
//        examTestResult.setRequest_no(request.getRequest_no());
//        examTestResultService.save(examTestResult);
//
//        RiskInfo riskInfo = new RiskInfo();
//        riskInfo.setSerial_id(idGenerator.nextUUID(null));
//
//        riskInfo.setExamtestresult_id(examTestResult.getSerial_id());
//        riskInfo.setCorp_risk_level(corp_risk_level);
//        riskInfo.setEn_invest_term(invest_term);
//        riskInfo.setEn_invest_kind(en_invest_kind);
//        riskInfo.setCooling_period(cooling_period);
//        riskInfoService.save(riskInfo);
//    }
//
//
//    private String getAdvice(List<ExamResultComment> list, BigDecimal score) {
//        for (ExamResultComment examResultComment : list) {
//            int right = BigDecimalUtil.compareTo(examResultComment.getMax_score(), score);
//            int left = BigDecimalUtil.compareTo(examResultComment.getMin_score(), score);
//
//            if (right >= 0 && left < 0) {
//                return examResultComment.getInvest_advice();
//            }
//        }
//        return null;
//    }
//
//
//    /**
//     * 考试题目评分信息查询
//     *
//     * @return
//     */
//    public Ws230212Response.MessageResponseBody questionAnswer(String resType, String custName, String certType, String certNo,
//                                                               String busScope, String busType, String srcOrderNo, List<Ws230212Request.Topic> topicList) {
//
//        log.info("questionAnswer()考试题目评分信息查询");
//
//        /**
//         * 请求体
//         */
//        Ws230212Request.MessageRequestBody requestBody = new Ws230212Request.MessageRequestBody();
//        requestBody.setCustName(custName);
////        requestBody.setCertType(certType);
//        // 海通证件类型转换
//        requestBody.setCertType(htBaseDictConvertUtils.getHtIdKind(certType));
//        requestBody.setCertNo(certNo);
//
//        requestBody.setResType(resType);
//        requestBody.setEmp(HtSecCommonUtils.SYSTEM_USER);
//        requestBody.setDept(HtSecCommonUtils.BRANCH_CODE);
//        requestBody.setOptDept(HtSecCommonUtils.BRANCH_CODE);
//        requestBody.setMacAddr(HtSecCommonUtils.SERVER_MAC);
//        requestBody.setEAppId("HTJSDWT");
//        //业务范文
//        if (StringUtils.isBlank(busScope))
//            busScope = "0"; //普通
//        requestBody.setBusScope(busScope);
//        requestBody.setAttribute("0"); //客户属性
//
//        requestBody.setBusType(busType);
//
//        if(StringUtils.isNotBlank(srcOrderNo)){
//            requestBody.setSrcOrderNo(srcOrderNo);
//        }
//        else{
//            requestBody.setSrcOrderNo("");
//        }
//
//        requestBody.setTopicList(topicList);
//
//        Ws230212Request ws230212Request = new Ws230212Request();
//        ws230212Request.setHeader(new Header());
//        ws230212Request.setBody(new EsbRequestXmlBean.Body<>(new EsbRequestXmlBean.Request<>(HtSecCommonUtils.getHtEsbHead(), requestBody)));
//
//        Ws230212Response response = esbWsService.ws230212(ws230212Request);
//
//        if (HtSecCommonUtils.isResponseSucceed(response)) {
//            return response.getBody().getRequestResponse().getResponseBody();
//        }
//        log.info("没有考试题目评分信息，失败信息为：", response);
//        return null;
//    }
//
//
//    /**
//     * 试题选项排序设置，海通需要使用serial_id升序排序
//     * @param queryWrapper 查询包装器
//     */
//    @Override
//    protected void questionOptionSortSetting(LambdaQueryWrapper<ExamQuestionOptions> queryWrapper) {
//        queryWrapper.orderByAsc(ExamQuestionOptions::getSerial_id);
//    }
//}
