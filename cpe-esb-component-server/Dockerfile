FROM openjdk:8
MAINTAINER sunyy <<EMAIL>>
RUN mkdir -p /data/cpe/cpe-esb
WORKDIR /data/cpe/cpe-esb

RUN mkdir -p /usr/local/runtime_config_root/counter

COPY build/libs/cpe-esb-component-server.jar /data/cpe/cpe-esb/
COPY fonts/  /usr/share/fonts/
COPY conf/version.txt /data/cpe/cpe-esb/conf/

COPY AnyChatServerSDK.ini  libanychatserver4java.so libanychatserversdk.so /opt/java/openjdk/jre/lib/
COPY AnyChatServerSDK.ini  libanychatserver4java.so libanychatserversdk.so /usr/lib64/

RUN fc-cache -f -v

# 安装所需的依赖库
RUN apt-get update && apt-get install -y

# 设置环境变量
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8

ENV CPE_XMS=512m
ENV CPE_MAXRAMPERCENTAGE=70.0
RUN echo "开始制作cpe-esb-component-server镜像：CPE_XMS：$CPE_XMS;CPE_MAXRAMPERCENTAGE：$CPE_MAXRAMPERCENTAGE"
ENTRYPOINT ["sh", "-c", "java -jar -Xms$CPE_XMS -XX:MaxRAMPercentage=$CPE_MAXRAMPERCENTAGE -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Duser.language=zh -Duser.country=CN /data/cpe/cpe-esb/cpe-esb-component-server.jar"]
