package com.cairh.cpe.esb.component.ocr.factory.represent.idcard;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.YouTuIdCardResponse;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.ZhengTongIdCardResponse;

public class IdCardZhengTongRepresentWrapper extends AbstractRepresentWrapper<ZhengTongIdCardResponse> {

    public IdCardZhengTongRepresentWrapper(ZhengTongIdCardResponse source) {
        super(source);
    }
}
