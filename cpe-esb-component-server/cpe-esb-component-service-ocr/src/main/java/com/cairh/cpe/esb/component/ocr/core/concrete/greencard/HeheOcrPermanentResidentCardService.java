package com.cairh.cpe.esb.component.ocr.core.concrete.greencard;

import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.counter.support.util.FastJsonUtil;
import com.cairh.cpe.esb.component.core.advise.support.AutoCloseDetectableResourceCreator;
import com.cairh.cpe.esb.component.ocr.core.OcrPermanentResidentCardService;
import com.cairh.cpe.esb.component.ocr.core.standard.greendcard.HeheGreenCardResponse;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.http.data.component.hehe.ComponentHeheHttpService;
import com.cairh.cpe.http.data.component.hehe.req.HeheGreencardRequest;
import jodd.util.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合合永居证识别ocr服务
 */
@Service
public class HeheOcrPermanentResidentCardService implements OcrPermanentResidentCardService {
    @Autowired
    private ComponentHeheHttpService httpService;

    @Override
    public OcrParseIdCardResponse greencardReconition(OcrParseIdCardRequest request) {
        HeheGreencardRequest heheGreencardRequest = new HeheGreencardRequest();
        heheGreencardRequest.setImage(AutoCloseDetectableResourceCreator.createTempResource(Base64.decode(request.getBase64_image())));
        String responseStr = httpService.permanentResidentReco(heheGreencardRequest);
        if(StringUtils.isBlank(responseStr)){
            throw new BizException(ErrorCode.ERR_SYSERROR,"合合永居证ocr识别返回结果为空");
        }
        HeheGreenCardResponse response = FastJsonUtil.parseObject(responseStr,HeheGreenCardResponse.class);
        if(response.getCode() != 200){
            throw new BizException(ErrorCode.ERR_SYSERROR,response.getMessage());
        }
        HeheGreenCardResponse.Detail detail = response.getResult().getDetails();
        OcrParseIdCardResponse responseDto = new OcrParseIdCardResponse();
        responseDto.setClient_name(detail.getChinese_name());
        responseDto.setEnglish_name(detail.getName());
        responseDto.setBirthday(Integer.valueOf(detail.getDate_of_birth()));
        responseDto.setId_no(detail.getId_number());
        responseDto.setIssued_depart(detail.getIssued_authority());
        responseDto.setNationality(detail.getNationality());
        responseDto.setId_begindate(Integer.valueOf(detail.getId_begindate()));
        responseDto.setId_enddate(Integer.valueOf(detail.getId_enddate()));
        responseDto.setClient_gender(detail.getSex());
        return responseDto;
    }
}