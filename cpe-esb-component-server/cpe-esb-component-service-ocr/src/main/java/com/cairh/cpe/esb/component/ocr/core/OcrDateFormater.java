package com.cairh.cpe.esb.component.ocr.core;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2023-02-13
 */
@Slf4j
public class OcrDateFormater {

    public static Integer format(String src_date) {
        if (StringUtils.isBlank(src_date)) {
            return 0;
        }
        try {
            src_date = src_date.replaceAll("\\.", StrUtil.DOT);
            src_date = src_date.replaceAll("/", StrUtil.DOT);
            src_date = src_date.replaceAll("-", StrUtil.DOT);
            src_date = src_date.replaceAll(":", StrUtil.DOT);

            src_date = src_date.replaceAll("年", StrUtil.DOT);
            src_date = src_date.replaceAll("月", StrUtil.DOT);
            src_date = src_date.replaceAll("日", StrUtil.DOT);

            src_date = src_date.replaceAll("长期", "3000.12.31");

            String[] dateParts = src_date.split("\\.");
            String result = dateParts[0];
            result += dateParts[1].length() == 2 ? dateParts[1] : "0" + dateParts[1];
            result += dateParts[2].length() == 2 ? dateParts[2] : "0" + dateParts[2];

            LocalDate.parse(result, DatePattern.PURE_DATE_FORMATTER);
            return Integer.valueOf(result.trim());
        } catch (Exception e) {
            log.warn("时间格式处理失败", e);
            return 0;
        }
    }

    public static Integer getBeginDate(String value) {
        String split[] = value.split("-");
        if (split.length > 2) {
            return 0;
        }
        if (split.length > 0) {
            return format(split[0]);
        }
        return 0;
    }

    public static Integer getEndDate(String value) {
        String split[] = value.split("-");
        if (split.length > 2) {
            return format(value);
        }
        if (split.length > 1) {
            return format(split[1]);
        }
        return 0;
    }
}
