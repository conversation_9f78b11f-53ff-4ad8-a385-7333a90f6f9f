package com.cairh.cpe.esb.component.ocr.core.concrete.face;

import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.FaceDetectService;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrFaceDetectResponse;
import com.cairh.cpe.esb.component.ocr.handler.face.OcrFaceDetectYiDaoServiceDecorator;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoFaceDetectRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class YiDaoOcFaceDetectService extends OcrFaceDetectYiDaoServiceDecorator implements FaceDetectService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;


    @Override
    public OcrFaceDetectResponse doFaceDetect(FaceDetectRequest request) {
        YiDaoFaceDetectRequest apiRequest = converterApplyFactory.convert(request, new YiDaoFaceDetectRequest());
        RepresentWrapper<?> representResponse = super.ocrFaceDetect(apiRequest);
        return converterApplyFactory.convert(representResponse, new OcrFaceDetectResponse());
    }

}
