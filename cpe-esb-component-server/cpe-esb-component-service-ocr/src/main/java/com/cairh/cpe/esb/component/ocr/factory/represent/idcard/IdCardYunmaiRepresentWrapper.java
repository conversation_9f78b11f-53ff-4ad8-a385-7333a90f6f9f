package com.cairh.cpe.esb.component.ocr.factory.represent.idcard;

import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.YunmaiIdCardResponse;

/**
 * yunmai idcard {@link RepresentWrapper}
 *
 * <AUTHOR>
 */
public class IdCardYunmaiRepresentWrapper extends AbstractRepresentWrapper<YunmaiIdCardResponse> {

    public IdCardYunmaiRepresentWrapper(YunmaiIdCardResponse source) {
        super(source);
    }
}
