package com.cairh.cpe.esb.component.ocr.converter.bankcard.yidao;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseBankCardRequest;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoBankCardRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cairh.cpe.esb.component.core.util.Base64PicUtil.delBase64Head;

/**
 * {@link OcrParseBankCardRequest} to {@link YiDaoBankCardRequest} converter
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OcrParseBankCardRequestToYiDaoBankCardRequestConverter implements Converter<OcrParseBankCardRequest, YiDaoBankCardRequest> {

    @Override
    public YiDaoBankCardRequest convert(OcrParseBankCardRequest source, YiDaoBankCardRequest target) {
        String base64_image = source.getBase64_image();
        base64_image = delBase64Head(base64_image);
        target.setImage_base64(base64_image);
        return target;
    }
}
