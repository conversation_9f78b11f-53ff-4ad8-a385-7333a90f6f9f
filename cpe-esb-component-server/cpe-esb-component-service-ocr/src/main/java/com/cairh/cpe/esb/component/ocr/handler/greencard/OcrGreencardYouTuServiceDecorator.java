package com.cairh.cpe.esb.component.ocr.handler.greencard;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.greencard.GreencardYouTuRepresentWrapper;
import com.cairh.cpe.http.data.component.youtu.ComponentYoutuHttpService;
import com.cairh.cpe.http.data.component.youtu.req.YouTuGreencardRequest;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

@NoArgsConstructor
@AllArgsConstructor
public class OcrGreencardYouTuServiceDecorator {
    @Autowired
    private ComponentYoutuHttpService httpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;

    public RepresentWrapper<?> permanResidentCardReco(YouTuGreencardRequest apiRequest) {
        String apiResponse = httpService.greenCard(apiRequest);
        return standarizedDelegatingProxy.transform(apiResponse, GreencardYouTuRepresentWrapper.class);
    }
}
