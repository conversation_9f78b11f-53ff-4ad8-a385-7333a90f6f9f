package com.cairh.cpe.esb.component.ocr.converter.face.youtu;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.http.data.component.youtu.req.YoutuFaceDetectRequest;
import org.springframework.stereotype.Component;

import static com.cairh.cpe.esb.component.core.util.Base64PicUtil.delBase64Head;

@Component
public class FaceDetectRequestConvertYoutuFaceDetectRequest implements Converter<FaceDetectRequest, YoutuFaceDetectRequest> {

    @Override
    public YoutuFaceDetectRequest convert(FaceDetectRequest source, YoutuFaceDetectRequest target) {
        String base64_image = source.getBase64_image();
        base64_image = delBase64Head(base64_image);

        YoutuFaceDetectRequest.FaceImage faceImage = new YoutuFaceDetectRequest.FaceImage();
        faceImage.setImage(base64_image);
        target.setRaw_image(faceImage);
        return target;
    }
}

