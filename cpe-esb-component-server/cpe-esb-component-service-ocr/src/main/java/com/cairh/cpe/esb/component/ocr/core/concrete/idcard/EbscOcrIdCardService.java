package com.cairh.cpe.esb.component.ocr.core.concrete.idcard;

import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.OcrIdCardService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrImageQualityResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.handler.idcard.OcrIdCardEbscServiceDecorator;
import com.cairh.cpe.http.data.component.ebsc.req.EbscIdCardCheckRequest;
import com.cairh.cpe.http.data.component.ebsc.req.EbscIdCardRequest;
import com.cairh.cpe.http.data.component.ebsc.req.EbscOcrCommonQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
  * ebsc implementation of {@link OcrIdCardService}, core ability use cpe-counter api
  *
  * <AUTHOR>
  */
 @Service
 public class EbscOcrIdCardService extends OcrIdCardEbscServiceDecorator implements OcrIdCardService {

     @Autowired
     private ConverterApplyFactory converterApplyFactory;

     @Override
     public OcrImageQualityResponse idCardQualityInspect(OcrImageQualityRequest request) {
         EbscIdCardCheckRequest apiRequest = converterApplyFactory.convert(request, new EbscIdCardCheckRequest());

         RepresentWrapper<?> representResponse = super.ocrIdCardCheck(apiRequest, new EbscOcrCommonQueryParam());
         return converterApplyFactory.convert(representResponse, new OcrImageQualityResponse());
     }

     @Override
     public OcrParseIdCardResponse idCardRecognition(OcrParseIdCardRequest request) {
         EbscIdCardRequest apiRequest = converterApplyFactory.convert(request, new EbscIdCardRequest());

         RepresentWrapper<?> representResponse = super.ocrIdCard(apiRequest,new EbscOcrCommonQueryParam());
         OcrParseIdCardResponse convert = converterApplyFactory.convert(representResponse, new OcrParseIdCardResponse());
         //处理光大身份证反面 识别为空提示报错
         if ("6B".equals(request.getImage_no())) {
             //反面判断 签发机构  有效期都为空 则是 错误图片
             if (StringUtils.isBlank(convert.getIssued_depart()) && Objects.isNull(convert.getId_begindate()) && Objects.isNull(convert.getId_enddate())) {
                 throw new BizException("身份证识别失败，无身份信息");
             }
         }else {
             //正面判断 客户姓名  民族 证件号码 证件地址 则是 错误图片
             if (StringUtils.isBlank(convert.getId_no())
                     && StringUtils.isBlank(convert.getClient_name())
                     && StringUtils.isBlank(convert.getNation_id())
                     && StringUtils.isBlank(convert.getId_address())) {
                 throw new BizException("身份证识别失败，无身份信息");
             }
             if (StringUtils.isBlank(convert.getId_no()) || StringUtils.isBlank(convert.getClient_name())) {
                 throw new BizException("身份证识别失败，无身份信息");
             }
         }
         return convert;
     }
 }
