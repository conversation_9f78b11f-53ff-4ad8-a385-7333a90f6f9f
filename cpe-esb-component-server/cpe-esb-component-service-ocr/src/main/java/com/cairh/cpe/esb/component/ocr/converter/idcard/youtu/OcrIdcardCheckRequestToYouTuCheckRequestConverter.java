package com.cairh.cpe.esb.component.ocr.converter.idcard.youtu;

import com.alibaba.excel.util.StringUtils;
import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.constants.OcrFields;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.http.data.component.ocr.idcard.youtu.req.YouTuIdCardCheckRequest;
import org.springframework.stereotype.Component;

/**
 * idcardCheck请求转换类：commonRequest--->YouTuIdcardCheckRequest
 */
@Component
public class OcrIdcardCheckRequestToYouTuCheckRequestConverter implements Converter<OcrImageQualityRequest, YouTuIdCardCheckRequest> {
	
	@Override
	public YouTuIdCardCheckRequest convert(OcrImageQualityRequest source, YouTuIdCardCheckRequest target) {
		target.setImage(source.getImage_data());
		if(OcrFields.IDENTITY_FRONT.containsKey(source.getImage_no())) {
			target.setCard_type(0);//人像面
		} else if(OcrFields.IDENTITY_BACK.containsKey(source.getImage_no())) {
			target.setCard_type(1);//国徽面
		}
		if(StringUtils.isNotBlank(source.getBorder_check())){
			target.setBorder_check_flag(Boolean.valueOf(source.getBorder_check()));
		}
		if(StringUtils.isNotBlank(source.getReshoot())){
			target.setEnable_reshoot(Boolean.valueOf(source.getReshoot()));
		}
		if(StringUtils.isNotBlank(source.getDetect_copy())){
			target.setEnable_detect_copy(Boolean.valueOf(source.getDetect_copy()));
		}
		if(StringUtils.isNotBlank(source.getDetect_ps())){
			target.setEnable_detect_ps(Boolean.valueOf(source.getDetect_ps()));
		}
		if(StringUtils.isNotBlank(source.getRet_portrait())){
			target.setRet_portrait_flag(Boolean.valueOf(source.getRet_portrait()));
		}
		if(StringUtils.isNotBlank(source.getMulticard_det())){
			target.setEnable_multicard_det(Boolean.valueOf(source.getMulticard_det()));
		}
		if(StringUtils.isNotBlank(source.getRet_image())){
			target.setRet_image(Boolean.valueOf(source.getRet_image()));
		}
		if(StringUtils.isNotBlank(source.getRecognize_warn_code())){
			target.setEnable_recognize_warn_code(Boolean.valueOf(source.getRecognize_warn_code()));
		}
		if(StringUtils.isNotBlank(source.getQuality_value())){
			target.setEnable_quality_value(Boolean.valueOf(source.getQuality_value()));
		}
		return target;
	}
}