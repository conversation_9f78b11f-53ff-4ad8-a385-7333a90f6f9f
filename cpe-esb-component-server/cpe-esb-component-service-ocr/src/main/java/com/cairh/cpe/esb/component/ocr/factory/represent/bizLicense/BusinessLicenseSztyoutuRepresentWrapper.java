package com.cairh.cpe.esb.component.ocr.factory.represent.bizLicense;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.bizLicense.SztyoutuBusinessLicenseResponse;

public class BusinessLicenseSztyoutuRepresentWrapper extends AbstractRepresentWrapper<SztyoutuBusinessLicenseResponse> {

    public BusinessLicenseSztyoutuRepresentWrapper(SztyoutuBusinessLicenseResponse source) {
        super(source);
    }
}
