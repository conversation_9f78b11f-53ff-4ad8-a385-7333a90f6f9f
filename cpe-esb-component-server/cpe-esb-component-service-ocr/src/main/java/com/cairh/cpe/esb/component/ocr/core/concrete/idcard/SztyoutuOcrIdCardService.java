package com.cairh.cpe.esb.component.ocr.core.concrete.idcard;

import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.OcrIdCardService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrImageQualityResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.handler.idcard.OcrIdCardSztyoutuServiceDecorator;
import com.cairh.cpe.http.data.component.sztyoutu.req.SztyoutuIdCardCheckRequest;
import com.cairh.cpe.http.data.component.sztyoutu.req.SztyoutuIdCardRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SztyoutuOcrIdCardService extends OcrIdCardSztyoutuServiceDecorator implements OcrIdCardService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Override
    public OcrImageQualityResponse idCardQualityInspect(OcrImageQualityRequest request) {
        SztyoutuIdCardCheckRequest apiRequest = converterApplyFactory.convert(request, new SztyoutuIdCardCheckRequest());
        RepresentWrapper<?> representResponse = super.ocrIdCardCheck(apiRequest);
        return converterApplyFactory.convert(representResponse, new OcrImageQualityResponse());
    }

    @Override
    public OcrParseIdCardResponse idCardRecognition(OcrParseIdCardRequest request) {
        SztyoutuIdCardRequest apiRequest = converterApplyFactory.convert(request, new SztyoutuIdCardRequest());
        RepresentWrapper<?> representResponse = super.ocrIdCard(apiRequest);
        return converterApplyFactory.convert(representResponse, new OcrParseIdCardResponse());
    }
}
