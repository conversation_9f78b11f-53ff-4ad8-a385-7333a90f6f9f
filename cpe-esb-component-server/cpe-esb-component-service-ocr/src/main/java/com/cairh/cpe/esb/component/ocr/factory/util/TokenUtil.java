package com.cairh.cpe.esb.component.ocr.factory.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 获取token
 */
public class TokenUtil {
    public static final String PARAM_SIG_KEY = "Signature";
    public static final String PARAM_ACCESS_ID_KEY = "AccessKeyId";

    public static final String PARAM_ACTION_KEY = "Action";
    public static final String PARAM_ACTION_VALUE = "CreateToken";

    public static final String PARAM_FORMAT_KEY = "Format";
    public static final String PARAM_FORMAT_VALUE = "JSON";

    public static final String PARAM_REGION_ID_KEY = "RegionId";
    public static final String PARAM_REGION_ID_VALUE = "cn-shanghai";

    public static final String PARAM_SIG_METHOD_KEY = "SignatureMethod";
    public static final String PARAM_SIG_METHOD_VALUE = "HMAC-SHA1";

    public static final String PARAM_SIG_NONCE_KEY = "SignatureNonce";
    public static final String PARAM_SIG_NONCE_VALUE = "b924c8c3-6d03-4c5d-ad36-d984d3116788";

    public static final String PARAM_SIG_VERSION_KEY = "SignatureVersion";
    public static final String PARAM_SIG_VERSION_VALUE = "1.0";

    public static final String PARAM_TIMESTAMP_KEY = "Timestamp";
    public static final String PARAM_TIMESTAMP_VALUE = "2019-04-18T08%3A32%3A31Z";

    public static final String PARAM_VERSION_KEY = "Version";
    public static final String PARAM_VERSION_VALUE = "2019-02-28";

    public static final String ALGORITHM_NAME = "HmacSHA1";
    public static final String FORMAT_ISO8601 = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String TIME_ZONE = "GMT";
    public static final String URL_ENCODING = "UTF-8";
    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
    public static final String uuid = UUID.randomUUID().toString();

    public static String getToken(String domain, String accessKeyId, String accessKeySecret) {
        Map<String, String> sigParam = new HashMap<>();
        sigParam.put(PARAM_ACCESS_ID_KEY, accessKeyId);
        sigParam.put(PARAM_ACTION_KEY, PARAM_ACTION_VALUE);
        sigParam.put(PARAM_FORMAT_KEY, PARAM_FORMAT_VALUE);
        sigParam.put(PARAM_REGION_ID_KEY, PARAM_REGION_ID_VALUE);
        sigParam.put(PARAM_SIG_METHOD_KEY, PARAM_SIG_METHOD_VALUE);
        sigParam.put(PARAM_SIG_NONCE_KEY, PARAM_SIG_NONCE_VALUE);
        sigParam.put(PARAM_SIG_VERSION_KEY, PARAM_SIG_VERSION_VALUE);
        sigParam.put(PARAM_TIMESTAMP_KEY, PARAM_TIMESTAMP_VALUE);
        sigParam.put(PARAM_VERSION_KEY, PARAM_VERSION_VALUE);
        String strQuery = canonicalizedQuery(sigParam);
        String strSign = createStringToSign("GET", "/", strQuery);
        String strSig = sign(strSign, accessKeySecret + "&");
        sigParam.put(PARAM_SIG_KEY, strSig);

        String getTokenUrl = "https://" + domain + "/gettoken?" + sigParam.entrySet().stream().map(p -> p.getKey() + "=" + p.getValue()).reduce((p1, p2) -> p1 + "&" + p2).orElse("");
        String tokenJson = HttpUtil.sendGet(getTokenUrl);
        System.out.println("getTokenUrl:" + getTokenUrl);
        JSONObject result = JSON.parseObject(tokenJson);
        if (result.getJSONObject("Token") != null) {
            return result.getJSONObject("Token").getString("Id");
        }

        return null;
    }

    public static void main(String[] args) {
        String domain = "aip.tongtongcf.com";
        String accessKeyId = "591c366e7b6b5c2518a65995ce6160b2";
        String accessKeySecret = "d65ad099e1b0c913edcd096f88fda234";
        String token = getToken(domain, accessKeyId, accessKeySecret);
        System.out.println("token:" + token);
    }

    /**
     * 获取时间戳
     * 必须符合ISO8601规范，并需要使用UTC时间，时区为+0
     */
    public static String getISO8601Time(Date date) {
        Date nowDate = date;
        if (null == date) {
            nowDate = new Date();
        }
        SimpleDateFormat df = new SimpleDateFormat(FORMAT_ISO8601);
        df.setTimeZone(new SimpleTimeZone(0, TIME_ZONE));
        return df.format(nowDate);
    }

    /**
     * 获取UUID
     */
    public static String getUniqueNonce() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString();
    }

    /**
     * URL编码
     * 使用UTF-8字符集按照 RFC3986 规则编码请求参数和参数取值
     */
    public static String percentEncode(String value) throws UnsupportedEncodingException {
        return value != null ? URLEncoder.encode(value, URL_ENCODING).replace("+", "%20")
                .replace("*", "%2A").replace("%7E", "~") : null;
    }

    /***
     * 将参数排序后，进行规范化设置，组合成请求字符串
     * @param queryParamsMap   所有请求参数
     * @return 规范化的请求字符串
     */
    public static String canonicalizedQuery(Map<String, String> queryParamsMap) {
        String[] sortedKeys = queryParamsMap.keySet().toArray(new String[]{});
        Arrays.sort(sortedKeys);
        String queryString = null;
        try {
            StringBuilder canonicalizedQueryString = new StringBuilder();
            for (String key : sortedKeys) {
                canonicalizedQueryString.append("&")
                        .append(percentEncode(key)).append("=")
                        .append(queryParamsMap.get(key));
            }
            queryString = canonicalizedQueryString.toString().substring(1);
            System.out.println("规范化后的请求参数串：" + queryString);
        } catch (UnsupportedEncodingException e) {
            System.out.println("UTF-8 encoding is not supported.");
            e.printStackTrace();
        }
        return queryString;
    }

    /***
     * 构造签名字符串
     * @param method       HTTP请求的方法
     * @param urlPath      HTTP请求的资源路径
     * @param queryString  规范化的请求字符串
     * @return 签名字符串
     */
    public static String createStringToSign(String method, String urlPath, String queryString) {
        String stringToSign = null;
        try {
            StringBuilder strBuilderSign = new StringBuilder();
            strBuilderSign.append(method);
            strBuilderSign.append("&");
            strBuilderSign.append(percentEncode(urlPath));
            strBuilderSign.append("&");
            strBuilderSign.append(percentEncode(queryString));
//            strBuilderSign.append(queryString);
            stringToSign = strBuilderSign.toString();
            System.out.println("构造的签名字符串：" + stringToSign);
        } catch (UnsupportedEncodingException e) {
            System.out.println("UTF-8 encoding is not supported.");
            e.printStackTrace();
        }
        return stringToSign;
    }

    /***
     * 计算签名
     * @param stringToSign      签名字符串
     * @param accessKeySecret   阿里云AccessKey Secret加上与号&
     * @return 计算得到的签名
     */
    public static String sign(String stringToSign, String accessKeySecret) {
        try {
            Mac mac = Mac.getInstance(ALGORITHM_NAME);
            mac.init(new SecretKeySpec(
                    accessKeySecret.getBytes(URL_ENCODING),
                    ALGORITHM_NAME
            ));
            byte[] signData = mac.doFinal(stringToSign.getBytes(URL_ENCODING));
            String signBase64 = DatatypeConverter.printBase64Binary(signData);
            System.out.println("计算的得到的签名：" + signBase64);
            String signUrlEncode = percentEncode(signBase64);
            System.out.println("UrlEncode编码后的签名：" + signUrlEncode);
            return signUrlEncode;
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException(e.toString());
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException(e.toString());
        } catch (InvalidKeyException e) {
            throw new IllegalArgumentException(e.toString());
        }
    }
}
