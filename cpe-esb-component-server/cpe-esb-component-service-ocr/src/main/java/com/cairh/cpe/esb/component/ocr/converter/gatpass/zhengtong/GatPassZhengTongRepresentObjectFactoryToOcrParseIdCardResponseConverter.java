package com.cairh.cpe.esb.component.ocr.converter.gatpass.zhengtong;

import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.standard.gatpass.YiDaoGatPassResponse;
import com.cairh.cpe.esb.component.ocr.core.standard.gatpass.ZhengTongGatPassResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.factory.represent.gatpass.GatPassYiDaoRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.gatpass.GatPassZhengTongRepresentWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GatPassZhengTongRepresentObjectFactoryToOcrParseIdCardResponseConverter implements Converter<GatPassZhengTongRepresentWrapper, OcrParseIdCardResponse> {
    @Override
    public OcrParseIdCardResponse convert(GatPassZhengTongRepresentWrapper source, OcrParseIdCardResponse target) {
        log.info("证通港澳台通行证识别response对象封装:{}", source.getObject());
        if (Objects.isNull(source.getObject())) {
            log.error("港澳台通行证识别失败，识别结果：{}", source.getObject());
            throw new BizException("港澳台通行证识别失败");
        }
        OcrParseIdCardResponse response = new OcrParseIdCardResponse();
        ZhengTongGatPassResponse data = source.getObject();
        //接口原返回数据->组件规范返回值
        if (Objects.nonNull(data)) {
            response.setPass_number(data.getItem_list().stream().filter(item -> "id_card_number".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));
            //生日
//            String s = data.getItemList().stream().filter(item -> "birth_date".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0);
//            response.setBirthday();

            response.setClient_name(data.getItem_list().stream().filter(item -> "name".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));
            response.setClient_gender(data.getItem_list().stream().filter(item -> "gender".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));
            response.setIssued_place(data.getItem_list().stream().filter(item -> "issue_place".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));

//            response.setRelease_date(data.getItemList().stream().filter(item -> "card_valid_period".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));

            response.setName_pinyin(data.getItem_list().stream().filter(item -> "name_pinyin".equals(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList()).get(0));

        }
        return response;
    }
}
