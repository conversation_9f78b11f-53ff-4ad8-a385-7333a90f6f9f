package com.cairh.cpe.esb.component.ocr.core.concrete.idcard;

import cn.hutool.core.codec.Base64;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.counter.support.util.FastJsonUtil;
import com.cairh.cpe.esb.component.core.constant.BusiConstant;
import com.cairh.cpe.esb.component.ocr.core.OcrDateFormater;
import com.cairh.cpe.esb.component.ocr.core.OcrIdCardService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrImageQualityResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.http.data.component.hehe.ComponentHeheHttpService;
import com.cairh.cpe.http.data.component.hehe.req.HeheIdCardRequest;
import com.cairh.cpe.http.data.component.hehe.resp.HeheIdCardResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合合身份证识别
 */
@Slf4j
@Service
public class HeheOcrIdCardService implements OcrIdCardService {

    @Autowired
    private ComponentHeheHttpService httpService;

    @Override//身份证识别
    public OcrParseIdCardResponse idCardRecognition(OcrParseIdCardRequest request) {
        HeheIdCardRequest heheRequest = new HeheIdCardRequest();
        byte[] bytes = Base64.decode(request.getBase64_image());
        String responseStr = httpService.ocrIdCard(bytes, heheRequest);
        if(StringUtils.isBlank(responseStr)){
            throw new BizException(ErrorCode.ERR_SYSERROR, "合合ocr识别返回结果为空");
        }
        HeheIdCardResponse heheResponse = FastJsonUtil.parseObject(responseStr,HeheIdCardResponse.class);
        if(!StringUtils.equals(String.valueOf(heheResponse.getError_code()),"0")){
            throw new BizException(ErrorCode.ERR_SYSERROR, "合合ocr识别结果异常："+heheResponse.getError_msg());
        }
        OcrParseIdCardResponse response = new OcrParseIdCardResponse();
        //正面信息
        response.setClient_name(heheResponse.getName());
        response.setClient_gender(heheResponse.getSex());//男
        response.setNation_id(heheResponse.getPeople());//汉
        response.setBirthday(OcrDateFormater.format(heheResponse.getBirthday()));//xxxx年7月29日
        response.setId_address(heheResponse.getAddress());
        response.setId_no(heheResponse.getId_number());
        //反面信息
        response.setIssued_depart(heheResponse.getIssue_authority());//签证机关
        if(StringUtils.isNotBlank(heheResponse.getValidity())){//有效日期：2018.07.20-2038.07.20
            String[] validityArr = heheResponse.getValidity().split("-");
            if(validityArr.length == 2) {
                response.setId_begindate(OcrDateFormater.format(validityArr[0]));
                response.setId_enddate(OcrDateFormater.format(validityArr[1]));
            }
        }
        return response;
    }

    @Override//质检
    public OcrImageQualityResponse idCardQualityInspect(OcrImageQualityRequest request) {
        return null;
    }
}