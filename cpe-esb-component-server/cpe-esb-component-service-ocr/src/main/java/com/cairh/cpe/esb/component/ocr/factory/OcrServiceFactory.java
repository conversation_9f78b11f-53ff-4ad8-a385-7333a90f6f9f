package com.cairh.cpe.esb.component.ocr.factory;

import com.cairh.cpe.esb.component.core.factory.BaseServiceFactory;
import com.cairh.cpe.esb.component.ocr.core.OcrService;

import java.util.function.Supplier;

/**
 * OCR implementation of {@link BaseServiceFactory}, simply delegate getService operation
 * to getOcrService
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface OcrServiceFactory extends BaseServiceFactory<OcrService> {

    @Override
    default OcrService getService(Supplier<String> serviceNameSupplier) {
        return getOcrService(serviceNameSupplier);
    }

    OcrService getOcrService(Supplier<String> serviceNameSupplier);
}
