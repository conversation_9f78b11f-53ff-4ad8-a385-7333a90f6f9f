package com.cairh.cpe.esb.component.ocr.core;

import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrImageQualityResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;

/**
 * ocr idcard extension of {@link OcrService}, simply delegate to idCard prefix operation
 *
 * <AUTHOR>
 */
public interface OcrIdCardService extends OcrService {

    @Override
    default OcrImageQualityResponse qualityInspect(OcrImageQualityRequest request) {
        return idCardQualityInspect(request);
    }

    OcrImageQualityResponse idCardQualityInspect(OcrImageQualityRequest request);

    @Override
    default OcrParseIdCardResponse recognition(OcrParseIdCardRequest request) {
        return idCardRecognition(request);
    }

    OcrParseIdCardResponse idCardRecognition(OcrParseIdCardRequest request);
}
