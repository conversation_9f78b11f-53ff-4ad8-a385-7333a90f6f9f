package com.cairh.cpe.esb.component.ocr.handler.idcard;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.idcard.GtjaOcrIdCardRepresentWrapper;
import com.cairh.cpe.http.data.component.gtja.ComponentGtjaHttpService;
import com.cairh.cpe.http.data.component.gtja.req.GtjaOcrParseIdCardRequest;
import com.cairh.cpe.http.data.component.gtja.req.GtjaOcrQueryParam;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class OcrIdCardGtjaServiceDecorator {

    @Autowired
    private ComponentGtjaHttpService componentGtjaHttpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;

    public RepresentWrapper<?> ocrIdCard(@NonNull GtjaOcrParseIdCardRequest apiRequest, GtjaOcrQueryParam queryParam) {
        String apiResponse = componentGtjaHttpService.ocrIdCard(apiRequest, queryParam);
        return standarizedDelegatingProxy.transform(apiResponse, GtjaOcrIdCardRepresentWrapper.class);
    }
}
