package com.cairh.cpe.esb.component.ocr.core;

import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrFaceDetectResponse;

/**
 * ocr FaceDetect extension of {@link FaceService}, simply delegate to idCard prefix operation
 *
 * <AUTHOR>
 */
public interface FaceDetectService extends FaceService {

    @Override
    default OcrFaceDetectResponse faceDetect(FaceDetectRequest request) {
        return doFaceDetect(request);
    }

    OcrFaceDetectResponse doFaceDetect(FaceDetectRequest request);
}
