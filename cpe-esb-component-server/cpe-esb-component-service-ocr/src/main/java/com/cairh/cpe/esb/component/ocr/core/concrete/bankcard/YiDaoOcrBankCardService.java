package com.cairh.cpe.esb.component.ocr.core.concrete.bankcard;

import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.OcrBankCardService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseBankCardCheckRequest;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseBankCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseBankCardCheckResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseBankCardResponse;
import com.cairh.cpe.esb.component.ocr.handler.bankcard.OcrBankCardYiDaoServiceDecorator;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoBankCardRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * yidao implementation of {@link OcrBankCardService}, core ability use cpe-counter api
 *
 * <AUTHOR>
 */
@Lazy
@Service
public class YiDaoOcrBankCardService extends OcrBankCardYiDaoServiceDecorator implements OcrBankCardService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;
    @Override
    public OcrParseBankCardCheckResponse bankCardCheckInspect(OcrParseBankCardCheckRequest request) {
        throw new BizException("暂不支持银行卡用户信息核验");
    }
    @Override
    public OcrParseBankCardResponse recognition(OcrParseBankCardRequest req) {
        YiDaoBankCardRequest request = converterApplyFactory.convert(req, new YiDaoBankCardRequest());
        RepresentWrapper<?> extResponse = super.ocrBankCard(request);
        return converterApplyFactory.convert(extResponse, new OcrParseBankCardResponse());
    }
}
