package com.cairh.cpe.esb.component.ocr.factory;

import com.cairh.cpe.esb.component.core.factory.BaseServiceFactory;
import com.cairh.cpe.esb.component.ocr.core.FaceService;

import java.util.function.Supplier;

/**
 * OCR implementation of {@link BaseServiceFactory}, simply delegate getService operation
 * to getOcrFaceService
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface FaceServiceFactory extends BaseServiceFactory<FaceService> {

    @Override
    default FaceService getService(Supplier<String> serviceNameSupplier) {
        return getFaceService(serviceNameSupplier);
    }

    FaceService getFaceService(Supplier<String> serviceNameSupplier);
}
