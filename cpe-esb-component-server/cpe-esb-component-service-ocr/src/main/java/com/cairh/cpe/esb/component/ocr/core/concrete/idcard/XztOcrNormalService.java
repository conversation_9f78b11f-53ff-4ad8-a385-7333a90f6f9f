package com.cairh.cpe.esb.component.ocr.core.concrete.idcard;

import com.cairh.cpe.counter.support.util.MD5Util;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.util.CompositePropertySourcesUtil;
import com.cairh.cpe.esb.component.ocr.core.OcrNormalService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseNormalRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseNormalResponse;
import com.xyzq.mid.sdk.client.Service131MidSdkClientApi;
import com.xyzq.mid.sdk.dto.ResponseDTO;
import com.xyzq.mid.sdk.model.service131.CallFunction2002RespbodyResult;
import com.xyzq.mid.sdk.model.service131.PostCall131Function19RO;
import com.xyzq.mid.sdk.model.service131.PostCall131Function19VO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class XztOcrNormalService implements OcrNormalService {

    @Autowired
    private Service131MidSdkClientApi service131MidSdkClientApi;

    public List<OcrParseNormalResponse> ocrNormal(OcrParseNormalRequest request) {

        PostCall131Function19RO ro = new PostCall131Function19RO();

//        String mguserid = "CRHOOPENAC";
//        String mgpassword = "DE1F0F416B0229F35BE9C9433A2DCA72";
//        String opchannel = "2";
//        String opstation = "127.0.0.1";
//        String md5key = "123456";
        String mguserid = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_OCR_ZXT_MID_USERID, "请配置兴证通中台userid");
        String mgpassword = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_OCR_ZXT_MID_PASSWORD, "请配置兴证通中台password");
        String md5key = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_OCR_ZXT_MID_MD5KEY, "请配置兴证通中台md5key");
        String opchannel = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_OCR_ZXT_MID_OPCHANNEL, "请配置兴证通中台opchannel");
        String opstation = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_OCR_ZXT_MID_OPSTATION, "请配置兴证通中台opstation");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String md5 = MD5Util.getMD5String(mguserid + mgpassword + opchannel + opstation + timestamp + md5key);
        ro.setImagebase64(request.getBase64_image());
        ro.setOpstation(opstation);
        ro.setOpchannel(opchannel);
        ro.setMd5sum(md5);
        ro.setTimestamp(timestamp);
        log.info("131.19兴证通通用文本识别服务入参打印:opstation={}, opchannel={},md5={},timestamp={},picdata=image", new Object[]{opstation, opchannel, md5, timestamp});
        ResponseDTO<PostCall131Function19VO> responseDTO = service131MidSdkClientApi.postCall131Function19(ro);
        log.info("131.19兴证通通用文本识别服务msg:{}, data：{}", responseDTO.getMsg(), responseDTO.getData());
        PostCall131Function19VO data = responseDTO.getData();
        List<OcrParseNormalResponse> responses = new ArrayList<OcrParseNormalResponse>();

        //接口原返回数据->组件规范返回值
        if (Objects.nonNull(data)) {
            for(CallFunction2002RespbodyResult result :data.getResult()){
                OcrParseNormalResponse response = new OcrParseNormalResponse();
                response.setWords(result.getWords());
                response.setType(result.getType());
                response.setScore(result.getScore());
                responses.add(response);
            }
        }else{
            log.info("131.19兴证通通用文本识别服务respcode={}, respdetails={}", responseDTO.getHeaders().get("respcode"), responseDTO.getHeaders().get("respdetails"));
        }
        return responses;
    }
}
