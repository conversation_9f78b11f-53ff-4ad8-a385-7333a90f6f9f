package com.cairh.cpe.esb.component.ocr.factory.support;

import com.cairh.cpe.esb.component.ocr.core.OcrService;
import com.cairh.cpe.esb.component.ocr.factory.OcrForeignerGreencardServiceFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import java.util.function.Supplier;

@Service
@Primary
public class BeanFactoryOcrGreencardServiceFactory extends WebApplicationObjectSupport implements OcrForeignerGreencardServiceFactory {
    @Override
    public OcrService getOcrGreencardService(Supplier<String> serviceNameSupplier) {
        return getWebApplicationContext().getBean(serviceNameSupplier.get(), OcrService.class);
    }
}
