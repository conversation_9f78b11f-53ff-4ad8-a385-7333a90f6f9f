package com.cairh.cpe.esb.component.ocr.core.concrete.face;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.FaceDetectService;
import com.cairh.cpe.esb.component.ocr.core.standard.face.EbscFaceDetectRepresent;
import com.cairh.cpe.esb.component.ocr.core.standard.face.EbscFaceDetectResponse;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.support.ExocrFacePosition;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrFaceDetectResponse;
import com.cairh.cpe.esb.component.ocr.handler.face.OcrFaceDetectEbscServiceDecorator;
import com.cairh.cpe.http.data.component.ebsc.ComponentEbscHttpService;
import com.cairh.cpe.http.data.component.ebsc.req.EbscFaceDetectRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EbscOcFaceDetectService extends OcrFaceDetectEbscServiceDecorator implements FaceDetectService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Autowired
    private ComponentEbscHttpService componentEbscHttpService;

    @Override
    public OcrFaceDetectResponse doFaceDetect(FaceDetectRequest request) {
        EbscFaceDetectRequest apiRequest = converterApplyFactory.convert(request, new EbscFaceDetectRequest());
//        RepresentWrapper<?> representResponse = super.ocrFaceDetect(apiRequest);

        String extResponse = componentEbscHttpService.ocrFaceDetect(apiRequest);
        log.info("光大证券人像识别服务response入参：{}",extResponse);
        EbscFaceDetectRepresent represent = JSON.parseObject(extResponse, EbscFaceDetectRepresent.class);
        OcrFaceDetectResponse response = new OcrFaceDetectResponse();
        EbscFaceDetectResponse data = represent.getData();
        //接口原返回数据->组件规范返回值
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getFaces())) {
            throw new BizException("-1", "识别结果列表为空");
        }
        if (Objects.nonNull(data)) {
            List<EbscFaceDetectResponse.FaceData> face_list = data.getFaces();
            response.setCount(face_list.size() + "");


            response.setPass_score("");
            response.setDetect_result("1");

            List<OcrFaceDetectResponse.FacePosition> collect = face_list.stream().map(facePosition -> {
                OcrFaceDetectResponse.FacePosition fp = new OcrFaceDetectResponse.FacePosition();
                if (Objects.isNull(facePosition)){
                    return fp;
                }
                fp.setCoord_x(facePosition.getX());
                fp.setCoord_y(facePosition.getY());
                fp.setWidth(facePosition.getWidth());
                fp.setHeight(facePosition.getHeight());
                return fp;
            }).collect(Collectors.toList());
            response.setFacePositions(collect);
        }
        log.info("光大证券人像识别服务response出参：{}",response);
        return response;
    }

}
