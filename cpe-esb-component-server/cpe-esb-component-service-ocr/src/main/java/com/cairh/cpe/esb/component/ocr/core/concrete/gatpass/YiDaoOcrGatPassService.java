package com.cairh.cpe.esb.component.ocr.core.concrete.gatpass;

import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.OcrGATPassService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.handler.gatpass.OcrGatPassYiDaoServiceDecorator;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoGatPassRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * yidao implementation of {@link OcrGATPassService}, core ability use cpe-counter api
 *
 * <AUTHOR>
 */
@Service
public class YiDaoOcrGatPassService extends OcrGatPassYiDaoServiceDecorator implements OcrGATPassService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Override
    public OcrParseIdCardResponse gatPassRecognition(OcrParseIdCardRequest req) {
        YiDaoGatPassRequest request = converterApplyFactory.convert(req, new YiDaoGatPassRequest());
        RepresentWrapper<?> extResponse = super.ocrGatPass(request);
        return converterApplyFactory.convert(extResponse, new OcrParseIdCardResponse());
    }
}
