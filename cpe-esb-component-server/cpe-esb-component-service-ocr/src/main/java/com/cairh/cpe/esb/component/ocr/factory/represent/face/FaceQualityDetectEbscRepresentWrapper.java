package com.cairh.cpe.esb.component.ocr.factory.represent.face;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.face.EbscFaceQualityDetectRepresent;

public class FaceQualityDetectEbscRepresentWrapper extends AbstractRepresentWrapper<EbscFaceQualityDetectRepresent> {

    public FaceQualityDetectEbscRepresentWrapper(EbscFaceQualityDetectRepresent source) {
        super(source);
    }
}
