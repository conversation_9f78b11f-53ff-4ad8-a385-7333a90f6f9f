package com.cairh.cpe.esb.component.ocr.core.standard.greendcard;

import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.esb.component.ocr.core.OcrDateFormater;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Data
@Accessors(chain = true)
public class HeheGreenCardResponse {
    private String message;
    private Integer code;
    private Result result;


    @Data
    public static class Result {
        private Detail details;
    }

    @Data
    public static class Detail {
        private String chinese_name;
        private String date_of_birth;
        private String id_number;
        private String issued_authority;
        private String name;
        private String nationality;
        private String period_of_validity;
        private String sex;

        public String getChinese_name() {
            return getValue(chinese_name);
        }

        public String getDate_of_birth() {
            if (null == getValue(date_of_birth)) {
                return "0";
            }
            String org = getValue(date_of_birth);
            long ordInt = org.chars().filter(Character::isDigit).count();
            // 判断字符长度是否为8而且是8个数字才会直接返回
            if (org.length() == 8 && 8 == ordInt) {
                return org;
            }
            return OcrDateFormater.format(org).toString();
        }

        public String getId_number() {
            return getValue(id_number);
        }

        public String getIssued_authority() {
            return getValue(issued_authority);
        }

        public String getName() {
            return getValue(name);
        }

        public String getNationality() {
            return getValue(nationality);
        }

        public String getSex() {
            return getValue(sex);
        }

        public String getId_begindate() {
            if (null == getValue(period_of_validity)) {
                return "0";
            }
            String[] days = getValue(period_of_validity).split("-");
            if (days.length <= 0) {
                return "0";
            }
            return OcrDateFormater.format(days[0].trim()).toString();
        }

        public String getId_enddate() {
            if (null == getValue(period_of_validity)) {
                return "0";
            }
            String[] days = getValue(period_of_validity).split("-");
            if (days.length <= 1) {
                return "0";
            }
            if (null == days[1] || "".equals(days[1].trim())) {
                return "0";
            }
            return OcrDateFormater.format(days[1]).toString();
        }

        private String getValue(String json_str) {
            String value = "";
            if (StringUtils.isNotBlank(json_str)) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(json_str);
                    value = jsonObject.getString("value");
                } catch (Exception e) {
                    log.error("永居证识别结果json格式化失败,json为：{}", json_str);
                    log.error("永居证识别结果格式化失败：", e);
                }
            }
            return value;
        }
    }

    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }


}