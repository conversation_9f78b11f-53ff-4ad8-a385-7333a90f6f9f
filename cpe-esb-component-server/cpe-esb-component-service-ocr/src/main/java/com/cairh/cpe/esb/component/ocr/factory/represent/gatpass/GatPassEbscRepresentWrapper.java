package com.cairh.cpe.esb.component.ocr.factory.represent.gatpass;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.gatpass.EbscGatPassRepresent;

public class GatPassEbscRepresentWrapper extends AbstractRepresentWrapper<EbscGatPassRepresent> {

    public GatPassEbscRepresentWrapper(EbscGatPassRepresent source) {
        super(source);
    }
}
