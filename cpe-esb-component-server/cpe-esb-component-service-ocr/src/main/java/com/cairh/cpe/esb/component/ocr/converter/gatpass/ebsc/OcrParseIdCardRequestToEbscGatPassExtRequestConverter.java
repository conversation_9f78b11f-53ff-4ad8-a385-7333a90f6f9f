package com.cairh.cpe.esb.component.ocr.converter.gatpass.ebsc;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.core.advise.support.AutoCloseDetectableResourceCreator;
import com.cairh.cpe.esb.component.file.utils.ImageBase64Util;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.http.data.component.ebsc.req.EbseGatPassRequest;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoGatPassRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cairh.cpe.esb.component.core.util.Base64PicUtil.delBase64Head;

/**
 * {@link OcrParseIdCardRequest} to {@link YiDaoGatPassRequest} converter
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OcrParseIdCardRequestToEbscGatPassExtRequestConverter implements Converter<OcrParseIdCardRequest, EbseGatPassRequest> {

    @Override
    public EbseGatPassRequest convert(OcrParseIdCardRequest source, EbseGatPassRequest target) {
        String image_data = source.getBase64_image();
        image_data = delBase64Head(image_data);
        byte[] binary = ImageBase64Util.getStrToBytes(image_data);
        target.setImage(AutoCloseDetectableResourceCreator.createTempResource(binary));
        return target;
    }
}
