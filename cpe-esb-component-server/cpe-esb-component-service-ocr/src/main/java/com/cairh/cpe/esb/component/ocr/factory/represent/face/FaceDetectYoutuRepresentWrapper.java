package com.cairh.cpe.esb.component.ocr.factory.represent.face;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.face.SztYoutuFaceDetectRepresent;
import com.cairh.cpe.esb.component.ocr.core.standard.face.YoutuFaceDetectRepresent;

public class FaceDetectYoutuRepresentWrapper extends AbstractRepresentWrapper<YoutuFaceDetectRepresent> {

    public FaceDetectYoutuRepresentWrapper(YoutuFaceDetectRepresent source) {
        super(source);
    }
}
