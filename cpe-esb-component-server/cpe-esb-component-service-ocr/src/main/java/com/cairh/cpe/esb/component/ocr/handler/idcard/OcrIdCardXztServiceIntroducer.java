package com.cairh.cpe.esb.component.ocr.handler.idcard;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.idcard.IdCardXztRepresentWrapper;
import com.cairh.cpe.http.data.component.xzt.ComponentXztHttpService;
import com.cairh.cpe.http.data.component.xzt.req.XztIdCardCheckRequest;
import com.cairh.cpe.protocol.gateway.handler.component.xzt.XztQueryParam;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class OcrIdCardXztServiceIntroducer {

    @Autowired
    private ComponentXztHttpService httpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;


    public RepresentWrapper<?> ocrIdCard(XztIdCardCheckRequest request) {
        String apiResponse = httpService.ocrIdCard(request, new XztQueryParam());
        return standarizedDelegatingProxy.transform(apiResponse, IdCardXztRepresentWrapper.class);
    }
}
