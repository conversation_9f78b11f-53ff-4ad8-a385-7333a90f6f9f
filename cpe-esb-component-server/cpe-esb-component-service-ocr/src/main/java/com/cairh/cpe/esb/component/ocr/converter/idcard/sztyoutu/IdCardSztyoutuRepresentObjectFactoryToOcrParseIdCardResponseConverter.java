package com.cairh.cpe.esb.component.ocr.converter.idcard.sztyoutu;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.SztyoutuIdCardResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.factory.represent.idcard.IdCardSztyoutuRepresentWrapper;
import org.springframework.stereotype.Component;

@Component
public class IdCardSztyoutuRepresentObjectFactoryToOcrParseIdCardResponseConverter implements Converter<IdCardSztyoutuRepresentWrapper, OcrParseIdCardResponse> {

    @Override
    public OcrParseIdCardResponse convert(IdCardSztyoutuRepresentWrapper source, OcrParseIdCardResponse target) {
        SztyoutuIdCardResponse sztyoutuIdCardResponse = source.getObject();

        target.setClient_name(sztyoutuIdCardResponse.getName());
        target.setId_no(sztyoutuIdCardResponse.getId());
        target.setClient_gender(sztyoutuIdCardResponse.getSex());
        target.setNation_id(sztyoutuIdCardResponse.getNation());
        target.setBirthday(Integer.parseInt(sztyoutuIdCardResponse.getBirth()));
        target.setId_address(sztyoutuIdCardResponse.getAddress());

        target.setIssued_depart(sztyoutuIdCardResponse.getAuthority());
        target.setId_begindate(Integer.parseInt(sztyoutuIdCardResponse.getId_begindate()));
        target.setId_enddate(Integer.parseInt(sztyoutuIdCardResponse.getId_enddate()));

        return target;
    }
}
