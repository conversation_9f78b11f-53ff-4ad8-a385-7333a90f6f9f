package com.cairh.cpe.esb.component.ocr.converter.greencard.zhengtong;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.standard.greendcard.ZhengTongGreencardResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.factory.represent.greencard.GreencardZhengTongRepresentWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class GreencardZhengTongRepresentToDubboResponseConverter implements Converter<GreencardZhengTongRepresentWrapper, OcrParseIdCardResponse> {
    @Override
    public OcrParseIdCardResponse convert(GreencardZhengTongRepresentWrapper source, OcrParseIdCardResponse target) {
        ZhengTongGreencardResponse data = source.getObject().getDetails();
        log.info("证通永居证ocr返回：{}",data);
        OcrParseIdCardResponse response = new OcrParseIdCardResponse();
        if(Objects.nonNull(data)){
            response.setClient_name(data.getChinese_name());
            response.setId_no(data.getId_number());
            response.setNationality(data.getNationality());
            response.setIssued_depart(data.getIssued_authority());
            if(data.getDate_of_birth() != null){
                response.setBirthday(Integer.valueOf(data.getDate_of_birth()));
            }
            response.setClient_gender(data.getSex());
            if(data.getId_begindate() != null && data.getId_enddate() != null) {
                response.setId_enddate(Integer.valueOf(data.getId_enddate()));
                response.setId_begindate(Integer.valueOf(data.getId_begindate()));
            }
            response.setEnglish_name(data.getName());

        }
        return response;
    }
}
