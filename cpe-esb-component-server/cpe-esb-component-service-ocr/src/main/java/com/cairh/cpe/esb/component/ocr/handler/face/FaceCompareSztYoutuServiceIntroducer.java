package com.cairh.cpe.esb.component.ocr.handler.face;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.face.FaceCompareSztYoutuRepresentWrapper;
import com.cairh.cpe.http.data.component.sztyoutu.ComponentSztyoutuHttpService;
import com.cairh.cpe.http.data.component.sztyoutu.req.SztyoutuFaceCompareRequest;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

@NoArgsConstructor
@AllArgsConstructor
public abstract class FaceCompareSztYoutuServiceIntroducer {

    @Autowired
    private ComponentSztyoutuHttpService httpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;


    public RepresentWrapper<?> ocrFaceCompare(SztyoutuFaceCompareRequest request) {
        String extResponse = httpService.faceCompare(request);
        return standarizedDelegatingProxy.transform(extResponse, FaceCompareSztYoutuRepresentWrapper.class);
    }
}
