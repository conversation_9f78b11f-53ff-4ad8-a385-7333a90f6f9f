package com.cairh.cpe.esb.component.ocr.core.concrete.bizLicense;

import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.file.utils.MapUtil;
import com.cairh.cpe.esb.component.ocr.core.OcrBusinessLicenseService;
import com.cairh.cpe.esb.component.ocr.core.OcrDateFormater;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseBusinessLisenceRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseBusinessLisenceResponse;
import com.cairh.cpe.http.data.component.yidao.ComponentYiDaoHttpService;
import com.cairh.cpe.http.data.component.yidao.req.YidaoBizLicenseRequest;
import com.cairh.cpe.util.json.FastJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class YidaoBusinessLicenseService implements OcrBusinessLicenseService {

    @Autowired
    private ComponentYiDaoHttpService componentYiDaoHttpService;

    @Override
    public OcrParseBusinessLisenceResponse recognition(OcrParseBusinessLisenceRequest req) {
        YidaoBizLicenseRequest request = new YidaoBizLicenseRequest();
        request.setImage_base64(req.getBase64_image());
        String resp = componentYiDaoHttpService.ocrBizLicense(request);
        log.info("易道博识营业执照服务商返回结果: {}", resp);
        Map<String, String> resMap = null;
        try {
            resMap = FastJsonUtil.parseObject(resp, Map.class);
        }catch (Exception e){
            throw new BizException(ErrorConstant.BUSINESS_LICENSE_OCR_PARSE_ERROR,"易道博识营业执照解析失败");
        }
        String error_code = MapUtil.getString(resMap, "error_code", "");
        String description = MapUtil.getString(resMap, "description", "");
        if(!error_code.equals("0")){
            log.error("易道博识营业执照识别失败error_code: {}, description: {}", error_code, description);
            throw new BizException(ErrorConstant.BUSINESS_LICENSE_OCR_ERROR,"易道博识营业执照解析失败");
        }
        OcrParseBusinessLisenceResponse response = new OcrParseBusinessLisenceResponse();
        String ocrResult = MapUtil.getString(resMap, "result", "");
        Map<String, Object> map = FastJsonUtil.parseObject(ocrResult, Map.class);
        //统一社会信用代码
        response.setUnified_social_credit_code(parse(map, "no"));
        //名称
        response.setCompany_name(parse(map, "name"));
        //类型
        response.setCompany_type(parse(map, "type"));
        //法定代表人
        response.setCorporate_name(parse(map, "representitive"));
        //注册资本
        response.setRegister_capital(parse(map, "capital"));
        //成立日期
        response.setBuild_date(OcrDateFormater.format(parse(map, "found_date")).toString());
        //住所
        response.setAddress(parse(map, "address"));
        //营业期限 三种情况：1.长期 2.开始日期+长期 3.开始日期+结束日期
        String expire_date = parse(map, "expire_date");
        if (StringUtils.isBlank(expire_date)) {
            return response;
        }
        if (StringUtils.equals(expire_date, "长期")) {
            response.setOperate_enddate(expire_date);
        }
        if (StringUtils.contains(expire_date, "至")) {
            String[] split = expire_date.split("至");
            response.setOperate_begindate(OcrDateFormater.format(split[0]).toString());
            if( split[1].equals("长期")){
                response.setOperate_enddate(split[1]);
            }else {
                response.setOperate_enddate(OcrDateFormater.format(split[1]).toString());
            }
        }
        //经营范围
        response.setOperate_scope(parse(map, "business_scope"));
        //登记机关
        response.setRegister_depart(parse(map, "reg_authority"));
        //核准日期
        response.setApproval_date(OcrDateFormater.format(parse(map, "reg_date")).toString());
        //经营状态
        response.setBusiness_status(parse(map, ""));
        //注册号
        response.setRegister_no(parse(map, ""));

        return response;
    }

    // 解析字段
    private String parse(Map<String, Object> map, String key) {
        JSONObject ocrResult = (JSONObject) map.get(key);
        if (ocrResult == null) {
            return "";
        }
        return ocrResult.getString("words");
    }

}
