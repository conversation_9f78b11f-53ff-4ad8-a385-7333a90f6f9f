package com.cairh.cpe.esb.component.ocr.factory.support;

import com.cairh.cpe.esb.component.ocr.core.OcrService;
import com.cairh.cpe.esb.component.ocr.factory.OcrGATPassServiceFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import java.util.function.Supplier;

/**
 * default implementation of {@link OcrGATPassServiceFactory} which use {@link BeanFactory} for
 * detecting service
 *
 * <AUTHOR>
 */
@Service
@Primary
public class BeanFactoryOcrGATPassServiceFactory extends WebApplicationObjectSupport implements OcrGATPassServiceFactory {

    @Override
    public OcrService getOcrGATPassService(Supplier<String> serviceNameSupplier) {
        return getWebApplicationContext().getBean(serviceNameSupplier.get(), OcrService.class);
    }
}
