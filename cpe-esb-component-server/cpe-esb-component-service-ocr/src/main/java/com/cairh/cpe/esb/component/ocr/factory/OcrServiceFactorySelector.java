package com.cairh.cpe.esb.component.ocr.factory;

import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.ocr.core.OcrService;
import com.cairh.cpe.esb.component.ocr.dto.req.PicTypeEnum;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * determine which type of {@link OcrServiceFactory} should take effect through {@link PicTypeEnum pic type}
 *
 * <AUTHOR>
 */
@Service
public class OcrServiceFactorySelector {

    @Autowired
    private OcrIdCardServiceFactory idCardServiceFactory;

    @Autowired
    private OcrGATPassServiceFactory gatPassServiceFactory;

    @Autowired
    private OcrGATResidenceServiceFactory gatResidenceServiceFactory;

    @Autowired
    private OcrBankCardServiceFactory bankCardServiceFactory;

    @Autowired
    private OcrBusinessLicenseServiceFactory businessLicenseServiceFactory;

    @Autowired
    private OcrNormalServiceFactory normalServiceFactory;

    @Autowired
    private OcrForeignerGreencardServiceFactory foreignerGreencardServiceFactory;

    @Autowired
    private CompositePropertySources compositePropertySources;


    /**
     * select {@link OcrService} by pic type
     *
     * @param picType {@link PicTypeEnum}
     * @return {@link OcrService}
     */
    public OcrService select(@NonNull PicTypeEnum picType) {
        OcrService ocrService;
        if (Objects.isNull(picType)) {
            throw new BizException("picType invalid");
        }
        String service_name;
        switch (picType) {
            case IDCARDCHECK:
                service_name = compositePropertySources.getProperty(ConfigConst.OCR_IDCARD_CHECK_SERVICE_PROVIDER, "yiDaoOcrIdCardService");
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置身份证质检服务提供商");
                }
                ocrService = idCardServiceFactory.getOcrService(() -> service_name);
                break;
            case SFZ:
                service_name = compositePropertySources.getProperty(ConfigConst.OCR_IDCARD_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置身份证识别服务提供商");
                }
                ocrService = idCardServiceFactory.getOcrService(() -> service_name);
                break;
            case GATTXZ:
                service_name = compositePropertySources.getProperty(ConfigConst.OCR_PERMIT_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置港澳台通行证识别服务提供商");
                }
                ocrService = gatPassServiceFactory.getOcrService(() -> service_name);
                break;
            case GATJZZ:
                service_name = compositePropertySources.getProperty(ConfigConst.OCR_LIVE_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置港澳台居住证识别服务提供商");
                }
                ocrService = gatResidenceServiceFactory.getOcrService(() -> service_name);
                break;
            case BANKCARD:
                service_name = compositePropertySources.getProperty(ConfigConst.OCR_OCRBANKCARD_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置银行卡识别服务提供商");
                }
                ocrService = bankCardServiceFactory.getOcrService(() -> service_name);
                break;
            case BUSINESSLICENSE:
                service_name = compositePropertySources.getProperty(ConfigConst.COMP_OCR_BUSINESSLISENCE_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置营业执照识别服务提供商");
                }
                ocrService = businessLicenseServiceFactory.getOcrService(() -> service_name);
                break;
            case PERMANENTRESITENT:
                service_name = compositePropertySources.getProperty(ConfigConst.COMP_OCR_PERMANENT_RESITENT_CARD_SERVICE_PROVICER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置永居证识别服务提供商");
                }
                ocrService = foreignerGreencardServiceFactory.getOcrService(() -> service_name);
                break;
            case NORMAL:
                service_name = compositePropertySources.getProperty(ConfigConst.COMP_OCR_NORMAL_SERVICE_PROVIDER);
                if (StringUtils.isBlank(service_name)) {
                    throw new BizException("请配置通用识别服务提供商");
                }
                ocrService = normalServiceFactory.getOcrService(() -> service_name);
                break;
            default:
                throw new BizException("picType invalid");
        }

        return ocrService;
    }
}
