package com.cairh.cpe.esb.component.ocr.converter.greencard.youtu;

import com.cairh.cpe.component.common.utils.StringUtils;
import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.OcrDateFormater;
import com.cairh.cpe.esb.component.ocr.core.standard.greendcard.YouTuGreencardResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.factory.represent.greencard.GreencardYouTuRepresentWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 优图永居证返回结果转换
 */
@Component
@Slf4j
public class GreencardYouTuRepresentObjectFactoryToOcrParseIdCardResponseConverter implements Converter<GreencardYouTuRepresentWrapper, OcrParseIdCardResponse> {
    @Override
    public OcrParseIdCardResponse convert(GreencardYouTuRepresentWrapper source, OcrParseIdCardResponse target) {
        YouTuGreencardResponse data = source.getObject();
        OcrParseIdCardResponse response = new OcrParseIdCardResponse();
        if(Objects.nonNull(data)){
            for(YouTuGreencardResponse.Item i : data.getItems()){
                String item = i.getItem();
                String itemString = i.getItemstring();
                if(item.equals("姓名/Name") || item.contains("姓名")){
                    response.setClient_name(itemString);
                }
                if(item.equals("性别/Sex") || item.contains("性别")){
                    response.setClient_gender(itemString);
                }
                if(item.equals("证件号码/idno") || item.contains("证件号码")){
                    response.setId_no(itemString);
                }
                if(item.equals("国籍/Nationality") || item.contains("国籍")){
                    response.setNationality(itemString);
                }
                if(item.equals("出生日期/Date of Birth") || item.contains("出生日期")){
                    if(StringUtils.isNotBlank(itemString)){
                        response.setBirthday(OcrDateFormater.format(itemString));
                    }
                }
                if(item.equals("有效期限/Period of Validity") || item.contains("有效期限")){
                    String id_begindate = "";
                    String id_enddate = "";
                    if (StringUtils.isNotBlank(itemString)) {
                        String arg[] = itemString.split("至");
                        if (arg != null && arg.length >= 2) {
                            id_begindate = arg[0].replaceAll("年", "").replaceAll("月", "").replaceAll("日", "").replaceAll("自", "");
                            id_enddate = arg[1].replaceAll("年", "").replaceAll("月", "").replaceAll("日", "");
                            response.setId_begindate(Integer.valueOf(id_begindate));
                            response.setId_enddate(Integer.valueOf(id_enddate));
                        }
                    }
                }
                if(item.contains("签发机关") || item.contains("机构")){
                    response.setIssued_depart(itemString);
                }
            }
        }
        log.info("优图永居证识别结果封装完成");
        return response;
    }
}
