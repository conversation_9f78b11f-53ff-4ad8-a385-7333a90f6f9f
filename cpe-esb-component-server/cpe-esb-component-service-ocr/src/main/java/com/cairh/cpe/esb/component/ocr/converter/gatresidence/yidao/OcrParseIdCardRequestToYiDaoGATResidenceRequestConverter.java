package com.cairh.cpe.esb.component.ocr.converter.gatresidence.yidao;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.core.util.Base64PicUtil;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrParseIdCardRequest;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoGATResidenceRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * {@link OcrParseIdCardRequest} to {@link YiDaoGATResidenceRequest} converter
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OcrParseIdCardRequestToYiDaoGATResidenceRequestConverter implements Converter<OcrParseIdCardRequest, YiDaoGATResidenceRequest> {

    @Override
    public YiDaoGATResidenceRequest convert(OcrParseIdCardRequest source, YiDaoGATResidenceRequest target) {
        String base64_image = source.getBase64_image();
        base64_image = Base64PicUtil.delBase64Head(base64_image);
        target.setImage_base64(base64_image);
        return target;
    }
}
