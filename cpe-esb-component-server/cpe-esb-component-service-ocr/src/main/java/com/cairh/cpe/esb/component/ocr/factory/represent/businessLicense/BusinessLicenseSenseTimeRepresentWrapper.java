package com.cairh.cpe.esb.component.ocr.factory.represent.businessLicense;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.ocr.core.standard.businessLicense.SenseTimeBusinessLicenseResponse;

/**
 * <AUTHOR>
 */
public class BusinessLicenseSenseTimeRepresentWrapper extends AbstractRepresentWrapper<SenseTimeBusinessLicenseResponse> {

    public BusinessLicenseSenseTimeRepresentWrapper(SenseTimeBusinessLicenseResponse source) {
        super(source);
    }
}
