package com.cairh.cpe.esb.component.ocr.handler.bankcard;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.bankcard.BankCardYiDaoRepresentWrapper;
import com.cairh.cpe.http.data.component.yidao.ComponentYiDaoHttpService;
import com.cairh.cpe.http.data.component.yidao.req.YiDaoBankCardRequest;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * an association with cpe-counter ocr bankCardRecognition yidao api, perform as a bridge for connecting it to native
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
public abstract class OcrBankCardYiDaoServiceDecorator {

    @Autowired
    private ComponentYiDaoHttpService componentYiDaoHttpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;


    public RepresentWrapper<?> ocrBankCard(@NonNull YiDaoBankCardRequest request) {
        String extResponse = componentYiDaoHttpService.bankCardRecognition(request);
        return standarizedDelegatingProxy.transform(extResponse, BankCardYiDaoRepresentWrapper.class);
    }
}
