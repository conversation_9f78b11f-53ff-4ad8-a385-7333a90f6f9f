package com.cairh.cpe.esb.component.ocr.converter.idcard.ebsc;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.ocr.core.OcrDateFormater;
import com.cairh.cpe.esb.component.ocr.core.standard.idard.EbscIdCardResponse;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrParseIdCardResponse;
import com.cairh.cpe.esb.component.ocr.factory.represent.idcard.IdCardEbscRepresentWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Slf4j
@Component
public class IdCardEbscRepresentObjectFactoryToOcrImageQualityResponseConverter implements Converter<IdCardEbscRepresentWrapper, OcrParseIdCardResponse> {

    @Override
    public OcrParseIdCardResponse convert(IdCardEbscRepresentWrapper source, OcrParseIdCardResponse target) {
        log.info("光大证券身份证识别response为：{}", source.getObject());
        EbscIdCardResponse data = source.getObject().getData();
        OcrParseIdCardResponse response = new OcrParseIdCardResponse();
        if (Objects.nonNull(data)) {
            response.setNation_id(data.getPeople());
            response.setClient_gender(data.getSex());
            response.setClient_name(data.getName());
            response.setId_no(data.getId_number());
            response.setId_address(data.getAddress());
            response.setIssued_depart(data.getIssue_authority());
            try {
                response.setId_begindate(data.getId_begindate());
            } catch (Exception e) {

            }
            try {
                response.setId_enddate(data.getId_enddate());
            } catch (Exception e) {

            }
            try {
                response.setBirthday(OcrDateFormater.format(data.getBirthday()));
            } catch (Exception e) {

            }
        }
        return response;
    }
}
