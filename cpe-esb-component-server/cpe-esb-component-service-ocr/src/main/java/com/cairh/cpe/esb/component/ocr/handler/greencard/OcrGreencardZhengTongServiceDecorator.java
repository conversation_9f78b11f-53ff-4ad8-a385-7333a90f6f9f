package com.cairh.cpe.esb.component.ocr.handler.greencard;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.ocr.factory.represent.greencard.GreencardZhengTongRepresentWrapper;
import com.cairh.cpe.http.data.component.ect888.ComponentEct888HttpService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

@NoArgsConstructor
@AllArgsConstructor
public abstract class OcrGreencardZhengTongServiceDecorator {
    @Autowired
    private ComponentEct888HttpService httpService;

    @Autowired
    private StandarizedDelegatingProxy standarizedDelegatingProxy;

    public RepresentWrapper<?> permanResidentCardReco(byte[] apiRequest) {
        String apiResponse = httpService.ocrGreenCard(apiRequest);
        return standarizedDelegatingProxy.transform(apiResponse, GreencardZhengTongRepresentWrapper.class);
    }
}
