package com.cairh.cpe.esb.component.ocr.core.standard.face;

import com.cairh.cpe.esb.component.core.config.ConditionalOnApiResponse;
import lombok.Data;

@Data
@ConditionalOnApiResponse(condition = "#error_code == '0' @throw #error_code + ':' + #error_msg")
public class SztYoutuFaceCompareRepresent {

    private String error_code;

    private String error_msg;

    private Float similarity_float;
}