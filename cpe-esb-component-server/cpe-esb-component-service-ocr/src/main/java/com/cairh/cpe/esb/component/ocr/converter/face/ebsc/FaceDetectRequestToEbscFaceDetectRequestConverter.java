package com.cairh.cpe.esb.component.ocr.converter.face.ebsc;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.core.advise.support.AutoCloseDetectableResourceCreator;
import com.cairh.cpe.esb.component.file.utils.ImageBase64Util;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceDetectRequest;
import com.cairh.cpe.http.data.component.ebsc.req.EbscFaceDetectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FaceDetectRequestToEbscFaceDetectRequestConverter implements Converter<FaceDetectRequest, EbscFaceDetectRequest> {

    @Override
    public EbscFaceDetectRequest convert(FaceDetectRequest source, EbscFaceDetectRequest target) {
        String image_data = source.getBase64_image();
        if (image_data.contains("base64,")) {
            image_data = image_data.substring(image_data.indexOf("base64,") + 7);
        }
        byte[] binary = ImageBase64Util.getStrToBytes(image_data);
        target.setImg(AutoCloseDetectableResourceCreator.createTempResource(binary));
        return target;
    }
}
