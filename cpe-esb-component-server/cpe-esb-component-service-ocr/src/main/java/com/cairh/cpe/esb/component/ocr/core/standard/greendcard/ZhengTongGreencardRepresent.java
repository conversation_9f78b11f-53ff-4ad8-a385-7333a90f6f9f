package com.cairh.cpe.esb.component.ocr.core.standard.greendcard;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ZhengTongGreencardRepresent {
    private long log_id;
    private String error_msg;
    private String error_code;
    private String rotated_image_width;
    private String image_angle;
    private String rotated_image_height;
    private String type;
    private String category;
    private ZhengTongGreencardResponse details;

}
