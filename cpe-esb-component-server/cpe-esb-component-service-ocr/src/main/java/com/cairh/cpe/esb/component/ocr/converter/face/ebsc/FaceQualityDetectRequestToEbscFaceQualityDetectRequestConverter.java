package com.cairh.cpe.esb.component.ocr.converter.face.ebsc;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.core.advise.support.AutoCloseDetectableResourceCreator;
import com.cairh.cpe.esb.component.ocr.dto.req.FaceQualityDetectRequest;
import com.cairh.cpe.http.data.component.ebsc.req.EbscFaceQualityDetectRequest;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

/**
 * {@link FaceQualityDetectRequest} -> {@link EbscFaceQualityDetectRequest}
 *
 * <AUTHOR>
 */
@Component
public class FaceQualityDetectRequestToEbscFaceQualityDetectRequestConverter implements Converter<FaceQualityDetectRequest, EbscFaceQualityDetectRequest> {

    @Override
    public EbscFaceQualityDetectRequest convert(FaceQualityDetectRequest source, EbscFaceQualityDetectRequest target) {
        String image_data = source.getBase64_image();

        target.setImg(AutoCloseDetectableResourceCreator.createTempResource(Base64.decodeBase64(image_data)));
        return target;
    }
}
