package com.cairh.cpe.esb.component.ocr.zhengtong;

import com.cairh.cpe.esb.component.ocr.ApplicationTest;
import com.cairh.cpe.counter.core.annotation.EnableAPIServiceLazyLoad;
import com.cairh.cpe.esb.component.ocr.core.concrete.gatresidence.ZhengTongOcrGATResidenceService;
import com.cairh.cpe.esb.component.ocr.core.concrete.idcard.ZhengTongOcrIdCardService;
import com.cairh.cpe.esb.component.ocr.dto.req.OcrImageQualityRequest;
import com.cairh.cpe.esb.component.ocr.dto.resp.OcrImageQualityResponse;
import com.cairh.cpe.http.data.component.ect888.ComponentEct888HttpService;
import com.cairh.cpe.http.data.component.ect888.req.Ect888AsrQueryParam;
import com.cairh.cpe.http.data.component.ect888.req.Ect888TtsRequest;
import com.cairh.cpe.http.data.component.ect888.req.ZhengTongGATResidenceRequest;
import com.cairh.cpe.http.data.component.ect888.req.ZhengTongIdCardRequest;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.aspectj.weaver.ast.Var;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.FileSystemResource;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * zt test
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = ApplicationTest.class)
@EnableAPIServiceLazyLoad(basePackages = {"com.cairh.cpe.**"})
class ZtHttpServiceTest {

    @Autowired
    private ComponentEct888HttpService httpService;
    @Autowired
    private ZhengTongOcrIdCardService zhengTongOcrIdCardService;
    @Autowired
    private ZhengTongOcrGATResidenceService zhengTongOcrGATResidenceService;


    @Test
    public void idCardTest() throws Exception {
//        ZhengTongIdCardRequest zhengtongRequest = new ZhengTongIdCardRequest();
//        zhengtongRequest.setDetect_card("true");
//        zhengtongRequest.setDetect_direction("true");
//        zhengtongRequest.setDetect_photo("true");
//        zhengtongRequest.setDetect_quality("true");
//        zhengtongRequest.setDetect_risk("true");
//        zhengtongRequest.setId_card_side("front");
        String base64_image = fileToBase64("D:\\WxChat\\WXWork\\1688858052525514\\Cache\\File\\2023-12\\港澳台\\光斑11.jpg");
        base64_image = delBase64Head(base64_image);
//        zhengtongRequest.setImage(base64_image);
//        String result = httpService.ocrIdCard(zhengtongRequest);
//        JSON.parseObject(result,ZhengTongIdCardResponse)
        OcrImageQualityRequest request = new OcrImageQualityRequest();
        request.setImage_data(base64_image);
        OcrImageQualityResponse ocrImageQualityResponse = zhengTongOcrIdCardService.idCardQualityInspect(request);
        System.out.println("身份证验证：" + ocrImageQualityResponse);
    }

    /**
     * 港澳台居住证 测试
     * @throws Exception
     */
    @Test
    public void ocrGATResidenceTest() throws Exception {
        ZhengTongGATResidenceRequest request = new ZhengTongGATResidenceRequest();
        String base64_image = fileToBase64("D:\\WxChat\\WXWork\\1688858052525514\\Cache\\File\\2023-12\\港澳台\\港澳居民居住证正面.jpg");
        base64_image = delBase64Head(base64_image);
        request.setImage_base64(base64_image);
//        String result = httpService.ocrGATResidence(request);
//        System.out.println(result);
    }

    public static String delBase64Head(String base64_image) {
        if (base64_image.contains("base64,")) {
            base64_image = base64_image.substring(base64_image.indexOf("base64,") + 7);
        }
        return base64_image;
    }

    public static String fileToBase64(String filePath) {
        try {
            Path path = Paths.get(filePath);
            byte[] fileBytes = Files.readAllBytes(path);
            byte[] encodedBytes = Base64.getEncoder().encode(fileBytes);
            return new String(encodedBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}