import com.cairh.cpe.esb.component.EsbComponentServerApplication;
import com.cairh.cpe.esb.component.voice.IEsbComponentAsyncVoiceDubboService;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionResultRequest;
import com.cairh.cpe.esb.component.voice.dto.req.SpeechRecognitionSponsorRequest;
import com.cairh.cpe.esb.component.voice.dto.resp.SpeechRecognitionResultResponse;
import com.cairh.cpe.esb.component.voice.dto.resp.SpeechRecognitionSponsorResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-13
 */
@SpringBootTest(classes = EsbComponentServerApplication.class)
public class VoiceTest {

    @Autowired
    private IEsbComponentAsyncVoiceDubboService asyncVoiceDubboService;

    @Test
    public void testSpeechRecognitionSponsor() throws Exception{
        SpeechRecognitionSponsorRequest request = new SpeechRecognitionSponsorRequest();
        request.setFilerecord_id("111");
        SpeechRecognitionSponsorResponse response = asyncVoiceDubboService.speechRecognitionSponsor(request);
        System.out.println(response);
    }

    @Test
    public void testSpeechRecognitionResult() throws Exception{
        SpeechRecognitionResultRequest request = new SpeechRecognitionResultRequest();
        request.setTask_id("0576b61b-ccec-4e01-9b4a-fecef029e87c");
        List<SpeechRecognitionResultResponse> responses = asyncVoiceDubboService.speechRecognitionResult(request);
        System.out.println(responses);
    }

}