import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.context.util.JSONUtil;
import com.cairh.cpe.esb.component.EsbComponentServerApplication;
import com.cairh.cpe.esb.component.archive.service.third.IUploadToThirdService;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectThirdDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.*;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadThirdFileResponse;
import com.cairh.cpe.util.json.FastJsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2022-09-26
 */
@SpringBootTest(classes = EsbComponentServerApplication.class)
public class ArchTest {

//    @Autowired
//    private IUploadToThirdService uploadToThirdService;

    @Autowired
    private IEsbComponentElectDubboService esbComponentElectDubboService;

    @Autowired
    private IEsbComponentElectThirdDubboService esbComponentElectThirdDubboService;

//    @Test
//    public void test_uploadToThird() {
//        ElecUploadToThirdReq elecUploadToThirdReq = new ElecUploadToThirdReq();
//        elecUploadToThirdReq.setClient_id("3062646");
//        elecUploadToThirdReq.setSub_op_type("1110");
//        ArrayList<ThirdFileInfo> thirdFileInfos = new ArrayList<>();
//        ThirdFileInfo thirdFileInfo = new ThirdFileInfo();
////        thirdFileInfo.setFilerecord_ids("20220714487250820037");
//        thirdFileInfo.setFilerecord_ids("20220714487250820037,********");
//        thirdFileInfo.setFile_no("6A");
//        thirdFileInfos.add(thirdFileInfo);
//        ThirdFileInfo thirdFileInfo2 = new ThirdFileInfo();
////        thirdFileInfo2.setFilerecord_ids("20220714487250820037");
//        thirdFileInfo2.setFilerecord_ids("20220714487250820037,********");
//        thirdFileInfo2.setFile_no("6B");
//        thirdFileInfos.add(thirdFileInfo2);
//        ThirdFileInfo thirdFileInfo3 = new ThirdFileInfo();
////        thirdFileInfo2.setFilerecord_ids("20220714487250820037");
//        thirdFileInfo3.setFilerecord_ids("20220714487250820037,********");
//        thirdFileInfo3.setFile_no("Ai");
//        thirdFileInfos.add(thirdFileInfo3);
//        elecUploadToThirdReq.setThird_file_info(thirdFileInfos);
//        elecUploadToThirdReq.setFund_account("3062646");
//        elecUploadToThirdReq.setSub_file_task_type("1100");
//        elecUploadToThirdReq.setFile_task_type("11");
//        elecUploadToThirdReq.setMobile_tel("***********");
//        System.out.println("调用结果：" + uploadToThirdService.uploadToThird(elecUploadToThirdReq));
//    }
//
//    @Test
//    public void test_uploadToThirdByMobile() {
////        ElecUploadToThirdByMobileReq elecUploadToThirdReq = new ElecUploadToThirdByMobileReq();
////        elecUploadToThirdReq.setClient_id("3062646");
////        ArrayList<ThirdFileInfo> thirdFileInfos = new ArrayList<>();
////        ThirdFileInfo thirdFileInfo = new ThirdFileInfo();
////        thirdFileInfo.setFilerecord_ids("20220714487250820037,********");
////        thirdFileInfo.setFile_no("6A");
////        thirdFileInfos.add(thirdFileInfo);
////        ThirdFileInfo thirdFileInfo2 = new ThirdFileInfo();
////        thirdFileInfo2.setFilerecord_ids("20220714487250820037,********");
////        thirdFileInfo2.setFile_no("6B");
////        thirdFileInfos.add(thirdFileInfo2);
////        elecUploadToThirdReq.setThird_file_info(thirdFileInfos);
////        elecUploadToThirdReq.setFund_account("3062646");
////        elecUploadToThirdReq.setMobile_tel("***********");
////        ElecUploadToThirdByMobileReq elecUploadToThirdReq = JSONObject.parseObject("{\"client_id\":\"3062654\",\"fund_account\":\"3062654\",\"third_file_info\":[{\"filerecord_ids\":\"20220927605450060700\",\"file_no\":\"6A\"},{\"filerecord_ids\":\"20220927605450060699\",\"file_no\":\"6B\"},{\"filerecord_ids\":\"20220927606880060711\",\"file_no\":\"80\"},{\"filerecord_ids\":\"20220927607550060715\",\"file_no\":\"82\"}],\"mobile_tel\":\"***********\"}", ElecUploadToThirdByMobileReq.class);
//        ElecUploadToThirdByMobileReq elecUploadToThirdReq = JSONObject.parseObject("{\"client_id\":\"3062654\",\"fund_account\":\"3062654\",\"third_file_info\":[{\"filerecord_ids\":\"20220927607550060715\",\"file_no\":\"82\"}],\"mobile_tel\":\"***********\"}", ElecUploadToThirdByMobileReq.class);
//        uploadToThirdService.uploadToThirdByMobile(elecUploadToThirdReq);
//    }
//
//    @Test
//    public  void ttt() {
//        LocalDate localDate = LocalDate.now().plusDays(720);
//        System.out.println("时间为:"+localDate);
//    }

//    @Test
//    public  void test_electDownloadThirdFile() {
//        ElectDownloadThirdFileRequest request = new ElectDownloadThirdFileRequest();
//        request.setFile_id("*********");
//        ElectDownloadThirdFileResponse reponse = esbComponentElectDubboService.electDownloadThirdFile(request);
//    }

    @Test
    public void test_t2_uploadToThird() {
        ElecUploadToThirdReq elecUploadToThirdReq = new ElecUploadToThirdReq();
        //BOP
        elecUploadToThirdReq.setAcpt_id("****************");
        //T2
        elecUploadToThirdReq.setClient_id("3058277");
        elecUploadToThirdReq.setSub_op_type("1110");
        ArrayList<ThirdFileInfo> thirdFileInfos = new ArrayList<>();
        ThirdFileInfo thirdFileInfo = new ThirdFileInfo();
        thirdFileInfo.setFilerecord_ids("10121");
        thirdFileInfo.setFile_no("av");
        thirdFileInfos.add(thirdFileInfo);
        ThirdFileInfo thirdFileInfo2 = new ThirdFileInfo();
        thirdFileInfo2.setFilerecord_ids("10122");
        thirdFileInfo2.setFile_no("ja");
        thirdFileInfos.add(thirdFileInfo2);
        ThirdFileInfo thirdFileInfo3 = new ThirdFileInfo();
        thirdFileInfo3.setFilerecord_ids("10123");
        thirdFileInfo3.setFile_no("n3");
        thirdFileInfos.add(thirdFileInfo3);
        ThirdFileInfo thirdFileInfo4 = new ThirdFileInfo();
        thirdFileInfo4.setFilerecord_ids("10124");
        thirdFileInfo4.setFile_no("n4");
        thirdFileInfos.add(thirdFileInfo4);
        elecUploadToThirdReq.setThird_file_info(thirdFileInfos);
        elecUploadToThirdReq.setFund_account("3058277");
        elecUploadToThirdReq.setFile_task_type("10");
        elecUploadToThirdReq.setSub_file_task_type("1001");
        elecUploadToThirdReq.setSub_op_type("10027");
        elecUploadToThirdReq.setMobile_tel("***********");
        elecUploadToThirdReq.setStore_branch_no("8888");
        elecUploadToThirdReq.setOrgan_flag("0");
        elecUploadToThirdReq.setBranch_no("100");
        elecUploadToThirdReq.setId_no("511423200412230034");
        System.out.println("T2调用结果：" + esbComponentElectThirdDubboService.uploadToThird(elecUploadToThirdReq));
    }
//
//    @Test
//    public  void test_t2_electArchiveThirdFile() {
//        ElectArchiveThirdFileRequest request = new ElectArchiveThirdFileRequest();
//        //T2归档入参
//        request.setScantask_id("****************");
//        esbComponentElectThirdDubboService.electArchiveThirdFile(request);
//    }

//    @Test
//    public void test_shine_uploadToThird() {
//        ElecUploadToThirdReq elecUploadToThirdReq = new ElecUploadToThirdReq();
//        elecUploadToThirdReq.setClient_id("3058277");
//        elecUploadToThirdReq.setSub_op_type("1110");
//        ArrayList<ThirdFileInfo> thirdFileInfos = new ArrayList<>();
//        ThirdFileInfo thirdFileInfo = new ThirdFileInfo();
//        thirdFileInfo.setFilerecord_ids("10121");
//        thirdFileInfo.setFile_no("6A");
//        thirdFileInfos.add(thirdFileInfo);
//        elecUploadToThirdReq.setThird_file_info(thirdFileInfos);
//        elecUploadToThirdReq.setFund_account("3058277");
//        elecUploadToThirdReq.setSub_file_task_type("1100");
//        elecUploadToThirdReq.setFile_task_type("11");
//        elecUploadToThirdReq.setMobile_tel("***********");
//        elecUploadToThirdReq.setSub_op_type("1101");
//        elecUploadToThirdReq.setBusi_serial_no("100020");
//        elecUploadToThirdReq.setBusi_code("10058");
//        elecUploadToThirdReq.setId_kind("0");
//        elecUploadToThirdReq.setId_no("511423200412230034");
//        elecUploadToThirdReq.setUser_id("ct1111");
//        elecUploadToThirdReq.setNo_submit_flag("0");
//        FastJsonUtil.toJSONString(elecUploadToThirdReq);
//        System.out.println("新意调用结果：" + esbComponentElectThirdDubboService.uploadToThird(elecUploadToThirdReq));
//    }

//    @Test
//    public  void test_shine_electArchiveThirdFile() {
//        ElectArchiveThirdFileRequest request = new ElectArchiveThirdFileRequest();
//        //新意归档入参
//        request.setSource_nos("61,105130178");
//        request.setBusi_serial_no("100020");
//        request.setOrgan_flag("0");
//        request.setBusi_code("100058");
//        request.setId_kind("3");
//        request.setId_no("511423199206240017");
//        request.setClient_id("3062646");
//        request.setUser_name("测试");
//        request.setBranch_no("100");
//        request.setOpen_type("1");
//        request.setOp_user_code("ct1111");
//        request.setC_busi_code("12346");
//        FastJsonUtil.toJSONString(request);
//        esbComponentElectThirdDubboService.electArchiveThirdFile(request);
//    }
}