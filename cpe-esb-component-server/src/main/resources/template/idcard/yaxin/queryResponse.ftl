<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <soapenv:Body>
      <ns1:getIDCardInfoResponse soapenv:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:ns1="http://inter.ailk.com">
         <getIDCardInfoReturn xsi:type="soapenc:string" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
		 <![CDATA[
			<?xml version="1.0" encoding="UTF-8"?>
			<MSGPKG>
				<HEAD><MSGTYPE>01</MSGTYPE><RECORDTYPE>101</RECORDTYPE><VERSION>01</VERSION></HEAD>
				<BODY>
					<TimeStamp>20161228135750</TimeStamp>
					<TransactionID></TransactionID>
					<ResultCode>${ResultCode}</ResultCode>
					<ResultMsg>${ResultMsg}</ResultMsg>
				</BODY>
			</MSGPKG>
		 ]]>
		</getIDCardInfoReturn>
      </ns1:getIDCardInfoResponse>
   </soapenv:Body>
</soapenv:Envelope>