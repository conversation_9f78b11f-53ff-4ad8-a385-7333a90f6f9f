package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.JinHuiCreateSealRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class JinHuiCreateSealRepresentWrapper extends AbstractRepresentWrapper<JinHuiCreateSealRepresent> {

    public JinHuiCreateSealRepresentWrapper(JinHuiCreateSealRepresent source) {
        super(source);
    }
}
