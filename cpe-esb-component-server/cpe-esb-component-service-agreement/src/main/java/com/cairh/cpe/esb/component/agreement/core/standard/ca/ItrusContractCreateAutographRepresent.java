package com.cairh.cpe.esb.component.agreement.core.standard.ca;

import com.cairh.cpe.esb.component.core.config.ConditionalOnApiResponse;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ConditionalOnApiResponse(condition = "#code == 0 @throw #code + ':' + #  message")
public class ItrusContractCreateAutographRepresent {

    private Integer code;

    private String message;

    private RESULT data;

    @Data
    public static class RESULT {
        /**
         * autographId
         */
        private String autographId;
    }
}
