package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.JinHuiPersonOpenAccountRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class JinHuiPersonOpenAccountRepresentWrapper extends AbstractRepresentWrapper<JinHuiPersonOpenAccountRepresent> {

    public JinHuiPersonOpenAccountRepresentWrapper(JinHuiPersonOpenAccountRepresent source) {
        super(source);
    }
}
