package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractCreateContractRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class ItrusContractCreateContractRepresentWrapper extends AbstractRepresentWrapper<ItrusContractCreateContractRepresent> {

    public ItrusContractCreateContractRepresentWrapper(ItrusContractCreateContractRepresent source) {
        super(source);
    }
}
