package com.cairh.cpe.esb.component.agreement.core.concrete.ca;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.agreement.core.CaService;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractAddSignerRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractCreateAutographRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractCreateContractRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractCreateUserRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractDownloadContractRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractSignRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractStampListRepresent;
import com.cairh.cpe.esb.component.agreement.dto.req.CaRequest;
import com.cairh.cpe.esb.component.agreement.dto.resp.CaResponse;
import com.cairh.cpe.esb.component.agreement.handler.ca.ItrusContractCaServiceDecorator;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractAddSignerRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractCreateAutographRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractCreateContractRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractCreateUserRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractDownloadContractRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractSignRequest;
import com.cairh.cpe.http.data.component.itrus.contract.req.ItrusContractStampListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class ItrusContractCaService extends ItrusContractCaServiceDecorator implements CaService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Override
    public CaResponse createCAPdf(CaRequest caRequest)  throws Exception {

        //1. 调用天威电子合同创建用户接口，获取userId
        String userId = this.getItrusContractUserId(caRequest);

        //2. 调用天威电子合同创建印章接口，获取autographId
        String autographId = this.getExistItrusContractAutographId(caRequest, userId);

        if(StringUtils.isBlank(autographId)){
            autographId = createItrusContractAutographId(caRequest, userId);
        }

        //TODO 从参数配置中获取enterpriseId和managerId
        String enterpriseId = null;
        String managerId = null;
        //3. 创建天威电子合同，获取合同相关信息(contractId,)
        Map<String, String> map = this.createItruscontract(caRequest, enterpriseId, managerId);
        String docId = map.get("docId");
        String contractId = map.get("contractId");

        //4. 添加天威电子合同签署人信息
        Map<String, Object> map2 = this.addItruscontractSigner(caRequest, userId, docId, contractId);
        Long signerId = (Long)map2.get("signerId");
        Set<Integer> signControlIds = (Set<Integer>)map2.get("signControlIds");

        //5. 上传文件合同后台签署
        this.signItruscontract(signerId, signControlIds, contractId, docId, autographId);

        //6. 调用天威电子合同下载合同接口，获取盖章的协议文件流
        byte[] sealedPdfData = downloadItruscontract(contractId);
        CaResponse caResponse = new CaResponse();
        caResponse.setFile(sealedPdfData);
        return caResponse;
    }

    private String getItrusContractUserId(CaRequest caRequest) throws Exception {
        ItrusContractCreateUserRequest apiRequest = new ItrusContractCreateUserRequest();
        apiRequest.setType("1");
        apiRequest.setPhone(caRequest.getMobile_tel());
        apiRequest.setAuthentication("true");
        apiRequest.setIdCardType("0");
        apiRequest.setIdCardNum(caRequest.getId_no());
        apiRequest.setDisplayName(caRequest.getUser_name());
        RepresentWrapper<?> representResponse = super.getItrusContractUserId(apiRequest);
        ItrusContractCreateUserRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractCreateUserRepresent());
        return represent.getData().getUserId();
    }

    private String getExistItrusContractAutographId(CaRequest caRequest, String userId) throws Exception  {
        String autographId = "";
        ItrusContractStampListRequest apiRequest = new ItrusContractStampListRequest();
        apiRequest.setUserId(userId);
        apiRequest.setName(caRequest.getUser_name());
        RepresentWrapper<?> representResponse = super.getExistItrusContractAutographId(apiRequest);
        ItrusContractStampListRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractStampListRepresent());
        if(represent.getData().getTotalRecord() > 0){
            for (ItrusContractStampListRepresent.Stamp stamp : represent.getData().getList()) {
                if(StringUtils.isNotBlank(stamp.getId())){
                    autographId = stamp.getId();
                    break;
                }
            }
        }
        return autographId;
    }

    private String createItrusContractAutographId(CaRequest caRequest, String userId) throws Exception  {
        ItrusContractCreateAutographRequest apiRequest = new ItrusContractCreateAutographRequest();
        apiRequest.setUserId(userId);
        apiRequest.setAutographType("1");
        apiRequest.setAutographType(caRequest.getUser_name());
        RepresentWrapper<?> representResponse = super.createItrusContractAutographId(apiRequest);
        ItrusContractCreateAutographRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractCreateAutographRepresent());
        return represent.getData().getAutographId();
    }

    private Map<String, String> createItruscontract(CaRequest caRequest, String enterpriseId, String managerId) throws Exception  {
        ItrusContractCreateContractRequest apiRequest = new ItrusContractCreateContractRequest();
        apiRequest.setSignCount("1");
        apiRequest.setName(caRequest.getAgreement_name());
        apiRequest.setDocName(caRequest.getAgreement_name());
        apiRequest.setBase64(Base64Utils.encodeToString(caRequest.getFile()));
        apiRequest.setCreator(managerId);
        apiRequest.setEnterpriseId(enterpriseId);
        apiRequest.setWaterMarkOff("false");
        RepresentWrapper<?> representResponse = super.createItruscontract(apiRequest);
        ItrusContractCreateContractRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractCreateContractRepresent());
        Map<String, String> returnMap = new HashMap<>();
        returnMap.put("contractId", represent.getData().getContractId());
        returnMap.put("docId", represent.getData().getDocId());
        return returnMap;
    }

    private Map<String, Object> addItruscontractSigner(CaRequest caRequest, String userId, String docId, String contractId) throws Exception  {
        String signPos = caRequest.getSign_pos();
        JSONArray signPosJson = new JSONArray();
        try {
            if (StringUtils.isNotBlank(signPos)) {
                signPosJson = JSON.parseArray(signPos);
            }
        } catch (Exception e) {
            log.error("解析sign_pos失败", e);
            throw new BizException("-1", "解析json串失败!");
        }
        if(signPosJson ==null || signPosJson.isEmpty()){
            throw new BizException("-1", "未配置签名位置信息!");
        }
        List<ItrusContractAddSignerRequest.XySignControl> signLocationsList = new ArrayList<ItrusContractAddSignerRequest.XySignControl>();
        List<ItrusContractAddSignerRequest.KeywordSignControl> signKeywordsList = new ArrayList<ItrusContractAddSignerRequest.KeywordSignControl>();
        Set<Integer> signControlIds = new HashSet<Integer>();
        // 签名位置信息
        for (int i = 0; i < signPosJson.size(); i++) {
            String signtype = signPosJson.getJSONObject(i).getString("signtype");
            String kw = signPosJson.getJSONObject(i).getString("kw");//关键字
            String xOffset = signPosJson.getJSONObject(i).getString("xOffset");//x轴偏移量
            String yOffset = signPosJson.getJSONObject(i).getString("yOffset");//y轴偏移量
//            int height = signPosJson.getJSONObject(i).getIntValue("height");
//            int width = signPosJson.getJSONObject(i).getIntValue("width");
            String index = signPosJson.getJSONObject(i).getString("index");//第几页
            // 如果为1，则是位置定位，否则为关键字定位
            if (StringUtils.equals("1", signtype)) {
                // 2=按坐标签名
                // 页数，按坐标签章时不能为空；
                // 左侧的x坐标（单位：像素）；左侧的y坐标（单位：像素）；
                ItrusContractAddSignerRequest.XySignControl xySignControl = new ItrusContractAddSignerRequest.XySignControl();
                xySignControl.setId(i);
                xySignControl.setType("autograph");
                xySignControl.setPageNum(index);
                xySignControl.setX(xOffset);
                xySignControl.setY(yOffset);
                signLocationsList.add(xySignControl);
            } else {
                ItrusContractAddSignerRequest.KeywordSignControl keywordSignControl = new ItrusContractAddSignerRequest.KeywordSignControl();
                keywordSignControl.setId(i);
                keywordSignControl.setType("autograph");
                keywordSignControl.setPageNum(index);
                keywordSignControl.setOffsetX(xOffset);
                keywordSignControl.setOffsetY(yOffset);
                keywordSignControl.setKeyword(kw);
                signKeywordsList.add(keywordSignControl);
            }
            signControlIds.add(i);
        }
        List<ItrusContractAddSignerRequest.SignFile> signFilesList = new ArrayList<ItrusContractAddSignerRequest.SignFile>();
        ItrusContractAddSignerRequest.SignFile signFile = new ItrusContractAddSignerRequest.SignFile();
        signFile.setDocId(docId);
        if(!signLocationsList.isEmpty()){
            signFile.setXySignControls(signLocationsList);
        }
        if(!signKeywordsList.isEmpty()){
            signFile.setKeywordSignControls(signKeywordsList);
        }
        signFilesList.add(signFile);
        List<ItrusContractAddSignerRequest.Signer> signerList = new ArrayList<ItrusContractAddSignerRequest.Signer>();
        ItrusContractAddSignerRequest.Signer signer = new ItrusContractAddSignerRequest.Signer();
        signer.setUserId(userId);
        signer.setSignerType("1");
        signer.setSequence("1");
        signer.setSignFiles(signFilesList);
        signerList.add(signer);
        ItrusContractAddSignerRequest apiRequest = new ItrusContractAddSignerRequest();
        apiRequest.setContractId(contractId);
        apiRequest.setSigners(signerList);
        RepresentWrapper<?> representResponse = super.addItruscontractSigner(apiRequest);
        ItrusContractAddSignerRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractAddSignerRepresent());
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("signerId", represent.getData().getCurrSignerIds().get(0));
        returnMap.put("signControlIds", signControlIds);
        return returnMap;
    }

    private void signItruscontract(Long signerId, Set<Integer> signControlIds, String contractId, String docId, String autographId) throws Exception  {
        ItrusContractSignRequest apiRequest = new ItrusContractSignRequest();
        apiRequest.setContractId(contractId);
        ItrusContractSignRequest.Signer signer = new ItrusContractSignRequest.Signer();
        signer.setSignerId(signerId);
        List<ItrusContractSignRequest.SignFile> signFilesList = new ArrayList<ItrusContractSignRequest.SignFile>();
        ItrusContractSignRequest.SignFile signFile = new ItrusContractSignRequest.SignFile();
        signFile.setDocId(docId);
        Map<String, String> signControl = new HashMap<String, String>();
        Iterator<Integer> iterator = signControlIds.iterator();
        while(iterator.hasNext()){
            signControl.put(iterator.next() +"", "sealId:" + autographId);
        }
        signFile.setSignControl(signControl);
        signFilesList.add(signFile);
        signer.setSignFiles(signFilesList);
        apiRequest.setSigner(signer);
        RepresentWrapper<?> representResponse = super.signItruscontract(apiRequest);
        ItrusContractSignRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractSignRepresent());
    }

    private byte[] downloadItruscontract(String contractId) throws Exception  {
        ItrusContractDownloadContractRequest apiRequest = new ItrusContractDownloadContractRequest();
        apiRequest.setContractId(contractId);
        RepresentWrapper<?> representResponse = super.downloadItruscontract(apiRequest);
        ItrusContractDownloadContractRepresent represent = converterApplyFactory.convert(representResponse, new ItrusContractDownloadContractRepresent());
        return represent.getData().getData();
    }
}
