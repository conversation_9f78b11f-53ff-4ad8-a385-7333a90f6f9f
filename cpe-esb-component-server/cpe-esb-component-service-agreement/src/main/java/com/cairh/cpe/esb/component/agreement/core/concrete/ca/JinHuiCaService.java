package com.cairh.cpe.esb.component.agreement.core.concrete.ca;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.agreement.core.CaService;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.JinHuiCreateSealRepresent;
import com.cairh.cpe.esb.component.agreement.core.standard.ca.JinHuiPersonOpenAccountRepresent;
import com.cairh.cpe.esb.component.agreement.dto.req.CaRequest;
import com.cairh.cpe.esb.component.agreement.dto.resp.CaResponse;
import com.cairh.cpe.esb.component.agreement.handler.ca.JinHuiCaServiceDecorator;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.file.utils.DateUtil;
import com.cairh.cpe.http.data.component.jinhui.req.JinHuiCreateSealRequest;
import com.cairh.cpe.http.data.component.jinhui.req.JinHuiPersonOpenAccountRequest;
import com.cairh.cpe.protocol.gateway.handler.component.jinhui.JinHuiSignContractRequest;
import com.cairh.cpe.util.json.FastJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class JinHuiCaService extends JinHuiCaServiceDecorator implements CaService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Override
    public CaResponse createCAPdf(CaRequest caRequest)  throws Exception {

        //1. 调用金汇个人开户接口，获取金汇系统userId
        String userId = this.getJinHuiUserId(caRequest);
        caRequest.setCa_user_id(userId);

        //2. 调用金汇增加印章接口，获取金汇系统sealId
        String sealId = this.getJinHuiSealId(caRequest);
        caRequest.setCa_seal_id(sealId);

        //3. 调用金汇合同签署接口，获取盖章的协议文件流（只管签名区域（个人），不用管签章（机构））
        byte[] sealedPdfData = locationSign(caRequest);
        CaResponse caResponse = new CaResponse();
        caResponse.setFile(sealedPdfData);
        return caResponse;
    }

    private String getJinHuiUserId(CaRequest caRequest) throws Exception {
        JinHuiPersonOpenAccountRequest apiRequest = new JinHuiPersonOpenAccountRequest();
        apiRequest.setPersonName(caRequest.getUser_name());
        apiRequest.setIdentTypeCode(caRequest.getId_kind());
        apiRequest.setIdentNo(caRequest.getId_no());
        apiRequest.setCertStartDate(caRequest.getId_begindate());
        apiRequest.setCertExpiryDate(caRequest.getId_enddate());
        apiRequest.setMobilePhone(caRequest.getMobile_tel());
        apiRequest.setEmail(caRequest.getE_mail());
        apiRequest.setAuthenticationMode(caRequest.getAuth_type());
        apiRequest.setAuthenticationTime(DateUtil.format(new Date(),DateUtil.DATE_TIME_FORMAT_NO_DELIMITER));
        apiRequest.getTrxDevcInf().setIp_address(caRequest.getIp_address());
        apiRequest.getTrxDevcInf().setMac_address(caRequest.getMac_address());
        apiRequest.getTrxDevcInf().setTerminal_way(caRequest.getTerminal_way());
        RepresentWrapper<?> representResponse = super.personOpenAccount(apiRequest);
        JinHuiPersonOpenAccountRepresent represent = converterApplyFactory.convert(representResponse, new JinHuiPersonOpenAccountRepresent());
        return represent.getData().getUserId();
        //TODO url需要拼接，需要找雷智杰确认框架是否满足
//        String fullUrl = url + "/signature/personOpenAccount";
//        Map<String, String> signCodeMap = Appkey.generateSigncode(new HashMap<String, String>(), appkey, secret);
//        StringBuilder sbBuffer = new StringBuilder(fullUrl);
//        sbBuffer.append("?");
//        for (Map.Entry<String, String> entry : signCodeMap.entrySet()) {
//            String mapKey = entry.getKey();
//            String mapValue = entry.getValue();
//            sbBuffer.append(mapKey).append("=").append(mapValue).append("&");
//        }
//        String reqUrl = sbBuffer.toString();
//        reqUrl = reqUrl.substring(0, reqUrl.length() - 1);
//        log.info("金汇平台请求{}->:{}", reqUrl, DebugHelper.filterField(params));
//
//        OkHttpClient okHttpClient = getUnsafeOkHttpClient();
//        ByteString body = ByteString.encodeUtf8(JSON.toJSONString(params));
//        RequestBody requestBody = RequestBody.create(body, MediaType.get("application/json"));
//        Request request = new Request.Builder()
//                .url(reqUrl)
//                .post(requestBody)
//                .addHeader("Content-Type", "application/json")
//                .build();
//        Response response = okHttpClient.newCall(request).execute();
//        String response_str = response.body().string();
//        JSONObject messageJsonObj = (JSONObject) JSON.parseObject(response_str).get("message");
//        if(messageJsonObj.getInteger("code") == 0 ){
//            JSONObject dataJsonObj = (JSONObject) JSON.parseObject(response_str).get("data");
//            return dataJsonObj.getString("userId");
//        }else{
//            throw new BizException("-1", "调用金汇接口[个人开户]失败，接口返回:" + messageJsonObj.get("message"));
//        }
    }

    private String getJinHuiSealId(CaRequest caRequest) throws Exception  {
        JinHuiCreateSealRequest apiRequest = new JinHuiCreateSealRequest();
        apiRequest.setSealMessage(caRequest.getUser_name());
        apiRequest.setCenterName(caRequest.getUser_name());
        apiRequest.setUserId(caRequest.getCa_user_id());
        apiRequest.setColor("0");
        apiRequest.setType("1");
        apiRequest.setSealStyle("1");
        RepresentWrapper<?> representResponse = super.sealAdd(apiRequest);
        JinHuiCreateSealRepresent represent = converterApplyFactory.convert(representResponse, new JinHuiCreateSealRepresent());
        return represent.getData().getSealId();

//
//        String fullUrl = url + "/seal/add";
//        Map<String, String> signCodeMap = Appkey.generateSigncode(new HashMap<String, String>(), appkey, secret);
//        StringBuilder sbBuffer = new StringBuilder(fullUrl);
//        sbBuffer.append("?");
//        for (Map.Entry<String, String> entry : signCodeMap.entrySet()) {
//            String mapKey = entry.getKey();
//            String mapValue = entry.getValue();
//            sbBuffer.append(mapKey).append("=").append(mapValue).append("&");
//        }
//        String reqUrl = sbBuffer.toString();
//        reqUrl = reqUrl.substring(0, reqUrl.length() - 1);
//        log.info("金汇平台请求{}->:{}", reqUrl, DebugHelper.filterField(params));
//
//        OkHttpClient okHttpClient = getUnsafeOkHttpClient();
//        ByteString body = ByteString.encodeUtf8(JSON.toJSONString(params));
//        RequestBody requestBody = RequestBody.create(body, MediaType.get("application/json"));
//        Request request = new Request.Builder()
//                .url(reqUrl)
//                .post(requestBody)
//                .addHeader("Content-Type", "application/json")
//                .build();
//        Response response;
//        response = okHttpClient.newCall(request).execute();
//        String response_str = response.body().string();
//        JSONObject messageJsonObj = (JSONObject) JSON.parseObject(response_str).get("message");
//        if(messageJsonObj.getInteger("code") == 0 ){
//            JSONObject dataJsonObj = (JSONObject) JSON.parseObject(response_str).get("data");
//            return dataJsonObj.getString("sealId");
//        }else{
//            throw new BizException("-1", "调用金汇接口[增加印章]失败，接口返回:" + messageJsonObj.get("message"));
//        }
    }

    //上传协议文件并申请签章，返回字节流
    private byte[] locationSign(CaRequest caRequest) throws Exception {
        JinHuiSignContractRequest apiRequest = new JinHuiSignContractRequest();
        byte[] file = caRequest.getFile();
        String signPos = caRequest.getSign_pos();
        JSONArray signPosJson = new JSONArray();
        try {
            if (StringUtils.isNotBlank(signPos)) {
                signPosJson = JSON.parseArray(signPos);
            }
        } catch (Exception e) {
            log.error("解析sign_pos失败", e);
            throw new BizException("-1", "解析json串失败!");
        }
        if(signPosJson ==null || signPosJson.isEmpty()){
            throw new BizException("-1", "未配置签名位置信息!");
        }
        List<JinHuiSignContractRequest.SignInfo> list = new ArrayList<JinHuiSignContractRequest.SignInfo>();
        JinHuiSignContractRequest.SignInfo signInfo = new JinHuiSignContractRequest.SignInfo();
        signInfo.setUserId(caRequest.getCa_user_id());
        signInfo.setSealId(caRequest.getCa_seal_id());
        List<JinHuiSignContractRequest.SignLocation> signLocationsList = new ArrayList<JinHuiSignContractRequest.SignLocation>();
        List<JinHuiSignContractRequest.SignKeyword> signKeywordsList = new ArrayList<JinHuiSignContractRequest.SignKeyword>();
        // 签名位置信息
        for (int i = 0; i < signPosJson.size(); i++) {
            String signtype = signPosJson.getJSONObject(i).getString("signtype");
            String kw = signPosJson.getJSONObject(i).getString("kw");//关键字
            String xOffset = signPosJson.getJSONObject(i).getString("xOffset");//x轴偏移量
            String yOffset = signPosJson.getJSONObject(i).getString("yOffset");//y轴偏移量
//            int height = signPosJson.getJSONObject(i).getIntValue("height");
//            int width = signPosJson.getJSONObject(i).getIntValue("width");
            String index = signPosJson.getJSONObject(i).getString("index");//第几页
            // 如果为1，则是位置定位，否则为关键字定位
            if (StringUtils.equals("1", signtype)) {
                // 2=按坐标签名
                // 页数，按坐标签章时不能为空；
                // 左侧的x坐标（单位：像素）；左侧的y坐标（单位：像素）；
                JinHuiSignContractRequest.SignLocation signLocation = new JinHuiSignContractRequest.SignLocation();
                signLocation.setSignOnPage(index);
                signLocation.setSignLocationLBX(xOffset);
                signLocation.setSignLocationLBY(yOffset);
                signLocationsList.add(signLocation);
            } else {
                JinHuiSignContractRequest.SignKeyword signKeyword = new JinHuiSignContractRequest.SignKeyword();
                signKeyword.setKeyword(kw);
                signKeyword.setPageNo(index);
                signKeyword.setOffsetCoordX(xOffset);
                signKeyword.setOffsetCoordY(yOffset);
                signKeywordsList.add(signKeyword);
            }
        }
        signInfo.setSignKeywords(signKeywordsList);
        signInfo.setSignLocations(signLocationsList);
        list.add(signInfo);
        apiRequest.setSignInfos(list);
        Map<String, String> params = new HashMap<>();
        params.put("signInfos", FastJsonUtil.toJSONString(apiRequest));
        RepresentWrapper<?> representResponse = super.signContract(apiRequest, file);
        return converterApplyFactory.convert(representResponse, new byte[0]);

//
//        String fullUrl = url + "/signature/signContract";
//        Map<String, String> signCodeMap = Appkey.generateSigncode(params, appkey, secret);
//        signCodeMap.put("signInfos", FastJsonUtil.toJSONString(signInfosMap));
//        StringBuilder sbBuffer = new StringBuilder(fullUrl);
//        sbBuffer.append("?");
//        for (Map.Entry<String, String> entry : signCodeMap.entrySet()) {
//            String mapKey = entry.getKey();
//            String mapValue = entry.getValue();
//            sbBuffer.append(mapKey).append("=").append(mapValue).append("&");
//        }
//        String reqUrl = sbBuffer.toString();
//        reqUrl = reqUrl.substring(0, reqUrl.length() - 1);
//        log.info("金汇平台请求{}->:{}", reqUrl, DebugHelper.filterField(params));
//
//        OkHttpClient okHttpClient = getUnsafeOkHttpClient();
//        RequestBody requestBody = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("uploadFile", UUID.randomUUID() + ".jpg", RequestBody.create(MediaType.parse("application/octet-stream"),file))
//                .addFormDataPart("signInfos", null,RequestBody.create(MediaType.parse("application/json"), FastJsonUtil.toJSONString(signInfosMap).getBytes()))
//                .build();
//        Request request = new Request.Builder()
//                .url(reqUrl)
//                .post(requestBody)
//                .build();
//        Response response;
//        response = okHttpClient.newCall(request).execute();
//        if(response.code() == 200 ){
//            return response.body().bytes();
//        }else{
//            throw new BizException("-1", "调用金汇接口[合同签署]失败");
//        }
    }
}
