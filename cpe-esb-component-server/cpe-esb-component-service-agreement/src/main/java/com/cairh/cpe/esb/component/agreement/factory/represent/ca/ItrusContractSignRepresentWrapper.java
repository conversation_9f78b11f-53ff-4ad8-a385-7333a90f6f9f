package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractSignRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class ItrusContractSignRepresentWrapper extends AbstractRepresentWrapper<ItrusContractSignRepresent> {

    public ItrusContractSignRepresentWrapper(ItrusContractSignRepresent source) {
        super(source);
    }
}
