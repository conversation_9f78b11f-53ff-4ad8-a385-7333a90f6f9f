package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractDownloadContractRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class ItrusContractDownloadContractRepresentWrapper extends AbstractRepresentWrapper<ItrusContractDownloadContractRepresent> {

    public ItrusContractDownloadContractRepresentWrapper(ItrusContractDownloadContractRepresent source) {
        super(source);
    }
}
