package com.cairh.cpe.esb.component.agreement.factory.represent.ca;

import com.cairh.cpe.esb.component.agreement.core.standard.ca.ItrusContractStampListRepresent;
import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;

public class ItrusContractStampListRepresentWrapper extends AbstractRepresentWrapper<ItrusContractStampListRepresent> {

    public ItrusContractStampListRepresentWrapper(ItrusContractStampListRepresent source) {
        super(source);
    }
}
