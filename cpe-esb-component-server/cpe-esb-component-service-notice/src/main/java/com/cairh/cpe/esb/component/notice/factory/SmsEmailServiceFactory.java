package com.cairh.cpe.esb.component.notice.factory;

import com.cairh.cpe.esb.component.core.factory.BaseServiceFactory;
import com.cairh.cpe.esb.component.notice.core.SmsService;

import java.util.function.Supplier;

/**
 * SMS email implementation of {@link BaseServiceFactory}, simply delegate getService operation
 * to getSmsEmailService
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface SmsEmailServiceFactory extends SmsServiceFactory {

    @Override
    default SmsService getSmsService(Supplier<String> serviceNameSupplier) {
        return getSmsEmailService(serviceNameSupplier);
    }

    SmsService getSmsEmailService(Supplier<String> serviceNameSupplier);
}
