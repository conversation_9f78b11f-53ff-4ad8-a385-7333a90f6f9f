package com.cairh.cpe.esb.component.notice.converter.voice;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;
import com.cairh.cpe.http.data.component.bigthreadchinese.req.BigThreadChineseSmsRequest;
import org.springframework.stereotype.Component;

/**
 * {@link NoticeSendSmsRequest} to {@link BigThreadChineseSmsRequest}
 *
 * <AUTHOR>
 */
@Component
public class NoticeSendSmsRequestToBigThreadChineseSmsRequestConverter implements Converter<NoticeSendSmsRequest, BigThreadChineseSmsRequest> {

    @Override
    public BigThreadChineseSmsRequest convert(NoticeSendSmsRequest source, BigThreadChineseSmsRequest target) {
        target.setMobile(source.getMobile_tel());
        target.setContent(source.getMsg_content());

        return target;
    }
}
