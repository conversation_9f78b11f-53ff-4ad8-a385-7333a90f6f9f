package com.cairh.cpe.esb.component.notice.core.concrete.message;

import cn.hutool.core.map.MapUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.constant.Fields;
import com.cairh.cpe.esb.component.core.util.CompositePropertySourcesUtil;
import com.cairh.cpe.esb.component.notice.core.SmsMessageService;
import com.cairh.cpe.esb.component.notice.core.data.mapper.DbSmsMapper;
import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * db implementation of {@link SmsMessageService}
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DbSmsMessageService implements SmsMessageService {

    @Autowired
    private DbSmsMapper mapper;


    @Override
    public Supplier<Boolean> messageSend(NoticeSendSmsRequest request) {
        boolean apiResponse = sendFakeSms(request);
        return () -> apiResponse;
    }

    private boolean sendFakeSms(NoticeSendSmsRequest request) {
        String sql = buildSendSmsRequest(request);
        log.info("准备执行数据库短信sql:{}", sql);
        try {
            mapper.exeInsert(sql);
        } catch (Exception e) {
            log.error("数据库短信sql执行失败", e);
            throw new BizException(ErrorCode.ERR_SYSERROR, "系统异常,数据库短信sql执行异常");
        }
        return true;
    }

    protected String buildSendSmsRequest(NoticeSendSmsRequest request) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(Fields.BRANCH_NO, " ");
        params.put(Fields.MOBILE, request.getMobile_tel());
        params.put(Fields.CONTENT, request.getMsg_content());
        return getStatementString(params);
    }

    /**
     * 获取sql模板
     */
    public String getSqlMapTemplate() {
        String sms_db_sql = CompositePropertySourcesUtil.getIfBlank(ConfigConst.DB_SMS_SQL_TEMPLATE_CONF, "请配置sql模板");
        return sms_db_sql.replace("‘", "'").replace("’", "'");
    }

    /**
     * 获取执行语句
     */
    public String getStatementString(Map<String, Object> parameter) {
        String template = getSqlMapTemplate();
        if (MapUtil.isNotEmpty(parameter)) {
            for (Map.Entry<String, Object> entry : parameter.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof String) {
                    template = template.replace("${" + key + "}", "'" + String.valueOf(value) + "'");
                    template = template.replace("#{" + key + "}", "'" + String.valueOf(value) + "'");
                } else {
                    template = template.replace("${" + key + "}", String.valueOf(value));
                    template = template.replace("#{" + key + "}", String.valueOf(value));
                }
            }
        }
        return template;
    }
}
