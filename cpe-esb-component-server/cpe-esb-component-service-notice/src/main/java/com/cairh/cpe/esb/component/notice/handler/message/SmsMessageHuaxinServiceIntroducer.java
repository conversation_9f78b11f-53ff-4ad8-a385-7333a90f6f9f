package com.cairh.cpe.esb.component.notice.handler.message;

import com.cairh.cpe.esb.component.core.config.SimplexVerdicter;
import com.cairh.cpe.esb.component.notice.core.constant.VerdictConstant;
import com.cairh.cpe.http.data.component.huaxin.ComponentHuaxinHttpService;
import com.cairh.cpe.http.data.component.huaxin.req.HuaXinSendMsgRequest;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.function.Supplier;

/**
 * an association with cpe-counter sms message huaxin api, perform as a bridge for connecting it to native
 *
 * <AUTHOR>
 */
public abstract class SmsMessageHuaxinServiceIntroducer {

    @Autowired
    private ComponentHuaxinHttpService httpService;

    @Resource(name = VerdictConstant.HUAXIN_SMS_MESSAGE)
    private SimplexVerdicter simplexVerdicter;


    public Supplier<Boolean> sendMsg(HuaXinSendMsgRequest apiRequest) {
        String apiResponse = httpService.sendMsg(apiRequest);
        return simplexVerdicter.verdict(apiResponse);
    }
}
