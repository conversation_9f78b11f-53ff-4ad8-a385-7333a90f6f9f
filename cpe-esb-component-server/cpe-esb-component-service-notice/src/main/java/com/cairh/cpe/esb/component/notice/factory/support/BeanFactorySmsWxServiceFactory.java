package com.cairh.cpe.esb.component.notice.factory.support;

import com.cairh.cpe.esb.component.notice.core.SmsService;
import com.cairh.cpe.esb.component.notice.factory.SmsWxServiceFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import java.util.function.Supplier;

/**
 * default implementation of {@link SmsWxServiceFactory} which use {@link BeanFactory} for
 * detecting service
 *
 * <AUTHOR>
 */
@Service
@Primary
public class BeanFactorySmsWxServiceFactory extends WebApplicationObjectSupport implements SmsWxServiceFactory {

    @Override
    public SmsService getSmsWxService(Supplier<String> serviceNameSupplier) {
        return getWebApplicationContext().getBean(serviceNameSupplier.get(), SmsService.class);
    }
}
