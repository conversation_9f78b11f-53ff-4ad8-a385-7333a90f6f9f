//package com.cairh.cpe.esb.component.notice.core.verdict.message;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.cairh.cpe.esb.component.core.config.SimplexVerdicter;
//import com.cairh.cpe.esb.component.notice.core.constant.VerdictConstant;
//import com.cairh.cpe.protocol.gateway.request.htsec.esb.dto.resp.Ws290002Resp;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.util.Assert;
//
//import java.util.function.Supplier;
//
//@Slf4j
//@Component(VerdictConstant.ESB_SMS_MESSAGE)
//public class EsbSmsMessageSimplexVerdicter implements SimplexVerdicter {
//
//    @Override
//    public Supplier<Boolean> verdict(Object httpApiResponse) {
//        Ws290002Resp ws290002Resp = (Ws290002Resp) httpApiResponse;
//        log.info("esb试试短信response:{}", ws290002Resp);
//        Assert.notNull(ws290002Resp.getBody(), "短信发送失败,请求三方没有返回body");
//        Assert.notNull(ws290002Resp.getBody().getRequestResponse(), "短信发送失败,请求三方没有返回response");
//        Assert.notNull(ws290002Resp.getBody().getRequestResponse().getResponseBody(), "短信发送失败,请求三方没有返回responsebody");
//        Ws290002Resp.MessageResponseBody responseBody = ws290002Resp.getBody().getRequestResponse().getResponseBody();
//        return () -> ObjectUtil.equal(responseBody.getReturnCode(), "0");
//    }
//}
