package com.cairh.cpe.esb.component.notice.core;

import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;

import java.util.function.Supplier;

/**
 * specific message implementation of {@link SmsService}
 *
 * <AUTHOR>
 */
public interface SmsMessageService extends SmsService {

    @Override
    default Supplier<Boolean> sendMessage(NoticeSendSmsRequest request) {
        return messageSend(request);
    }

    Supplier<Boolean> messageSend(NoticeSendSmsRequest request);
}
