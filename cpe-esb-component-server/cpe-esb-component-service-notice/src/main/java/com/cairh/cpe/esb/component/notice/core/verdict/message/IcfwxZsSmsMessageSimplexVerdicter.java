package com.cairh.cpe.esb.component.notice.core.verdict.message;

import cn.hutool.core.util.ObjectUtil;
import com.cairh.cpe.esb.component.core.config.SimplexVerdicter;
import com.cairh.cpe.esb.component.notice.core.constant.VerdictConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Supplier;
@Slf4j
@Component(VerdictConstant.ICFWXZS_SMS_MESSAGE)
public class IcfwxZsSmsMessageSimplexVerdicter implements SimplexVerdicter {
    @Override
    public Supplier<Boolean> verdict(Object httpApiResponse) {
        log.info("财富无线（浙商证券）短信响应结果httpApiResponse:{}", httpApiResponse);
        Map<String,String> resMap = (Map<String, String>) httpApiResponse;
        String flag = resMap.get("error_no");
        return () -> ObjectUtil.equal(flag, "0");
    }
}
