package com.cairh.cpe.esb.component.archive.service.third.factory.represent;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.http.data.component.shine.resp.ShiNeTokenResponse;

public class ShiNeGetTokenRepresentWrapper extends AbstractRepresentWrapper<ShiNeTokenResponse> {

    public ShiNeGetTokenRepresentWrapper(ShiNeTokenResponse source) {
        super(source);
    }
}
