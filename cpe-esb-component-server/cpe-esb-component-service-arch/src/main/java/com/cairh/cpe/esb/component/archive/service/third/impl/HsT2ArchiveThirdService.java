package com.cairh.cpe.esb.component.archive.service.third.impl;

import com.cairh.cpe.component.common.constant.ErrorConstant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.archive.service.third.IArchiveThirdService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectArchiveThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElecQueryClientToThirdResp;
import com.cairh.cpe.esb.component.file.utils.DateUtil;
import com.cairh.cpe.t2.data.f1x.dto.req.T2_480000_Request;
import com.cairh.cpe.t2.data.f1x.dto.req.T2_703024_Request;
import com.cairh.cpe.t2.data.f1x.dto.resp.T2_480000_Response;
import com.cairh.cpe.t2.data.f1x.service.T2CounterFunctionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 恒生T2三方文件归档实现类
 *
 * <AUTHOR>
 * @since 2022-12-2
 */
@Lazy
@Service
@Slf4j
public class HsT2ArchiveThirdService implements IArchiveThirdService {

    @Autowired
    private T2CounterFunctionService t2CounterFunctionService;

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Override
    public void archiveThirdFile(ElectArchiveThirdFileRequest request) {

        String operator_no = compositePropertySources.getProperty("comp.arch.audit.operator", "");
        String op_station = compositePropertySources.getProperty("comp.arch.operator.station", "212121212");
        String op_station_prefix = compositePropertySources.getProperty("comp.arch.station.prefix", "CRH");
        log.info("恒生档案参数comp.arch.audit.operator={}, comp.arch.operator.station={}, comp.arch.station.prefix={}", operator_no, op_station, op_station_prefix);
        // 调柜台归档功能号
        String nowDate = DateUtil.format(new Date(), "yyyyMMdd");
        int d = Integer.parseInt(nowDate) - 7;
        StringBuffer sb = new StringBuffer();
        sb.append(" with rpt_scansubtaskjour ");
        sb.append(" as (select m.create_date,m.curr_time,m.scantask_id,m.scansubtask_id from hs_arch.scansubtaskjour m ");
        sb.append(" where m.create_date >= " + d + " and m.business_flag = 7010 and (m.op_station like '%" + op_station + "%' or m.op_station like '%"
                + op_station_prefix + "%') ");
        sb.append("  union all");
        sb.append(" select n.create_date,n.curr_time,n.scantask_id,n.scansubtask_id from hs_arch.scansubtaskjour n ");
        sb.append(" where n.create_date >= " + d + " and  n.business_flag = 7010 and (n.op_station like '%" + op_station + "%' or n.op_station like '%"
                + op_station_prefix + "%'))");
        sb.append(" select count(distinct a.scansubtask_id) as allcount,");
        sb.append("  count(distinct c.scansubtask_id) as xpecount");
        sb.append(" from hs_arch.scansubtask a");
        sb.append(" left join (select c.*,row_number() over(partition by c.scantask_id,c.scansubtask_id order by c.create_date asc,c.curr_time asc) rn_asc");
        sb.append(" from rpt_scansubtaskjour c");
        sb.append(" where 1 = 1) c on a.scantask_id = c.scantask_id and a.scansubtask_id = c.scansubtask_id and c.rn_asc = 1");
        sb.append(" where a.create_date >= " + d + " and a.scantask_id  = '" + request.getScantask_id() + "'");
        T2_480000_Request t2_480000_Request = new T2_480000_Request();
        t2_480000_Request.setAction_in("0");
        t2_480000_Request.setSSql(sb.toString());
        t2_480000_Request.setMenu_id("4215");
        List<T2_480000_Response> list = t2CounterFunctionService.T2_480000(t2_480000_Request);
        boolean flag = true;
        for (T2_480000_Response result : list) {
            String allCount = result.getAllcount();
            String xpeCount = result.getXpecount();
            if (!StringUtils.equals(allCount, xpeCount)) {
                flag = false;
                break;
            }
        }
        if (flag) {
            T2_703024_Request t2_703024_Request = new T2_703024_Request();
            t2_703024_Request.setScantask_id(request.getScantask_id());
            t2_703024_Request.setCheck_remark("");
            t2_703024_Request.setCheck_flag("1");
            t2_703024_Request.setTask_flag("2");
            t2_703024_Request.setRandom_flag("0");
            t2_703024_Request.setOperator_no(operator_no);
            t2CounterFunctionService.T2_703024(t2_703024_Request);
        } else {
            throw new BizException(ErrorConstant.ARCHIVE_THIRD_FILE_FAIL, "scantask_id为" + request.getScantask_id() + "不满足归档条件，不归档");
        }
    }

    @Override
    public ElecQueryClientToThirdResp queryClientArchiveThirdFile(ElectArchiveThirdFileRequest request) {
        return null;
    }
}