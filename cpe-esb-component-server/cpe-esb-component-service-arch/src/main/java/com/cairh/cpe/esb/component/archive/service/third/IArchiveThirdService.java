package com.cairh.cpe.esb.component.archive.service.third;

import com.cairh.cpe.esb.component.elect.dto.req.ElectArchiveThirdFileRequest;

public interface IArchiveThirdService {

    /**
     * 第三方系统归档文件
     * @param request
     * @return
     */
    void archiveThirdFile(ElectArchiveThirdFileRequest request);


    /**
     * 查询客户基本档案、业务档案（ECIMC.040221）
     * @param request
     * @return
     */
    ElecQueryClientToThirdResp queryClientArchiveThirdFile(ElectArchiveThirdFileRequest request);
}
