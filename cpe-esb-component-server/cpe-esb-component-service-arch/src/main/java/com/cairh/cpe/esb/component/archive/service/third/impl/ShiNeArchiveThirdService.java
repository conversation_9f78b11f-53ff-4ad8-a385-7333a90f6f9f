package com.cairh.cpe.esb.component.archive.service.third.impl;

import com.alibaba.fastjson.JSONArray;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.convert.ConverterApplyFactory;
import com.cairh.cpe.esb.component.archive.service.third.IArchiveThirdService;
import com.cairh.cpe.esb.component.archive.service.third.handler.ShiNeServiceDecorator;
import com.cairh.cpe.esb.component.archive.service.third.standard.ShiNeSubmitProcScanInfoRepresent;
import com.cairh.cpe.esb.component.core.factory.RepresentWrapper;
import com.cairh.cpe.esb.component.elect.dto.req.ElectArchiveThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElecQueryClientToThirdResp;
import com.cairh.cpe.esb.component.elect.dto.resp.QueryClientThirdResult;
import com.cairh.cpe.esb.component.file.utils.DateUtil;
import com.cairh.cpe.protocol.gateway.handler.component.shine.ShiNeSubmitProcScanInfoRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新意三方文件归档实现类
 *
 * <AUTHOR>
 * @since 2022-12-2
 */
@Lazy
@Service
@Slf4j
public class ShiNeArchiveThirdService extends ShiNeServiceDecorator implements IArchiveThirdService {

    @Autowired
    private ConverterApplyFactory converterApplyFactory;

    @Override
    public void archiveThirdFile(ElectArchiveThirdFileRequest request) {
        ShiNeSubmitProcScanInfoRequest shiNeSubmitProcScanInfoRequest = new ShiNeSubmitProcScanInfoRequest();
        shiNeSubmitProcScanInfoRequest.setSrc_order_no(request.getBusi_serial_no());
        shiNeSubmitProcScanInfoRequest.setCust_prop(request.getOrgan_flag());
        shiNeSubmitProcScanInfoRequest.setCust_code(request.getClient_id());
        shiNeSubmitProcScanInfoRequest.setCust_name(request.getUser_name());
        List<String> list = new ArrayList<>();
        list.add(request.getBusi_code());
        Map<Object,Object> map = new HashMap();
        map.put(request.getBusi_code(),list);
        shiNeSubmitProcScanInfoRequest.setBusi_code(map);
        shiNeSubmitProcScanInfoRequest.setCert_type(request.getId_kind());
        shiNeSubmitProcScanInfoRequest.setCert_code(request.getId_no());
        shiNeSubmitProcScanInfoRequest.setDep_code(request.getBranch_no());
        shiNeSubmitProcScanInfoRequest.setSource_nos(request.getSource_nos());
        shiNeSubmitProcScanInfoRequest.setOpr_date(DateUtil.getCurrentTime(DateUtil.DATE_TIME_FORMAT_NO_DELIMITER));
        //事中影像归档接口(ECIMC.020521)
        shiNeSubmitProcScanInfoRequest.setMethod_id("020521");
        if(StringUtils.isNotBlank(request.getC_busi_code())) {
            //事中影像归档接口(ECIMC.020524)
            shiNeSubmitProcScanInfoRequest.setMethod_id("020524");
            shiNeSubmitProcScanInfoRequest.setC_busi_code(request.getC_busi_code());
        }
        if(StringUtils.isBlank(request.getSync())){
            shiNeSubmitProcScanInfoRequest.setSync("0");
        }else{
            shiNeSubmitProcScanInfoRequest.setSync(request.getSync());
        }
        shiNeSubmitProcScanInfoRequest.setKhfs(request.getOpen_type());
        shiNeSubmitProcScanInfoRequest.setOp_user_code(request.getOp_user_code());
        shiNeSubmitProcScanInfoRequest.setHide_flag(request.getHide_flag());
        shiNeSubmitProcScanInfoRequest.setEams_data(new JSONObject());
        shiNeSubmitProcScanInfoRequest.setStage("9");
        RepresentWrapper<?> representResponse = super.submitProcScanInfo(shiNeSubmitProcScanInfoRequest);
        ShiNeSubmitProcScanInfoRepresent shiNeSubmitProcScanInfoRepresent = converterApplyFactory.convert(representResponse, new ShiNeSubmitProcScanInfoRepresent());
        if(!StringUtils.equals(shiNeSubmitProcScanInfoRepresent.getRetcode(), "0")){
            throw new BizException(shiNeSubmitProcScanInfoRepresent.getRetcode(), shiNeSubmitProcScanInfoRepresent.getRetdesc());
        }
    }

    @Override
    public ElecQueryClientToThirdResp queryClientArchiveThirdFile(ElectArchiveThirdFileRequest request) {
        ShiNeSubmitProcScanInfoRequest shiNeSubmitProcScanInfoRequest = new ShiNeSubmitProcScanInfoRequest();
        shiNeSubmitProcScanInfoRequest.setSrc_order_no(request.getBusi_serial_no());
        shiNeSubmitProcScanInfoRequest.setCust_prop(request.getOrgan_flag());
        shiNeSubmitProcScanInfoRequest.setCust_code(request.getClient_id());
        shiNeSubmitProcScanInfoRequest.setCust_name(request.getUser_name());
        List<String> list = new ArrayList<>();
        list.add(request.getBusi_code());
        Map<Object,Object> map = new HashMap();
        map.put(request.getBusi_code(),list);
        shiNeSubmitProcScanInfoRequest.setBusi_code(map);
        shiNeSubmitProcScanInfoRequest.setCert_type(request.getId_kind());
        shiNeSubmitProcScanInfoRequest.setCert_code(request.getId_no());
        shiNeSubmitProcScanInfoRequest.setDep_code(request.getBranch_no());
        shiNeSubmitProcScanInfoRequest.setSource_nos(request.getSource_nos());
        shiNeSubmitProcScanInfoRequest.setOpr_date(DateUtil.getCurrentTime(DateUtil.DATE_TIME_FORMAT_NO_DELIMITER));
        //查询客户基本档案、业务档案(ECIMC.040221)
        shiNeSubmitProcScanInfoRequest.setMethod_id("040221");
        shiNeSubmitProcScanInfoRequest.setKhfs(request.getOpen_type());
        shiNeSubmitProcScanInfoRequest.setOp_user_code(request.getOp_user_code());
        shiNeSubmitProcScanInfoRequest.setHide_flag(request.getHide_flag());
        shiNeSubmitProcScanInfoRequest.setEams_data(new JSONObject());
        shiNeSubmitProcScanInfoRequest.setStage("9");
        RepresentWrapper<?> representResponse = super.submitProcScanInfo(shiNeSubmitProcScanInfoRequest);
        ShiNeSubmitProcScanInfoRepresent shiNeSubmitProcScanInfoRepresent = converterApplyFactory.convert(representResponse, new ShiNeSubmitProcScanInfoRepresent());
        if(!StringUtils.equals(shiNeSubmitProcScanInfoRepresent.getRetcode(), "0")){
            throw new BizException(shiNeSubmitProcScanInfoRepresent.getRetcode(), shiNeSubmitProcScanInfoRepresent.getRetdesc());
        }
        // 结果处理
        String retvalue = shiNeSubmitProcScanInfoRepresent.getRetvalue();
        ElecQueryClientToThirdResp elecQueryClientToThirdResp = new ElecQueryClientToThirdResp();
        if(StringUtils.isNotBlank(retvalue)){
            // 转成 json 数组
            JSONArray jsonArray = JSONArray.parseArray(retvalue);
            List<QueryClientThirdResult> queryClientThirdResultList = new ArrayList<>();
            for(int i = 0; i < jsonArray.size(); i++) {
                QueryClientThirdResult queryClientThirdResult = new QueryClientThirdResult();
                queryClientThirdResult.setLine_no(jsonArray.getJSONObject(i).getString("line_no"));
                queryClientThirdResult.setBusin_name(jsonArray.getJSONObject(i).getString("busin_name"));
                queryClientThirdResult.setFather_no(jsonArray.getJSONObject(i).getString("father_no"));
                queryClientThirdResult.setFileid(String.valueOf(jsonArray.getJSONObject(i).getInteger("fileid")));
                queryClientThirdResult.setSource_no(String.valueOf(jsonArray.getJSONObject(i).getInteger("source_no")));
                queryClientThirdResult.setLev(String.valueOf(jsonArray.getJSONObject(i).getInteger("lev")));
                queryClientThirdResultList.add(queryClientThirdResult);
            }
            elecQueryClientToThirdResp.setQueryClientThirdResultList(queryClientThirdResultList);
        }
        return elecQueryClientToThirdResp;
    }
}