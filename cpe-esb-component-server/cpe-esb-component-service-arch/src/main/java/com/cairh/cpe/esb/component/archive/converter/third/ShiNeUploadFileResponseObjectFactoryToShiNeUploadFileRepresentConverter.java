package com.cairh.cpe.esb.component.archive.converter.third;

import com.cairh.cpe.context.convert.Converter;
import com.cairh.cpe.esb.component.archive.service.third.factory.represent.ShiNeUploadFileRepresentWrapper;
import com.cairh.cpe.esb.component.archive.service.third.standard.ShiNeUploadFileRepresent;
import com.cairh.cpe.http.data.component.shine.resp.ShiNeUploadFileResponse;
import org.springframework.stereotype.Component;

@Component
public class ShiNeUploadFileResponseObjectFactoryToShiNeUploadFileRepresentConverter implements Converter<ShiNeUploadFileRepresentWrapper, ShiNeUploadFileRepresent> {

    @Override
    public ShiNeUploadFileRepresent convert(ShiNeUploadFileRepresentWrapper source, ShiNeUploadFileRepresent target) {
        ShiNeUploadFileResponse shiNeUploadFileResponse = source.getObject();
        target.setRetcode(shiNeUploadFileResponse.getRetcode());
        target.setFile_id(shiNeUploadFileResponse.getFile_id());
        target.setRetdesc(shiNeUploadFileResponse.getRetdesc());
        target.setRetvalue(shiNeUploadFileResponse.getRetvalue());
        target.setSign_type(shiNeUploadFileResponse.getSign_type());
        target.setTotal_md5(shiNeUploadFileResponse.getTotal_md5());
        return target;
    }
}