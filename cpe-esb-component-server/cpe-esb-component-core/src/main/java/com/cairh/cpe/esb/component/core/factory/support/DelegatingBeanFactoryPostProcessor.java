package com.cairh.cpe.esb.component.core.factory.support;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import lombok.SneakyThrows;
import org.springframework.aop.framework.ProxyFactoryBean;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionDefaults;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.DefaultBeanNameGenerator;
import org.springframework.context.annotation.AnnotationConfigUtils;
import org.springframework.context.annotation.ScannedGenericBeanDefinition;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

/**
 * post processor for proxy delegating
 *
 * <AUTHOR>
 */
@Component
public class DelegatingBeanFactoryPostProcessor implements BeanDefinitionRegistryPostProcessor {

    private final ResourcePatternResolver resourcePatternResolver;


    public DelegatingBeanFactoryPostProcessor() {
        resourcePatternResolver = new PathMatchingResourcePatternResolver(ClassUtils.getDefaultClassLoader());
    }

    @SneakyThrows
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        String delegatingPattern = ResourceLoader.CLASSPATH_URL_PREFIX + ClassUtils.convertClassNameToResourcePath(ClassUtils.getQualifiedName(StandarizedDelegatingProxy.class)) + ClassUtils.CLASS_FILE_SUFFIX;
        Resource delegatingResource = resourcePatternResolver.getResource(delegatingPattern);
        MetadataReader delegatingMetadataReader = new CachingMetadataReaderFactory(resourcePatternResolver).getMetadataReader(delegatingResource);

        ScannedGenericBeanDefinition delegatingGenericBeanDefinition = new ScannedGenericBeanDefinition(delegatingMetadataReader);
        delegatingGenericBeanDefinition.setSource(delegatingResource);
        String delegatingBeanName = DefaultBeanNameGenerator.INSTANCE.generateBeanName(delegatingGenericBeanDefinition, registry);

        postProcessBeanDefinition(delegatingGenericBeanDefinition);
        AnnotationConfigUtils.processCommonDefinitionAnnotations(delegatingGenericBeanDefinition);

        delegatingGenericBeanDefinition.setInstanceSupplier(() -> {
            ProxyFactoryBean delegatingProxyFactoryBean = new ProxyFactoryBean();
            delegatingProxyFactoryBean.setTargetClass(StandarizedDelegatingProxy.class);
            return delegatingProxyFactoryBean;
        });
        BeanDefinitionHolder definitionHolder = new BeanDefinitionHolder(delegatingGenericBeanDefinition, delegatingBeanName);
        BeanDefinitionReaderUtils.registerBeanDefinition(definitionHolder, registry);
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
    }

    private void postProcessBeanDefinition(AbstractBeanDefinition beanDefinition) {
        beanDefinition.applyDefaults(new BeanDefinitionDefaults());
        beanDefinition.setAutowireCandidate(true);
    }
}
