package com.cairh.cpe.esb.component.core.util;

import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-08-11
 */
public class CompositePropertySourcesUtil {

    private static CompositePropertySources compositePropertySources;

    public static String get(String key, String defaultValue) {
        if (Objects.isNull(compositePropertySources)) {
            compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        }
        return compositePropertySources.getProperty(key, defaultValue);
    }

    public static String get(String key) {
        if (Objects.isNull(compositePropertySources)) {
            compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        }
        return compositePropertySources.getProperty(key);
    }

    public static String getIfBlank(String key, String errorMsg) {
        if (Objects.isNull(compositePropertySources)) {
            compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        }
        String property = compositePropertySources.getProperty(key);
        if (StringUtils.isBlank(property)) {
            throw new BizException(ErrorCode.ERR_SYSERROR, errorMsg);
        }
        return property;
    }

    public static int getInt(String key, int i) {
        if (Objects.isNull(compositePropertySources)) {
            compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);
        }
        String property = compositePropertySources.getProperty(key);
        return StringUtils.isBlank(property) ? i : Integer.valueOf(property);
    }
}