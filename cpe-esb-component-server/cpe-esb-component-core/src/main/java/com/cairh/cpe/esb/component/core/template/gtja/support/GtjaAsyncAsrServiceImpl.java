package com.cairh.cpe.esb.component.core.template.gtja.support;

import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.template.gtja.IGtjaAsyncAsrService;
import com.iflytek.ai.platform.api.core.entity.ApiResponse;
import com.iflytek.ai.platform.api.core.enumerate.ApiCodeEnum;
import com.iflytek.ai.platform.api.sdk.asr.client.IatApiClient;
import com.iflytek.ai.platform.api.sdk.asr.entity.OfflineIatRequest;
import com.iflytek.ai.platform.api.sdk.asr.entity.OfflineIatResponse;
import com.iflytek.ai.platform.api.sdk.common.entity.AppCredential;
import com.iflytek.ai.platform.api.sdk.common.utils.ApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 国泰君安离线语音识别
 *
 * <AUTHOR>
 * @since 2023/10/21 09:31
 */
@Slf4j
@Service
public class GtjaAsyncAsrServiceImpl implements IGtjaAsyncAsrService {

    @Autowired
    private CompositePropertySources compositePropertySources;

    @Override
    public OfflineIatResponse invoke(OfflineIatRequest offlineIatRequest) {
        String secretId = compositePropertySources.getProperty(ConfigConst.GTJA_VOICE_SECRET_ID);
        String secretKey = compositePropertySources.getProperty(ConfigConst.GTJA_VOICE_SECRET_KEY);
        String appId = compositePropertySources.getProperty(ConfigConst.GTJA_VOICE_APP_ID);
        String abilityId = compositePropertySources.getProperty(ConfigConst.GTJA_VOICE_ABILITY_ID);
        String url = compositePropertySources.getProperty(ConfigConst.GTJA_VOICE_URL);

        log.debug("secretId:[{}],secretKey:[{}],appId:[{}],abilityId:[{}],url:[{}]", secretId, secretKey, appId, abilityId, url);

        AppCredential credential = new AppCredential(secretId, secretKey, appId);
        IatApiClient iatApiClient = new IatApiClient(credential, abilityId, url);
        ApiResponse apiResponse = iatApiClient.recognizeInner(offlineIatRequest);
        if (apiResponse.getCode() != ApiCodeEnum.SUCCESS.code()) {
            throw new BizException("获取离线语音识别结果报错,错误码" + apiResponse.getCode() + "错误信息" + apiResponse.getMessage());
        }
        return ApiUtil.getResult(apiResponse, OfflineIatResponse.class);
    }

}
