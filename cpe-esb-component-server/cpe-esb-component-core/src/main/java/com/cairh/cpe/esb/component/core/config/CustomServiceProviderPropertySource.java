package com.cairh.cpe.esb.component.core.config;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.core.autoconfiure.env.AbstractSimplePropertySource;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.constant.Constant;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;

/**
 * service provider customized by service name through {@link RpcContext}, with a shape of
 * comp.**.service.provider, enjoy the highest priority of all
 *
 * <AUTHOR>
 */
@Service
public class CustomServiceProviderPropertySource extends AbstractSimplePropertySource implements Ordered {

    public CustomServiceProviderPropertySource() {
        super("custom-service-provider-propertysource");
    }

    @Override
    public Object getProperty(String name) {
        if (isComponentServerContext() && matchServiceProviderConfigPattern(name)) {
            return getServiceProvider();
        }

        return null;
    }

    private boolean isComponentServerContext() {
        RpcContext context = RpcContext.getContext();

        return ObjectUtil.isNotEmpty(context.getUrl());
    }

    private boolean matchServiceProviderConfigPattern(String name) {
        return ReUtil.isMatch(ConfigConst.COMP_SERVICE_PROVIDER_PATTERN, name);
    }

    private String getServiceProvider() {
        RpcContext context = RpcContext.getContext();

        Object[] arguments = context.getArguments();
        if (ArrayUtils.isNotEmpty(arguments)) {
            Object serviceProviderObject = ArrayUtil.get(arguments, 0);

            String serviceProvider = (String) ReflectUtil.getFieldValue(serviceProviderObject, Constant.SERVICE_VENDER);
            if (StrUtil.isNotBlank(serviceProvider)) {
                ReflectUtil.setFieldValue(serviceProviderObject, Constant.SERVICE_VENDER, null);
                return serviceProvider;
            }
        }

        return null;
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
