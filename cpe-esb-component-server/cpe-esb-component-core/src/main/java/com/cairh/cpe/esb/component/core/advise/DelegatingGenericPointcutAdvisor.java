package com.cairh.cpe.esb.component.core.advise;

import com.cairh.cpe.esb.component.core.config.StandarizedDelegatingProxy;
import org.springframework.aop.Advisor;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.AbstractGenericPointcutAdvisor;

/**
 * esb-component base implementation of {@link Advisor} for typed detecting, in conjunction with {@link StandarizedDelegatingProxy}
 *
 * <AUTHOR>
 */
public class DelegatingGenericPointcutAdvisor extends AbstractGenericPointcutAdvisor {

    @Override
    public Pointcut getPointcut() {
        return Pointcut.TRUE;
    }
}
