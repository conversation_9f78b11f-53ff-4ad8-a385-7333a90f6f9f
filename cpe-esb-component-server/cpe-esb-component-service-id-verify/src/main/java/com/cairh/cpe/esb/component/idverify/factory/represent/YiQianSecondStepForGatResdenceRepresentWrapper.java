package com.cairh.cpe.esb.component.idverify.factory.represent;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.core.standard.yiqian.YiQianSecondStepForGatResdenceRepresent;

public class YiQianSecondStepForGatResdenceRepresentWrapper extends AbstractRepresentWrapper<YiQianSecondStepForGatResdenceRepresent> {

    public YiQianSecondStepForGatResdenceRepresentWrapper(YiQianSecondStepForGatResdenceRepresent source) {
        super(source);
    }
}
