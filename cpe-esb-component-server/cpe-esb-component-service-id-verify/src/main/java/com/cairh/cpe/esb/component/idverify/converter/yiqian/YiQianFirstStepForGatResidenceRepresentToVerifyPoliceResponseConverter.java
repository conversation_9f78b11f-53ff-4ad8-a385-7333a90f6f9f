package com.cairh.cpe.esb.component.idverify.converter.yiqian;

import com.cairh.cpe.esb.component.core.standard.yiqian.YiQianFirstStepForGatResidenceRepresent;
import com.cairh.cpe.esb.component.idverify.factory.represent.YiQianFirstStepForGatResidenceRepresentWrapper;
import com.cairh.cpe.esb.component.idverify.dto.resp.yiqian.YQFirstResponse;
import com.cairh.cpe.esb.component.idverify.dto.resp.yiqian.YQFirstResponseData;
import org.springframework.stereotype.Component;
import com.cairh.cpe.context.convert.Converter;

/**
 * 第一步：身份证或居住证response转换：厂商--->组件
 */
@Component
public class YiQianFirstStepForGatResidenceRepresentToVerifyPoliceResponseConverter implements Converter<YiQianFirstStepForGatResidenceRepresentWrapper, YQFirstResponse> {
    @Override
    public YQFirstResponse convert(YiQianFirstStepForGatResidenceRepresentWrapper source, YQFirstResponse target) {
        YiQianFirstStepForGatResidenceRepresent yiQianRepresent = source.getObject();
        YQFirstResponse response = new YQFirstResponse();
        String errCode = yiQianRepresent.getErrCode();
        String errInfo = yiQianRepresent.getErrInfo();
        if(!"000000".equals(errCode)){
            response.setResultCode("GDHJ0100000001");
            response.setResultMsg(errInfo);
            YQFirstResponseData data = new YQFirstResponseData().setErrCode(errCode).setErrInfo(errInfo);
            response.setData(data);
            response.setError_no("0");
            response.setError_info("宜迁公安认证服务失败，失败原因："+errInfo);
            return response;
        }
        YiQianFirstStepForGatResidenceRepresent.RepresentReqData reqData = yiQianRepresent.getReqData();

        YQFirstResponseData data = new YQFirstResponseData();
        data.setRandom_data(reqData.getRandom_data());
        data.setStream_data(reqData.getStream_data());
        data.setTransid(reqData.getTrans_id());
        data.setWork_flow(reqData.getWork_flow());

        response.setData(data);
        response.setResultMsg(errInfo);
        response.setResultCode("GDHJ0100000000");
        return response;
    }
}