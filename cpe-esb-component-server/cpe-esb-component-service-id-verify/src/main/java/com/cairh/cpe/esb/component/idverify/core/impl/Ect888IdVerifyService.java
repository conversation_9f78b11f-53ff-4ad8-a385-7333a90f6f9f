package com.cairh.cpe.esb.component.idverify.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.core.constant.ConfigConst;
import com.cairh.cpe.esb.component.core.constant.Fields;
import com.cairh.cpe.esb.component.core.constant.StatusConstant;
import com.cairh.cpe.esb.component.core.util.CompositePropertySourcesUtil;
import com.cairh.cpe.esb.component.file.utils.MapUtil;
import com.cairh.cpe.esb.component.idverify.core.IdVerifyService;
import com.cairh.cpe.esb.component.idverify.dto.req.*;
import com.cairh.cpe.esb.component.idverify.dto.resp.*;
import com.cairh.cpe.http.data.component.ect888.ComponentEct888HttpService;
import com.cairh.cpe.protocol.gateway.handler.component.ect888.Ect888FaceRecognitionRequest;
import com.cairh.cpe.protocol.gateway.handler.component.ect888.Ect888GetResultRequest;
import com.cairh.cpe.protocol.gateway.handler.component.ect888.Ect888ModPwdRequest;
import com.cairh.cpe.protocol.gateway.handler.component.ect888.Ect888ModSessionSecretRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 证通公安
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Slf4j
@Service
@Lazy
public class Ect888IdVerifyService implements IdVerifyService {

    @Autowired
    private ComponentEct888HttpService componentEct888HttpService;

    @Override
    public VerifyPoliceResponse verifyPolice(VerifyPoliceRequest request) {
        throw new BizException("该厂商暂不支持身份信息认证");
    }

    @Override
    public VerifyMobileResp verifyMobile(VerifyMobileRequest request) {
        throw new BizException("该厂商暂不支持手机实名认证");
    }

    @Override
    public VerifyPoliceAllResp verifyPoliceAll(VerifyPoliceAllReq request) {
        log.info("证通全要素公安认证入参：{}", request);
        return getPersonalRealTimeVerifyPoliceAllResponse(request);
    }

    @Override
    public VerifyPassPortResp verifyPassPort(VerifyPassPortReq request) {
        throw new BizException("该厂商暂不支持出入境证件核查");
    }

    @Override
    public VerifyBankResponse verifyBankCard(VerifyBankRequest request) {
        return null;
    }

    protected VerifyPoliceAllResp getPersonalRealTimeVerifyPoliceAllResponse(VerifyPoliceAllReq request) {
        VerifyPoliceAllResp response = new VerifyPoliceAllResp();
        Ect888FaceRecognitionRequest ect888FaceRecognitionRequest = new Ect888FaceRecognitionRequest();
        ect888FaceRecognitionRequest.setCertseq(request.getId_no());
        String base64_image = request.getBase64_image();
        if (StringUtils.isNotBlank(base64_image) && base64_image.contains("data:image")) {
            base64_image = base64_image.substring(base64_image.indexOf(",") + 1);
        }
        ect888FaceRecognitionRequest.setVideopic(base64_image);
        ect888FaceRecognitionRequest.setUsernm(request.getFull_name());
        String recognition_result = componentEct888HttpService.faceRecognition(ect888FaceRecognitionRequest);
        log.info("证通公安全要素认证结果：{}", recognition_result);
        Map<String, Object> resMap2000101 = setResponseStrToMap(recognition_result);
        if (judgeResultIsFalsify(resMap2000101)) {
            log.info("证通公安认证需要初始化...");
            // 初始化参数
            call200006();
            call200002();
            recognition_result = componentEct888HttpService.faceRecognition(ect888FaceRecognitionRequest);
            log.info("证通公安全要素认证结果：{}", recognition_result);
            resMap2000101 = setResponseStrToMap(recognition_result);
        }
        String error_no = (String) resMap2000101.get(Fields.ERROR_NO);
        Map<String, Object> exInfo = new HashMap<>();
        if (StringUtils.equals(error_no, "0")) {
            List<Map<String, Object>> results = (List<Map<String, Object>>) resMap2000101.get("results");
            if (CollectionUtils.isEmpty(results)) {
                log.error("证通高清接口服务返回对象:{}", results);
                throw new BizException("-1", "未获取到证通高清接口服务返回");
            }
            String sysseqnb = (String) results.get(0).get("sysseqnb");
            if (StringUtils.isBlank(sysseqnb)) {
                log.error("证通高清接口服务返回流水号sysseqnb为:{}", sysseqnb);
                throw new BizException("-1", "未获取到证通高清接口服务返回流水号");
            }
            int requestCount = 0;
            try {
                Thread.sleep(200);
                while (requestCount < 30) {
                    log.info("证通公安调用2000102接口查看返回结果详情：{}", "第" + requestCount + "次");
                    Ect888GetResultRequest ect888GetResultRequest = new Ect888GetResultRequest();
                    ect888GetResultRequest.setSysseqnb(sysseqnb);
                    String resultInfo = componentEct888HttpService.getResultInfo(ect888GetResultRequest);
                    log.info("本次证通公安调用2000102接口查看返回结果详情respons：{}", resultInfo);
                    Map<String, Object> result_info_map = setResponseStrToMap(resultInfo);
                    if (isRecognitionOver(result_info_map)) {
                        List<Map<String, Object>> resultsList = (List<Map<String, Object>>) result_info_map.get("results");
                        if (CollectionUtils.isEmpty(resultsList)) {
                            log.error("调用证通全要素公安认证失败,返回结果:{}", resultsList);
                            throw new BizException("-1", "证通全要素公安认证失败");
                        }
                        exInfo.putAll(resultsList.get(0));
                        String respcd = (String) resultsList.get(0).get("respcd");
                        response.setResult_info((String) resultsList.get(0).get("respinfo"));
                        response.setScore((String) resultsList.get(0).get("mpssim"));
                        //高清人像比对配置菜单 算分是否为同一人
                        if (StringUtils.equals("1000", respcd) && isSamePerson((String) resultsList.get(0).get("mpssim"))) {
                            response.setStatus(StatusConstant.ID_VERIFY_RETURN_PASS);
                        } else {
                            response.setStatus(StatusConstant.ID_VERIFY_RETURN_NOT_PASS);
                        }
                        response.setExt_info(JSON.toJSONString(exInfo));
                        return response;
                    }
                    requestCount++;
                }
            } catch (Exception e) {
                log.error("调用证通全要素公安认证失败", e);
                throw new BizException("-1", "调用证通全要素公安认证失败");
            }
            if (Objects.isNull(response.getStatus())) {
                log.error("调用证通全要素公安认证失败,请求超时");
                throw new BizException("-1", "调用证通全要素公安认证失败");
            }
            response.setExt_info(JSON.toJSONString(exInfo));
            return response;
        } else {
            log.error("调用证通公安认证失败：" + MapUtil.transMapToString(resMap2000101));
            throw new BizException("-1", "证通高清人像全要素认证失败");

        }

    }

    private void call200002() {
        Ect888ModSessionSecretRequest ect888ModSessionSecretRequest = new Ect888ModSessionSecretRequest();
        log.info("证通公安全要素认证修改会话密钥操作入参：{]", ect888ModSessionSecretRequest);
        String result = componentEct888HttpService.modSessionSecret(ect888ModSessionSecretRequest);
        log.info("证通公安全要素认证修改会话密钥操作出参：{]", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        // 发送请求
        // 解析参数
        String error_no = (String) jsonObject.get("error_no");// error_no=0代表成功
        if (!error_no.equals("0")) {
            log.error("操作失败，失败原因：" + jsonObject.get("error_info"));
            throw new BizException("[" + error_no + "]" + (String) jsonObject.get("error_info"));
        } else {
            log.info("修改会话密钥操作成功！");
        }
    }

    private void call200006() {
        Ect888ModPwdRequest ect888ModPwdRequest = new Ect888ModPwdRequest();
        log.info("证通公安全要素公安认证修改密码操作入参：{]", ect888ModPwdRequest);
        String result = componentEct888HttpService.modPwd(ect888ModPwdRequest);
        log.info("证通公安全要素公安认证修改密码操作出参：{]", result);
        JSONObject passwdjsonObject = JSONObject.parseObject(result);
        String error_no = (String) passwdjsonObject.get("error_no");
        if (StringUtils.equals("0", error_no)) {
            log.info("修改密码操作成功！");
        } else {
            log.error("操作失败，失败原因：" + passwdjsonObject.get("error_info"));
            throw new BizException("[" + error_no + "]" + (String) passwdjsonObject.get("error_info"));
        }
    }

    private boolean isRecognitionOver(Map<String, Object> resultMap) throws Exception {
        String error_no = (String) resultMap.get("error_no");
        if ("0".equals(error_no)) {
            List<Map<String, Object>> results = (List<Map<String, Object>>) resultMap.get("results");
            if (results == null || results.size() == 0) {
                throw new BizException("未获取到证通高清接口服务返回[results]");
            }
            String status = (String) results.get(0).get("status");
            if (StringUtils.isBlank(status)) {
                throw new BizException("未获取到证通高清接口服务返回[sysseqnb]");
            }
            if ("00".equals(status) || "03".equals(status)) {
                return true;
            }
        }
        return false;
    }

    public boolean isSamePerson(String in_score) {
        double score = 0d;
        if (StringUtils.isNotBlank(in_score)) {
            score = Double.parseDouble(in_score);
        } else {
            return false;
        }
        String min_score = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_ID_VERIFY_ECT888_MIN_SCORE, "请配置证通公安高清人像全要素认证 分数最小值");
        String max_score = CompositePropertySourcesUtil.getIfBlank(ConfigConst.COMP_ID_VERIFY_ECT888_MAX_SCORE, "请配置证通公安高清人像全要素认证 分数最大值");
        if (score >= Double.valueOf(min_score) && score <= Double.valueOf(max_score)) {
            return true;

        }
        return false;
    }

    /**
     * 将json串转map
     *
     * @param responseStr
     * @return
     */
    public Map<String, Object> setResponseStrToMap(String responseStr) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        JSONObject jsonobject = JSONObject.parseObject(responseStr);
        for (String key : jsonobject.keySet()) {
            resultMap.put(key, jsonobject.get(key));
        }
        return resultMap;
    }

    /**
     * 判断如果返回error_no=-200000，则表示未修改密码，需要修改密码
     *
     * @param resultMap
     * @return
     */
    public boolean judgeResultIsFalsify(Map<String, Object> resultMap) {
        String error_no = (String) resultMap.get("error_no");
        if (!StringUtils.isBlank(error_no) && "-2000000".equals(error_no)) {
            return true;
        }
        return false;
    }


}