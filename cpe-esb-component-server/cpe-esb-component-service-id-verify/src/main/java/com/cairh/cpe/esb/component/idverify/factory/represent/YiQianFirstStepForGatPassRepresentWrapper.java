package com.cairh.cpe.esb.component.idverify.factory.represent;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.core.standard.yiqian.YiQianFirstStepForGatPassRepresent;

public class YiQianFirstStepForGatPassRepresentWrapper extends AbstractRepresentWrapper<YiQianFirstStepForGatPassRepresent> {

    public YiQianFirstStepForGatPassRepresentWrapper(YiQianFirstStepForGatPassRepresent source) {
        super(source);
    }
}
