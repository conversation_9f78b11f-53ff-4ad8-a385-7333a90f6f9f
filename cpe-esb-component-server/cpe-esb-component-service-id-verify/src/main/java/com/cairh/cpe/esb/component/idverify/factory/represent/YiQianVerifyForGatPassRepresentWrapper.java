package com.cairh.cpe.esb.component.idverify.factory.represent;

import com.cairh.cpe.esb.component.core.factory.support.AbstractRepresentWrapper;
import com.cairh.cpe.esb.component.core.standard.yiqian.YiQianFirstStepForGatResidenceRepresent;
import com.cairh.cpe.esb.component.core.standard.yiqian.YiQianVerifyForGatPassRepresent;
import org.springframework.stereotype.Component;

public class YiQianVerifyForGatPassRepresentWrapper extends AbstractRepresentWrapper<YiQianVerifyForGatPassRepresent> {
    public YiQianVerifyForGatPassRepresentWrapper(YiQianVerifyForGatPassRepresent source) {
        super(source);
    }
}