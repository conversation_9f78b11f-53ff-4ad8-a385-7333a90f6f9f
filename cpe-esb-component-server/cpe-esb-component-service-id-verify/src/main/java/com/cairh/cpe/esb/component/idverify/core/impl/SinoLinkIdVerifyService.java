package com.cairh.cpe.esb.component.idverify.core.impl;

import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.esb.component.core.constant.BusiConstant;
import com.cairh.cpe.esb.component.core.constant.StatusConstant;
import com.cairh.cpe.esb.component.idverify.core.IdVerifyService;
import com.cairh.cpe.esb.component.idverify.dto.req.*;
import com.cairh.cpe.esb.component.idverify.dto.resp.*;
import com.cairh.cpe.http.data.gjzq.yjb.YjbHttpService;
import com.cairh.cpe.http.data.gjzq.yjb.req.PoliceCheckRequest;
import com.cairh.cpe.http.data.gjzq.yjb.resp.PoliceCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 国金公安认证接口
 */
@Slf4j
@Service
public class SinoLinkIdVerifyService implements IdVerifyService {

    @Autowired
    private YjbHttpService yjbHttpService;

    @Override
    public VerifyPoliceResponse verifyPolice(VerifyPoliceRequest request) {
        PoliceCheckRequest policeCheckRequest = new PoliceCheckRequest();
        policeCheckRequest.setName(request.getFull_name());// 姓名
        policeCheckRequest.setIdNo(request.getId_no());// 身份证号码
        policeCheckRequest.setScene(2);// 业务场景(0:无头像要求,1:判断是否有头像,2:能获取到头像)
        PoliceCheckResponse policeCheckResponse = yjbHttpService.policeCheck(policeCheckRequest);

        if (policeCheckResponse == null) {
            throw new BizException(ErrorCode.ERR_SYSERROR, "国金公安认证返回空");
        }

        VerifyPoliceResponse response = new VerifyPoliceResponse();
        if (!BusiConstant.SINOLINK_POLICE_SUCCESS_CODE.equals(policeCheckResponse.getStatus())){
            response.setStatus(StatusConstant.ID_VERIFY_RETURN_NOT_PASS);

        } else {
            response.setUsreName(policeCheckResponse.getName());
            response.setId_no(policeCheckResponse.getIdNo());
            response.setBirthday(policeCheckResponse.getBirthDate());
            response.setStatus(StatusConstant.ID_VERIFY_RETURN_PASS);
            // 公安头像存在标识 1-存在 0-不存在
            if (BusiConstant.SINOLINK_POLICE_IMAGE_FLAG.equals(policeCheckResponse.getPoliceFileFlag())) {
                response.setImage_data(StatusConstant.NO_ID_PHOTO);
                response.setIs_police_photo(1); // 存在公安认证头像
            } else {
                response.setIs_police_photo(0); // 不存在公安认证头像
            }
        }

        response.setResult_info(policeCheckResponse.getMatchInfo());

        return response;
    }

    @Override
    public VerifyMobileResp verifyMobile(VerifyMobileRequest request) {
        throw null;
    }

    @Override
    public VerifyPoliceAllResp verifyPoliceAll(VerifyPoliceAllReq request) {
        throw null;
    }

    @Override
    public VerifyPassPortResp verifyPassPort(VerifyPassPortReq request) {
        return null;
    }

    @Override
    public VerifyBankResponse verifyBankCard(VerifyBankRequest request) {
        return null;
    }
}