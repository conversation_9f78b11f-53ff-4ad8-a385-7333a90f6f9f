package com.cairh.cpe.esb.component.idverify.core.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.component.archive.service.third.impl.ShiNeDownloadThirdService;
import com.cairh.cpe.esb.component.core.gtja.service.GtIwmTokenAuthenticateService;
import com.cairh.cpe.esb.component.core.gtja.service.support.IwmTokenResponse;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadThirdFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadThirdFileResponse;
import com.cairh.cpe.esb.component.idverify.core.IdVerifyService;
import com.cairh.cpe.esb.component.idverify.dto.req.*;
import com.cairh.cpe.esb.component.idverify.dto.resp.*;
import com.cairh.cpe.t2.data.fgt.dto.req.T2_11804254_Request;
import com.cairh.cpe.t2.data.fgt.dto.req.T2_11804280_Request;
import com.cairh.cpe.t2.data.fgt.dto.req.T2_11804446_Request;
import com.cairh.cpe.t2.data.fgt.dto.req.T2_11804447_Request;
import com.cairh.cpe.t2.data.fgt.dto.resp.T2_11804254_Response;
import com.cairh.cpe.t2.data.fgt.dto.resp.T2_11804280_Response;
import com.cairh.cpe.t2.data.fgt.dto.resp.T2_11804446_Response;
import com.cairh.cpe.t2.data.fgt.dto.resp.T2_11804447_Response;
import com.cairh.cpe.t2.data.fgt.service.T2GtFunctionService;
import com.cairh.cpe.util.crypt.Md5Encrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * 国泰君安公安认证
 *
 * <AUTHOR>
 * @since 2023/10/28 09:35
 */
@Slf4j
@Service
@Lazy
public class GtjaT2IdVerifyServiceImpl implements IdVerifyService {
    private static final String GTJA_T2_KEY = "gtjajzyy";
    @Autowired
    private T2GtFunctionService t2GtFunctionService;
    @Autowired
    private GtIwmTokenAuthenticateService iwmTokenAuthenticateService;
    @Autowired
    private ShiNeDownloadThirdService shiNeDownloadThirdService;
    @Value("${t2.gtja.sdk.branch_no:3306}")
    private String branch_no;

    @Override
    public VerifyPoliceResponse verifyPolice(VerifyPoliceRequest request)  {
        T2_11804280_Request t211804280Request = new T2_11804280_Request();
        BeanUtil.copyProperties(request, t211804280Request);

        IwmTokenResponse iwmTokenResponse = iwmTokenAuthenticateService.authToken();
        t211804280Request.setGt_iwm_id(iwmTokenResponse.getTokenSerialNo());
        t211804280Request.setUser_token(iwmTokenResponse.getUserToken());

        t211804280Request.setId(iwmTokenResponse.getId());
        t211804280Request.setId_string(iwmTokenResponse.getId());
        t211804280Request.setBranch_no(request.getBranch_no());
        t211804280Request.setFull_name(request.getFull_name());
        t211804280Request.setId_kind(request.getId_kind());
        t211804280Request.setId_no(request.getId_no());
        t211804280Request.setCsdc_busi_kind(request.getCsdc_busi_kind());
        t211804280Request.setAction_in("1");

        String md5Str = GTJA_T2_KEY + t211804280Request.getId_kind() +
                t211804280Request.getId_no() + StringUtils.defaultString(t211804280Request.getCsdc_busi_kind()) + t211804280Request.getAction_in();
        String md5 = Md5Encrypt.MD5(md5Str);
        t211804280Request.setGt_md5(md5);
        log.info("verifyPolice-T2_11804280请求参数：{}", JSON.toJSONString(t211804280Request));
        VerifyPoliceResponse verifyPoliceResponse = new VerifyPoliceResponse();
        for (int i = 0; i < 30; i++) {
            T2_11804280_Response t211804280Response = t2GtFunctionService.T2_11804280(t211804280Request);
            log.info("国泰君安公安认证返回第{}次结果:{}", i + 1, JSON.toJSONString(t211804280Response));

            verifyPoliceResponse.setId_no(t211804280Response.getId_no());
            verifyPoliceResponse.setUsreName(t211804280Response.getFull_name());
            verifyPoliceResponse.setResult_info(t211804280Response.getDeal_info());
            verifyPoliceResponse.setStatus("0");
            if (StringUtils.equals("-1", t211804280Response.getCode())) {
                return verifyPoliceResponse;
            }

            if (StringUtils.isNotBlank(t211804280Response.getFile_path())) {
                verifyPoliceResponse.setStatus("1");
                ElectDownloadThirdFileRequest electDownloadThirdFileRequest = new ElectDownloadThirdFileRequest();
                electDownloadThirdFileRequest.setFile_id(t211804280Response.getFile_path());
                ElectDownloadThirdFileResponse electDownloadThirdFileResponse = shiNeDownloadThirdService.downloadThirdFile(electDownloadThirdFileRequest);
                log.info("新意下载文件路径={},长度={}",t211804280Response.getFile_path(),electDownloadThirdFileResponse.getFile() ==null ? 0 :electDownloadThirdFileResponse.getFile().length);
                verifyPoliceResponse.setImage_data(Base64.getEncoder().encodeToString(electDownloadThirdFileResponse.getFile()));
                return verifyPoliceResponse;
            }

            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
                log.warn("国泰君安公安认证线程休眠失败");
            }
        }
        throw new BizException("-1", "查询超时！");
    }

    @Override
    public VerifyMobileResp verifyMobile(VerifyMobileRequest request) {
        return null;
    }

    @Override
    public VerifyPoliceAllResp verifyPoliceAll(VerifyPoliceAllReq request) {
        T2_11804254_Request t2_11804254_req = new T2_11804254_Request();
        BeanUtil.copyProperties(request, t2_11804254_req);

        IwmTokenResponse iwmTokenResponse = iwmTokenAuthenticateService.authToken();
        t2_11804254_req.setGt_iwm_id(iwmTokenResponse.getTokenSerialNo());
        t2_11804254_req.setUser_token(iwmTokenResponse.getUserToken());
        t2_11804254_req.setId(iwmTokenResponse.getId());
        String gt_md5 = Md5Encrypt.MD5(GTJA_T2_KEY + request.getId_no() + request.getFull_name() + request.getBase64_image() + "1");
        t2_11804254_req.setGt_md5(gt_md5);
        t2_11804254_req.setGt_sfzh(request.getId_no());
        t2_11804254_req.setGt_xm(request.getFull_name());
        t2_11804254_req.setGt_bdzp(request.getBase64_image());
        t2_11804254_req.setGt_cxlx("1");
        t2_11804254_req.setOp_entrust_way("3");
        log.info("verifyPoliceAll-T2_11804254_Request请求参数：{}", JSON.toJSONString(t2_11804254_req));
        VerifyPoliceAllResp verifyPoliceAllResp = new VerifyPoliceAllResp();
        for (int i = 0; i < 30; i++) {
            T2_11804254_Response response = t2GtFunctionService.T2_11804254(t2_11804254_req);
            log.info("国泰君安三要素查询证通分数第{}次结果：{}", i + 1, JSON.toJSONString(response));
            verifyPoliceAllResp.setResult_info(response.getDeal_info());
            verifyPoliceAllResp.setScore(response.getGt_bdfz());
            verifyPoliceAllResp.setStatus("0");
            if(StringUtils.equals(response.getGt_cljg(), "-1")) {
                return verifyPoliceAllResp;
            }
            if(StringUtils.equals(response.getGt_cljg(), "1")) {
                verifyPoliceAllResp.setStatus("1");
                return verifyPoliceAllResp;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
                log.error("国泰君安三要素查询证通分数休眠异常！");
            }
        }
        throw new BizException("-1", "查询超时！");
    }

    @Override
    public VerifyPassPortResp verifyPassPort(VerifyPassPortReq request) {
        log.info("verifyPassPort请求参数：{}", JSON.toJSONString(request));
        IwmTokenResponse iwmTokenResponse = iwmTokenAuthenticateService.authToken();
        T2_11804446_Request t2_11804446_req = new T2_11804446_Request();
        BeanUtil.copyProperties(request, t2_11804446_req);
        t2_11804446_req.setOperator_no(request.getOperator_no());
        t2_11804446_req.setGt_iwm_id(iwmTokenResponse.getTokenSerialNo());
        t2_11804446_req.setUser_token(iwmTokenResponse.getUserToken());
        t2_11804446_req.setId(iwmTokenResponse.getId());
        String gt_md5 = Md5Encrypt.MD5(GTJA_T2_KEY + request.getGt_cxlx() + request.getId_kind() + request.getId_no() + request.getArea_code());
        t2_11804446_req.setGt_md5(gt_md5);
        t2_11804446_req.setGt_hclb(request.getGt_hclb());
        t2_11804446_req.setGt_zjlb(request.getId_kind());
        t2_11804446_req.setGt_zjbh(request.getId_no());
        t2_11804446_req.setGt_zjqsrq(request.getId_begindate());
        t2_11804446_req.setGt_zjjzrq(request.getId_enddate());
        t2_11804446_req.setClient_name(request.getFull_name());
        t2_11804446_req.setGt_gjdm(request.getArea_code());
        t2_11804446_req.setGt_xb(request.getGender());
        t2_11804446_req.setGt_csrq(request.getBirthday());
        t2_11804446_req.setGt_khtx(request.getBase64_image());
        log.info("公民出入境证件信息核查请求参数：{}", JSON.toJSONString(t2_11804446_req));
        T2_11804446_Response t2_11804446_response = t2GtFunctionService.T_2_11804446(t2_11804446_req);
        log.info("公民出入境证件信息核查返回结果：{}", JSON.toJSONString(t2_11804446_response));

        String positionStr = t2_11804446_response.getPosition_str();
        T2_11804447_Request t2_11804447_Request = new T2_11804447_Request();
        t2_11804447_Request.setPosition_str(positionStr);
        t2_11804447_Request.setUser_token(iwmTokenResponse.getUserToken());
        t2_11804447_Request.setGt_iwm_id(iwmTokenResponse.getTokenSerialNo());
        t2_11804447_Request.setOperator_no(request.getOperator_no());
        gt_md5 = Md5Encrypt.MD5(GTJA_T2_KEY + positionStr);
        t2_11804447_Request.setGt_md5(gt_md5);
        log.info("查询公民出入境证件信息核查结果请求参数：{}", JSON.toJSONString(t2_11804447_Request));
        VerifyPassPortResp passPortResp = new VerifyPassPortResp();
        for (int i = 0; i < 30; i++) {
            T2_11804447_Response t2_11804447_Response = t2GtFunctionService.T_2_11804447(t2_11804447_Request);
            log.info("查询公民出入境证件信息核查第{}次结果：{}", i + 1, JSON.toJSONString(t2_11804447_Response));
            passPortResp.setStatus("0");
            passPortResp.setResult_info(t2_11804447_Response.getDeal_info());
            passPortResp.setIdverifyrecord_id(t2_11804447_Response.getId());
            if(StringUtils.equals(t2_11804447_Response.getGt_cljg(), "-1")) {
                return passPortResp;
            }
            if(StringUtils.equals(t2_11804447_Response.getGt_cljg(), "1")) {
                passPortResp.setStatus("1");
                return passPortResp;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
                log.error("查询公民出入境证件信息核查休眠异常！");
            }
        }
        throw new BizException("-1", "查询超时！");
    }

    @Override
    public VerifyBankResponse verifyBankCard(VerifyBankRequest request) {
        return null;
    }
}