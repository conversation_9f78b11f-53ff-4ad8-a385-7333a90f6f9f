package com.cairh.cpe.esb.component.front.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class JSONUtil {
    /**
     * 合并JSON对象，用source覆盖target，返回覆盖后的JSON对象，
     *
     * @param source JSONObject
     * @param target JSONObject
     * @return JSONObject
     */
    public static JSONObject jsonMerge(JSONObject source, JSONObject target) {
        // 覆盖目标JSON为空，直接返回覆盖源
        if (target == null) {
            return source;
        }

        if (source == null) {
            return target;
        }

        if (target == null && source == null) {
            return new JSONObject();
        }

        for (String key : source.keySet()) {
            Object value = source.get(key);
            if (!target.containsKey(key)) {
                target.put(key, value);
            } else {
                if (value instanceof JSONObject) {
                    JSONObject valueJson = (JSONObject) value;
                    JSONObject targetValue = jsonMerge(valueJson, target.getJSONObject(key));
                    target.put(key, targetValue);
                } else if (value instanceof JSONArray) {
                    JSONArray valueArray = (JSONArray) value;

                    for (int i = 0; i < valueArray.size(); i++) {
                        if (valueArray.get(i) instanceof JSONArray) {
                            JSONObject obj = (JSONObject) valueArray.get(i);
                            JSONObject targetValue = jsonMerge(obj, (JSONObject) target.getJSONArray(key).get(i));
                            target.getJSONArray(key).set(i, targetValue);
                        }

                        if (valueArray.get(i) instanceof JSONObject) {
                            JSONObject obj = (JSONObject) valueArray.get(i);
                            JSONObject targetValue = jsonMerge(obj, (JSONObject) target.getJSONArray(key).get(i));
                            target.getJSONArray(key).set(i, targetValue);
                        }

                        if (valueArray.get(i) instanceof String || valueArray.get(i) instanceof Integer) {
                            target.getJSONArray(key).set(i, valueArray.get(i));
                        }
                    }
                } else {
                    target.put(key, value);
                }
            }
        }
        return target;
    }

    public static String getJSONString(JSONObject jsonObject) {
        if (jsonObject != null) {
            return jsonObject.toJSONString();
        } else {
            return null;
        }
    }
}
