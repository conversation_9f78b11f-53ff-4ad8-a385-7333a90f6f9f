package com.cairh.cpe.esb.component.front.api.form.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EncodeQuery {

    private static final long serialVersionUID = 1L;

    private boolean user;

    private boolean comp;

    private boolean data;

    private boolean wskh;

    private boolean sbc;

    private boolean mall;

}
