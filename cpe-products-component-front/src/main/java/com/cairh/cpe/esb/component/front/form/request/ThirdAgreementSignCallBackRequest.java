package com.cairh.cpe.esb.component.front.form.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 三方协议签署完成回调请求类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ThirdAgreementSignCallBackRequest {
    private String request_id;
    // 标准签署order_no
    private String order_no;
    private List<FileInfo> file_infos;


    /**
     * 自定义签署
     * 协议模板和三方签署后文件id
     *
     * 自定义：一个
     * 批量：多个
     */
    @Data
    public static class FileInfo {
        private String model_id;
        private String file_id;
    }
}