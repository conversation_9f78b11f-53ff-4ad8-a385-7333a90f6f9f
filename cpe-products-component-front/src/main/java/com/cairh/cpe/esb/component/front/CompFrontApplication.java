package com.cairh.cpe.esb.component.front;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@EnableCaching
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@MapperScan("com.cairh.cpe.**.mapper")
@SpringBootApplication(scanBasePackages = "com.cairh.cpe",exclude = {GsonAutoConfiguration.class, FreeMarkerAutoConfiguration.class})
public class CompFrontApplication {

    public static void main(String[] args) {
        SpringApplication.run(CompFrontApplication.class, args);
    }
}