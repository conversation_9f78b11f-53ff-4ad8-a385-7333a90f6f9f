package com.cairh.cpe.esb.component.front.application.filter;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.esb.component.front.common.utils.RestLogFilterFieldUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 如果包含header参数，不做输出
 * 顺便 输出对应url 请求体，响应体，耗时
 */
//@WebFilter(urlPatterns = "/*", filterName = "logFilter")
@Slf4j
//@Configuration
public class LogFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

//        String mdc_logger_uuid = request.getParameter(Constants.MDC_LOGGER_UUID);
//        if (StringUtils.isBlank(mdc_logger_uuid)) {
//            mdc_logger_uuid = UUID.randomUUID().toString().replace("-", "").toUpperCase();
//        }
//        MDC.put(Constants.MDC_LOGGER_UUID, mdc_logger_uuid);
//        request.setAttribute(Constants.MDC_LOGGER_UUID, mdc_logger_uuid);
//
//        String mdc_logger_user_id = request.getParameter(Constants.MDC_LOGGER_USER_ID);
//        if (StringUtils.isBlank(mdc_logger_user_id)) {
//            mdc_logger_user_id = StringUtils.defaultIfBlank(request.getHeader("uid"), "");
//        }
//        if (StringUtils.isBlank(mdc_logger_user_id)) {
//            mdc_logger_user_id = StringUtils.defaultIfBlank(request.getParameter("uid"), "");
//        }
//        MDC.put(Constants.MDC_LOGGER_USER_ID, mdc_logger_user_id);
//        request.setAttribute(Constants.MDC_LOGGER_USER_ID, mdc_logger_user_id);
        long requestTime = new Date().getTime();
        String uri = request.getRequestURI();
        String contextPath = request.getContextPath();
        String url = uri.substring(contextPath.length());
        //静态资源 跳过
        if (url.contains(".")) {
            filterChain.doFilter(request, response);
            return;
        }
        //输出请求体
        String requestBody = "";
        String requestContentType = request.getHeader(HttpHeaders.CONTENT_TYPE);

        if (requestContentType != null) {
            //xml json
            if (requestContentType.startsWith(MediaType.APPLICATION_JSON_VALUE) || requestContentType.startsWith(MediaType.APPLICATION_XML_VALUE)) {
                requestBody = getRequestBody(request);
//                JSONObject jsonObject = JSON.parseObject(requestBody);
//                request.setAttribute(Fields.REQUEST_NO, jsonObject.getString(Fields.REQUEST_NO));
                final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody.getBytes(StandardCharsets.UTF_8));
                request = new HttpServletRequestWrapper(request) {
                    @Override
                    public ServletInputStream getInputStream() {
                        return new ByteArrayServletInputStream(byteArrayInputStream);
                    }
                };
                //普通表单提交
            } else if (requestContentType.startsWith(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                requestBody = toJson(request.getParameterMap());
                //文件表单提交
            } else if (requestContentType.startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                //multipart-formdata exist 1M limit problem, config multipart factory
                requestBody = getFormParam(request);
            }
        }
        log.info(String.format("->%s:%s", request.getRequestURI(), RestLogFilterFieldUtil.jsonFilterField(requestBody)));
        //logger.info(String.format("[%s]HttpRequest[%s:%s]:%s:%s", mdc_logger_user_id, request.getRemoteHost(), request.getRemotePort(), request.getRequestURI(), RestLogFilterFieldUtil.jsonFilterField(requestBody)));

        final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        response = new HttpServletResponseWrapper(response) {
            @Override
            public ServletOutputStream getOutputStream() throws IOException {
                return new TeeServletOutputStream(super.getOutputStream(), byteArrayOutputStream);
            }
        };

        filterChain.doFilter(request, response);

        long costTime = new Date().getTime() - requestTime;
        String responseBody = "";
        //暂定只有json 输出响应体
        String contentType = response.getHeader(HttpHeaders.CONTENT_TYPE);
        if (contentType != null && contentType.startsWith(MediaType.APPLICATION_JSON_VALUE)) {
            responseBody = byteArrayOutputStream.toString();
        }
        log.info(String.format("<-%s[%s]:%s", request.getRequestURI(), costTime, RestLogFilterFieldUtil.jsonFilterField(responseBody)));
        //logger.info(String.format("[%s]HttpResponse[%s][%s:%s]:%s:%s", mdc_logger_user_id, costTime, request.getRemoteHost(), request.getRemotePort(), request.getRequestURI(), RestLogFilterFieldUtil.jsonFilterField(responseBody)));
        MDC.clear();
    }

    private String getRequestBody(HttpServletRequest request) {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            return "";
        }
        try {
            return IOUtils.toString(request.getReader());
        } catch (IOException e) {
            log.error("获取请求体失败", e);
            return "";
        }
    }

    private String getFormParam(HttpServletRequest request) {
        MultipartResolver resolver = new StandardServletMultipartResolver();
        MultipartHttpServletRequest mRequest = resolver.resolveMultipart(request);

        Map<String, Object> param = new HashMap<>();
        Map<String, String[]> parameterMap = mRequest.getParameterMap();
        if (!parameterMap.isEmpty()) {
            param.putAll(parameterMap);
        }
        Map<String, MultipartFile> fileMap = mRequest.getFileMap();
        if (!fileMap.isEmpty()) {
            for (Map.Entry<String, MultipartFile> fileEntry : fileMap.entrySet()) {
                MultipartFile file = fileEntry.getValue();
                param.put(fileEntry.getKey(), file.getOriginalFilename() + "(" + file.getSize() + " byte)");
            }
        }
        return toJson(param);
    }

    private static String toJson(Object object) {
        return JSON.toJSONStringWithDateFormat(object, "yyyy-MM-dd HH:mm:ss");
    }

}