package com.cairh.cpe.esb.component.front.form.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务参数上传请求类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UploadBizParamRequest {

    @JSONField(serialize = false)
    private String request_id;
    private JSONObject base_data;
    private JSONObject render_data;
    private JSONObject regular_data;
    private JSONObject sign_seal_data;
    private JSONObject ex_param_data;
    private JSONObject update_data;
}


